{"name": "datatables.net", "description": "DataTables for jQuery ", "main": ["js/jquery.dataTables.js"], "keywords": ["filter", "sort", "DataTables", "j<PERSON><PERSON><PERSON>", "table", "DataTables"], "dependencies": {"jquery": ">=1.7"}, "moduleType": ["globals", "amd", "node"], "ignore": ["composer.json", "datatables.json", "package.json"], "authors": [{"name": "SpryMedia Ltd", "homepage": "https://datatables.net"}], "homepage": "https://datatables.net", "license": "MIT", "version": "1.10.16", "_release": "1.10.16", "_resolution": {"type": "version", "tag": "1.10.16", "commit": "7880f80fc056607386a9090a6c5f1e398211ae9a"}, "_source": "https://github.com/DataTables/Dist-DataTables.git", "_target": "^1.10.15", "_originalSource": "datatables.net"}