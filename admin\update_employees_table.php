<?php
require_once('includes/script.php');
require_once('session/ModelController.php');

$model = new Dashboard();
$connection = $model->TemporaryConnection();

// Disable foreign key checks
mysqli_query($connection, "SET FOREIGN_KEY_CHECKS = 0;");

// Drop temporary table if it exists
$drop_temp = "DROP TABLE IF EXISTS employees_temp;";
mysqli_query($connection, $drop_temp);

// Create temporary table with new structure
$create_temp_table = "CREATE TABLE `employees_temp` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` varchar(45) DEFAULT NULL,
  `position_id` int(11) DEFAULT NULL,
  `schedule_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `created_on` varchar(45) DEFAULT NULL,
  `photo` longtext DEFAULT NULL,
  `fullname` varchar(45) DEFAULT NULL,
  `address` varchar(45) DEFAULT NULL,
  `email` varchar(45) DEFAULT NULL,
  `phonenumber` varchar(45) DEFAULT NULL,
  `sex` varchar(45) DEFAULT NULL,
  `position` varchar(45) DEFAULT NULL,
  `emergency_name` varchar(45) DEFAULT NULL,
  `emergency_contact` varchar(45) DEFAULT NULL,
  `project_name` varchar(45) DEFAULT NULL,
  `site_location` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;";

// Copy data from old table to new table
$copy_data = "INSERT INTO employees_temp (
    id, employee_id, position_id, schedule_id, created_on, photo, 
    fullname, address, email, phonenumber, sex, position, 
    emergency_name, emergency_contact, project_name, site_location
) 
SELECT 
    id, employee_id, position_id, schedule_id, created_on, photo, 
    fullname, address, email, phonenumber, sex, position, 
    emergency_name, emergency_contact, project_name, site_location
FROM employees;";

// Drop old table
$drop_old = "DROP TABLE employees;";

// Rename new table
$rename_table = "RENAME TABLE employees_temp TO employees;";

try {
    // Execute the queries
    mysqli_query($connection, $create_temp_table);
    mysqli_query($connection, $copy_data);
    mysqli_query($connection, $drop_old);
    mysqli_query($connection, $rename_table);
    
    // Re-enable foreign key checks
    mysqli_query($connection, "SET FOREIGN_KEY_CHECKS = 1;");
    
    echo "Table structure updated successfully!";
} catch (Exception $e) {
    // Re-enable foreign key checks even if there's an error
    mysqli_query($connection, "SET FOREIGN_KEY_CHECKS = 1;");
    echo "Error updating table structure: " . $e->getMessage();
}
?> 