<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

class DepartmentAPI {
    private $baseUrl = 'http://196.189.151.125:8080/api/HRMAPI';
    
    /**
     * Get all departments from the API
     * 
     * @return array Array of departments
     */
    public function getDepartments() {
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->baseUrl . '/get_department');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $response = curl_exec($ch);
            
            if(curl_errno($ch)) {
                error_log("cURL Error: " . curl_error($ch));
                return [];
            }
            curl_close($ch);
        } else {
            // Fallback to file_get_contents
            $context = stream_context_create([
                'http' => [
                    'ignore_errors' => true,
                    'timeout' => 30
                ]
            ]);
            
            $response = @file_get_contents($this->baseUrl . '/get_department', false, $context);
            if ($response === false) {
                error_log("Error fetching departments using file_get_contents");
                return [];
            }
        }
        
        $data = json_decode($response, true);
        if (!$data || !isset($data['data'])) {
            error_log("Invalid API response: " . $response);
            return [];
        }
        
        return $data['data'];
    }
    
    /**
     * Sync departments with local database
     * 
     * @param mysqli $connection Database connection
     * @return array Result of sync operation
     */
    public function syncDepartments($connection) {
        try {
            // Get departments from API
            $departments = $this->getDepartments();
            
            // Begin transaction
            $connection->begin_transaction();
            
            // Clear existing departments
            $connection->query("TRUNCATE TABLE departments");
            
            // Insert new departments
            $stmt = $connection->prepare("INSERT INTO departments (department_name, department_code, parent_department, created_at) VALUES (?, ?, ?, NOW())");
            
            foreach ($departments as $dept) {
                $stmt->bind_param("ssi", 
                    $dept['department_name'],
                    $dept['department_code'],
                    $dept['parent_department']
                );
                $stmt->execute();
            }
            
            // Commit transaction
            $connection->commit();
            
            return [
                'success' => true,
                'message' => 'Departments synchronized successfully',
                'count' => count($departments)
            ];
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $connection->rollback();
            
            return [
                'success' => false,
                'message' => 'Failed to sync departments: ' . $e->getMessage()
            ];
        }
    }
}
?> 