<!DOCTYPE html>
<!--
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
-->
<html>
<head>
	<meta charset="utf-8">
	<title>Editing source code in a dialog &mdash; CKEditor Sample</title>
	<script src="../../../ckeditor.js"></script>
	<link rel="stylesheet" href="../../../samples/old/sample.css">
	<meta name="ckeditor-sample-name" content="Editing source code in a dialog">
	<meta name="ckeditor-sample-group" content="Plugins">
	<meta name="ckeditor-sample-description" content="Editing HTML content of both inline and classic editor instances.">
	<meta name="ckeditor-sample-isnew" content="1">
	<style>

		#editable
		{
			padding: 10px;
			float: left;
		}

	</style>
</head>
<body>
	<h1 class="samples">
		<a href="../../../samples/old/index.html">CKEditor Samples</a> &raquo; Editing source code in a dialog
	</h1>
	<div class="warning deprecated">
		This sample is not maintained anymore. Check out its <a href="http://sdk.ckeditor.com/samples/sourcearea.html">brand new version in CKEditor SDK</a>.
	</div>
	<div class="description">
		<p>
			<strong>Sourcedialog</strong> plugin provides an easy way to edit raw HTML content
			of an editor, similarly to what is possible with <strong>Sourcearea</strong>
			plugin for classic (<code>iframe</code>-based) instances but using dialogs. Thanks to that, it's also possible
			to manipulate raw content of inline editor instances.
		</p>
		<p>
			This plugin extends the toolbar with a button,
			which opens a dialog window with a source code editor. It works with both classic
			and inline instances. To enable this
			plugin, basically add <code>extraPlugins: 'sourcedialog'</code> to editor's
			config:
		</p>
<pre class="samples">
// Inline editor.
CKEDITOR.inline( 'editable', {
	<strong>extraPlugins: 'sourcedialog'</strong>
});

// Classic (iframe-based) editor.
CKEDITOR.replace( 'textarea_id', {
	<strong>extraPlugins: 'sourcedialog'</strong>,
	removePlugins: 'sourcearea'
});
</pre>
		<p>
			Note that you may want to include <code>removePlugins: 'sourcearea'</code>
			in your config when using <strong>Sourcedialog</strong> in classic editor instances.
			This prevents feature redundancy.
		</p>
		<p>
			Note that <code>editable</code> in the code above is the <code>id</code>
			attribute of the <code>&lt;div&gt;</code> element to be converted into an inline instance.
		</p>
		<p>
			Note that <code><em>textarea_id</em></code> in the code above is the <code>id</code> attribute of
			the <code>&lt;textarea&gt;</code> element to be replaced with CKEditor.
		</p>
	</div>
	<div>
		<label for="editor1">
			Inline editor:
		</label>
		<div id="editor1" contenteditable="true" style="padding: 5px 20px;">
			<p>This is some <strong>sample text</strong>. You are using <a href="http://ckeditor.com/">CKEditor</a>.</p>
		</div>
	</div>
	<br>
	<div>
		<label for="editor2">
			Classic editor:
		</label>
		<textarea cols="80" id="editor2" name="editor2" rows="10">
			This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.
		</textarea>
	</div>
	<script>

		// We need to turn off the automatic editor creation first.
		CKEDITOR.disableAutoInline = true;

		var config = {
			toolbarGroups: [
				{ name: 'mode' },
				{ name: 'basicstyles' },
				{ name: 'links' }
			],
			extraPlugins: 'sourcedialog',
			removePlugins: 'sourcearea'
		}

		CKEDITOR.inline( 'editor1', config );
		CKEDITOR.replace( 'editor2', config );

	</script>
	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">
				http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2017, <a class="samples" href="http://cksource.com/">CKSource</a>
			- Frederico Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
