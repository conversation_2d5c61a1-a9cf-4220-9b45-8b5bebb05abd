## Prerequisites

- [ ] I have searched for similar issues in both open and closed tickets and cannot find a duplicate
- [ ] The issue still exists against the latest `master` branch of Select2
- [ ] This is not a usage question (Those should be directed to the [community](https://select2.github.io/community.html))
- [ ] I have attempted to find the simplest possible steps to reproduce the issue
- [ ] I have included a failing test as a pull request (Optional)

## Steps to reproduce the issue

1.
2.
3.

## Expected behavior and actual behavior

When I follow those steps, I see...

I was expecting...

## Environment

Browsers

- [ ] Google Chrome
- [ ] Mozilla Firefox
- [ ] Internet Explorer

Operating System

- [ ] Windows
- [ ] Mac OS X
- [ ] Linux
- [ ] Mobile

Libraries

- jQuery version:
- Select2 version:

## Isolating the problem

- [ ] This bug happens [on the examples page](https://select2.github.io/examples.html)
- [ ] The bug happens consistently across all tested browsers
- [ ] This bug happens when using Select2 without other plugins
- [ ] I can reproduce this bug in [a jsbin](https://jsbin.com/)
