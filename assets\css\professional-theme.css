/* Professional Theme Enhancements */

/* ===== GLOBAL STYLES ===== */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #34495e;
  --accent-color: #3498db;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --light-bg: #f8f9fa;
  --dark-text: #2c3e50;
  --muted-text: #6c757d;
  --border-color: #dee2e6;
  --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
  --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

body {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f6fa;
  color: var(--dark-text);
  line-height: 1.6;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  color: var(--dark-text);
  margin-bottom: 1rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.page-subtitle {
  color: var(--muted-text);
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

/* ===== CARDS ===== */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  background: white;
}

.card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
  padding: 1.5rem;
  border-bottom: none;
}

.card-header h3 {
  color: white;
  margin-bottom: 0;
  font-weight: 600;
}

.card-body {
  padding: 2rem;
}

.card-footer {
  background-color: var(--light-bg);
  border-top: 1px solid var(--border-color);
  padding: 1rem 2rem;
}

/* ===== BUTTONS ===== */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: var(--transition);
  border: none;
  text-transform: none;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-color) 0%, #2980b9 100%);
  box-shadow: var(--shadow-light);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
  box-shadow: var(--shadow-medium);
  transform: translateY(-1px);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, #229954 100%);
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, #d68910 100%);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
}

.btn-outline-primary {
  border: 2px solid var(--accent-color);
  color: var(--accent-color);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--accent-color);
  color: white;
  transform: translateY(-1px);
}

/* ===== TABLES ===== */
.table {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.table thead th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
  font-weight: 600;
  color: var(--dark-text);
  padding: 1rem;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.table tbody tr {
  transition: var(--transition);
}

.table tbody tr:hover {
  background-color: #f8f9fa;
  transform: scale(1.01);
}

.table tbody td {
  padding: 1rem;
  border-top: 1px solid #f1f3f4;
  vertical-align: middle;
}

/* ===== FORMS ===== */
.form-control {
  border: 2px solid #e9ecef;
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  transition: var(--transition);
  font-size: 1rem;
}

.form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.input-group-text {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #e9ecef;
  color: var(--muted-text);
}

/* ===== NAVIGATION ===== */
.nav-tabs {
  border-bottom: 2px solid var(--border-color);
}

.nav-tabs .nav-link {
  border: none;
  color: var(--muted-text);
  font-weight: 500;
  padding: 1rem 1.5rem;
  transition: var(--transition);
}

.nav-tabs .nav-link:hover {
  color: var(--accent-color);
  background: transparent;
}

.nav-tabs .nav-link.active {
  color: var(--accent-color);
  background: transparent;
  border-bottom: 3px solid var(--accent-color);
}

/* ===== ALERTS ===== */
.alert {
  border: none;
  border-radius: var(--border-radius);
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid;
}

.alert-primary {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(52, 152, 219, 0.05) 100%);
  border-left-color: var(--accent-color);
  color: #1f5f8b;
}

.alert-success {
  background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(39, 174, 96, 0.05) 100%);
  border-left-color: var(--success-color);
  color: #186a3b;
}

.alert-warning {
  background: linear-gradient(135deg, rgba(243, 156, 18, 0.1) 0%, rgba(243, 156, 18, 0.05) 100%);
  border-left-color: var(--warning-color);
  color: #b7950b;
}

.alert-danger {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(231, 76, 60, 0.05) 100%);
  border-left-color: var(--danger-color);
  color: #a93226;
}

/* ===== PAGINATION ===== */
.pagination {
  margin: 0;
}

.page-link {
  border: none;
  color: var(--muted-text);
  padding: 0.75rem 1rem;
  margin: 0 0.25rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.page-link:hover {
  background: var(--accent-color);
  color: white;
  transform: translateY(-1px);
}

.page-item.active .page-link {
  background: var(--accent-color);
  border-color: var(--accent-color);
  box-shadow: var(--shadow-light);
}

/* ===== BADGES ===== */
.badge {
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
}

/* ===== DROPDOWNS ===== */
.dropdown-menu {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  padding: 0.5rem 0;
}

.dropdown-item {
  padding: 0.75rem 1.5rem;
  transition: var(--transition);
}

.dropdown-item:hover {
  background: linear-gradient(135deg, var(--accent-color) 0%, #2980b9 100%);
  color: white;
}

/* ===== STATISTICS CARDS ===== */
.stat-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  border-left: 4px solid var(--accent-color);
}

.stat-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-3px);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--muted-text);
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .card-body {
    padding: 1.5rem;
  }
  
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .table-responsive {
    border-radius: var(--border-radius);
  }
}

/* ===== LOADING STATES ===== */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* ===== CUSTOM UTILITIES ===== */
.text-gradient {
  background: linear-gradient(135deg, var(--accent-color) 0%, #2980b9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--accent-color) 0%, #2980b9 100%);
}

.shadow-custom {
  box-shadow: var(--shadow-medium);
}
