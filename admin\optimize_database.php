<?php
/**
 * Database Optimization Script
 * Adds indexes and optimizes database for better performance
 */

require_once('session/ModelController.php');

$model = new Dashboard();
$connection = $model->TemporaryConnection();

// Check if user is admin
session_start();
if(!isset($_SESSION['official_username'])) {
    die('Access denied');
}

$optimizations = [];
$errors = [];

// Function to add index if it doesn't exist
function addIndexIfNotExists($connection, $table, $indexName, $columns, &$optimizations, &$errors) {
    // Check if index exists
    $checkQuery = "SHOW INDEX FROM `$table` WHERE Key_name = '$indexName'";
    $result = mysqli_query($connection, $checkQuery);
    
    if (mysqli_num_rows($result) == 0) {
        // Index doesn't exist, create it
        $createQuery = "ALTER TABLE `$table` ADD INDEX `$indexName` ($columns)";
        if (mysqli_query($connection, $createQuery)) {
            $optimizations[] = "Added index '$indexName' to table '$table'";
        } else {
            $errors[] = "Failed to add index '$indexName' to table '$table': " . mysqli_error($connection);
        }
    } else {
        $optimizations[] = "Index '$indexName' already exists on table '$table'";
    }
}

// Function to optimize table
function optimizeTable($connection, $table, &$optimizations, &$errors) {
    $query = "OPTIMIZE TABLE `$table`";
    if (mysqli_query($connection, $query)) {
        $optimizations[] = "Optimized table '$table'";
    } else {
        $errors[] = "Failed to optimize table '$table': " . mysqli_error($connection);
    }
}

echo "<h2>Database Optimization Results</h2>";

// Optimize employees table
addIndexIfNotExists($connection, 'employees', 'idx_employee_id', '`employee_id`', $optimizations, $errors);
addIndexIfNotExists($connection, 'employees', 'idx_fullname', '`fullname`', $optimizations, $errors);
addIndexIfNotExists($connection, 'employees', 'idx_position_id', '`position_id`', $optimizations, $errors);
addIndexIfNotExists($connection, 'employees', 'idx_department_id', '`department_id`', $optimizations, $errors);
addIndexIfNotExists($connection, 'employees', 'idx_created_on', '`created_on`', $optimizations, $errors);

// Optimize attendance table
addIndexIfNotExists($connection, 'attendance', 'idx_employee_id', '`employee_id`', $optimizations, $errors);
addIndexIfNotExists($connection, 'attendance', 'idx_date', '`date`', $optimizations, $errors);
addIndexIfNotExists($connection, 'attendance', 'idx_employee_date', '`employee_id`, `date`', $optimizations, $errors);
addIndexIfNotExists($connection, 'attendance', 'idx_status_morning', '`status_morning`', $optimizations, $errors);
addIndexIfNotExists($connection, 'attendance', 'idx_time_in_morning', '`time_in_morning`', $optimizations, $errors);

// Optimize departments table
addIndexIfNotExists($connection, 'departments', 'idx_name', '`name`', $optimizations, $errors);

// Optimize position table
addIndexIfNotExists($connection, 'position', 'idx_description', '`description`', $optimizations, $errors);

// Optimize administrators table
addIndexIfNotExists($connection, 'administrators', 'idx_username', '`username`', $optimizations, $errors);
addIndexIfNotExists($connection, 'administrators', 'idx_type', '`type`', $optimizations, $errors);

// Optimize tables
$tables = ['employees', 'attendance', 'departments', 'position', 'administrators'];
foreach ($tables as $table) {
    optimizeTable($connection, $table, $optimizations, $errors);
}

// Display results
echo "<h3>Successful Optimizations:</h3>";
echo "<ul>";
foreach ($optimizations as $optimization) {
    echo "<li style='color: green;'>$optimization</li>";
}
echo "</ul>";

if (!empty($errors)) {
    echo "<h3>Errors:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>$error</li>";
    }
    echo "</ul>";
}

// Show table statistics
echo "<h3>Table Statistics:</h3>";
$tables = ['employees', 'attendance', 'departments', 'position', 'administrators'];
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Table</th><th>Rows</th><th>Data Size</th><th>Index Size</th></tr>";

foreach ($tables as $table) {
    $query = "SELECT 
                table_rows as 'rows',
                ROUND(((data_length) / 1024 / 1024), 2) as 'data_size_mb',
                ROUND(((index_length) / 1024 / 1024), 2) as 'index_size_mb'
              FROM information_schema.TABLES 
              WHERE table_schema = DATABASE() AND table_name = '$table'";
    
    $result = mysqli_query($connection, $query);
    if ($result && $row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>$table</td>";
        echo "<td>" . number_format($row['rows']) . "</td>";
        echo "<td>" . $row['data_size_mb'] . " MB</td>";
        echo "<td>" . $row['index_size_mb'] . " MB</td>";
        echo "</tr>";
    }
}
echo "</table>";

// Performance recommendations
echo "<h3>Performance Recommendations:</h3>";
echo "<ul>";
echo "<li>Database indexes have been optimized for faster queries</li>";
echo "<li>Tables have been optimized to reduce fragmentation</li>";
echo "<li>Consider enabling query caching in MySQL configuration</li>";
echo "<li>Monitor slow query log for further optimization opportunities</li>";
echo "<li>Regular maintenance: Run this optimization monthly</li>";
echo "</ul>";

mysqli_close($connection);
?>
