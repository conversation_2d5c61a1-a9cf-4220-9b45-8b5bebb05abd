<?php
require_once('../includes/script.php');
require_once('../session/Login.php');

// Initialize database connection
$model = new Dashboard();
$connection = $model->TemporaryConnection();

// Set up logging
$log_file = __DIR__ . '/personnel_receive.log';
function write_log($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message\n", FILE_APPEND);
}

// Log request details
write_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
write_log("Content Type: " . (isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : 'Not set'));

// Get the raw POST data
$raw_data = file_get_contents('php://input');
write_log("Raw POST data: " . $raw_data);

// Try to parse the data
$data = null;

// First try to parse as JSON
$data = json_decode($raw_data, true);
if (json_last_error() === JSON_ERROR_NONE) {
    write_log("Successfully parsed as JSON");
} else {
    write_log("Failed to parse as JSON: " . json_last_error_msg());
    
    // Try to parse as form data
    parse_str($raw_data, $form_data);
    write_log("Form data: " . print_r($form_data, true));
    
    // If we have a data field, try to parse it as JSON
    if (isset($form_data['data']) && is_string($form_data['data'])) {
        $json_data = json_decode($form_data['data'], true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $data = $json_data;
            write_log("Successfully parsed data field as JSON");
        } else {
            write_log("Failed to parse data field as JSON: " . json_last_error_msg());
        }
    }
}

// Log the processed data
write_log("Processed data: " . print_r($data, true));

// Check if we have valid data
if (!$data) {
    write_log("No valid data found in request");
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'No valid data found in request']);
    exit;
}

if (!isset($data['deviceKey'])) {
    error_log("Missing deviceKey in data");
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing deviceKey in data']);
    exit;
}

if (!isset($data['data'])) {
    error_log("Missing data array in request");
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing data array in request']);
    exit;
}

if (!is_array($data['data'])) {
    error_log("Data is not an array: " . gettype($data['data']));
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Data must be an array']);
    exit;
}

try {
    // Process each personnel record
    foreach ($data['data'] as $person) {
        error_log("Processing person: " . print_r($person, true));
        
        // Validate required fields
        if (!isset($person['personSn']) || !isset($person['personName'])) {
            error_log("Missing required fields in person data");
            continue; // Skip this record but continue processing others
        }

        // Check if employee already exists
        $check_query = "SELECT id FROM employees WHERE employee_id = ?";
        $stmt = $connection->prepare($check_query);
        $stmt->bind_param("s", $person['personSn']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            // Update existing employee
            $update_query = "UPDATE employees SET 
                fullname = ?,
                position = ?,
                email = ?,
                phonenumber = ?,
                sex = ?,
                address = ?,
                photo = ?
                WHERE employee_id = ?";
            
            $stmt = $connection->prepare($update_query);
            $stmt->bind_param(
                "ssssssss",
                $person['personName'],
                $person['position'] ?? '',
                $person['email'] ?? '',
                $person['phone'] ?? '',
                $person['gender'] ?? '',
                $person['address'] ?? '',
                $person['photo'] ?? '',
                $person['personSn']
            );
        } else {
            // Insert new employee
            $insert_query = "INSERT INTO employees (
                employee_id,
                fullname,
                position,
                email,
                phonenumber,
                sex,
                address,
                photo,
                created_on
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $connection->prepare($insert_query);
            $stmt->bind_param(
                "ssssssss",
                $person['personSn'],
                $person['personName'],
                $person['position'] ?? '',
                $person['email'] ?? '',
                $person['phone'] ?? '',
                $person['gender'] ?? '',
                $person['address'] ?? '',
                $person['photo'] ?? ''
            );
        }
        
        if (!$stmt->execute()) {
            error_log("Database error for person {$person['personSn']}: " . $stmt->error);
            throw new Exception("Database error: " . $stmt->error);
        }
        $stmt->close();
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Personnel data processed successfully'
    ]);
    
} catch (Exception $e) {
    error_log("Error processing personnel data: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error processing personnel data: ' . $e->getMessage()
    ]);
} 