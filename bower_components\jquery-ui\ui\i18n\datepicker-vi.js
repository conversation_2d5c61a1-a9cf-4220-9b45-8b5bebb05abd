/* Vietnamese initialisation for the jQuery UI date picker plugin. */
/* Translated by <PERSON> (<EMAIL>). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['vi'] = {
	closeText: 'Đóng',
	prevText: '&#x3C;Trước',
	nextText: 'Tiếp&#x3E;',
	currentText: 'Hôm nay',
	monthNames: ['Tháng <PERSON>', 'Tháng <PERSON>', 'Tháng <PERSON>', '<PERSON>háng <PERSON>', 'Tháng <PERSON>', 'Tháng <PERSON>',
	'Tháng <PERSON>', '<PERSON>háng <PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
	monthNamesShort: ['Tháng 1', 'Tháng 2', 'Tháng 3', '<PERSON>h<PERSON><PERSON> 4', 'Tháng 5', '<PERSON>h<PERSON>g 6',
	'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
	day<PERSON>ames: ['Ch<PERSON> Nh<PERSON>t', 'Th<PERSON> <PERSON>', 'Th<PERSON> <PERSON>', 'Th<PERSON> T<PERSON>', 'Thứ N<PERSON>m', 'Th<PERSON> S<PERSON>u', 'Th<PERSON> B<PERSON>y'],
	dayNamesShort: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
	dayNamesMin: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
	weekHeader: 'Tu',
	dateFormat: 'dd/mm/yy',
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['vi']);

return datepicker.regional['vi'];

}));
