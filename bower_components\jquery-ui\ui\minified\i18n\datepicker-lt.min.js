/*! jQuery UI - v1.11.4 - 2015-03-13
* http://jqueryui.com
* Copyright jQuery Foundation and other contributors; Licensed MIT */
(function(e){"function"==typeof define&&define.amd?define(["../datepicker"],e):e(jQuery.datepicker)})(function(e){return e.regional.lt={closeText:"Uždaryti",prevText:"&#x3C;Atgal",nextText:"Pirmyn&#x3E;",currentText:"Šiandien",monthNames:["<PERSON><PERSON><PERSON>","<PERSON>asa<PERSON>","Ko<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON>lis","Lapkritis","Gruod<PERSON>"],monthNamesShort:["Sau","Vas","<PERSON>v","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>ug<PERSON>","Rugs","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>"],dayNames:["sek<PERSON><PERSON><PERSON>","pir<PERSON>ien<PERSON>","antradien<PERSON>","trečiadie<PERSON>","ketvirtadien<PERSON>","penktadienis","šeštadienis"],dayNamesShort:["sek","pir","ant","tre","ket","pen","šeš"],dayNamesMin:["Se","Pr","An","Tr","Ke","Pe","Še"],weekHeader:"SAV",dateFormat:"yy-mm-dd",firstDay:1,isRTL:!1,showMonthAfterYear:!0,yearSuffix:""},e.setDefaults(e.regional.lt),e.regional.lt});