<?php
header('Content-Type: text/plain');

$apiUrl = "http://196.189.151.125:8080/api/HRMAPI/get_employee";

// Initialize cURL session
$ch = curl_init();

// Set cURL options
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

// Execute cURL request
$response = curl_exec($ch);

// Check for cURL errors
if(curl_errno($ch)) {
    echo "Curl error: " . curl_error($ch);
    exit;
}

curl_close($ch);

// Decode the response
$data = json_decode($response, true);

// Print the response structure
echo "API Response Structure:\n";
echo "----------------------\n";
print_r($data);

// If we have data, show some statistics
if(isset($data['data']) && is_array($data['data'])) {
    echo "\n\nData Statistics:\n";
    echo "----------------\n";
    echo "Total records: " . count($data['data']) . "\n";
    
    if(count($data['data']) > 0) {
        echo "\nFirst record structure:\n";
        echo "----------------------\n";
        print_r($data['data'][0]);
    }
} 