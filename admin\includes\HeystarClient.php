<?php
/**
 * HeystarClient - A client for the X05 HeyStar cloud Gateway HTTP API
 * 
 * This class provides methods to interact with the X05 HeyStar cloud Gateway API
 * for device management, personnel management, face management, and record management.
 */
class HeystarClient {
    /**
     * @var string Base URL for API
     */
    private $baseUrl;
    
    /**
     * @var string Device serial number
     */
    private $serialNumber;
    
    /**
     * @var string Device secret key
     */
    private $deviceSecret;
    
    /**
     * @var int Connection timeout in seconds
     */
    private $timeout;

    /**
     * Constructor
     * 
     * @param string $baseUrl Base URL for API (default: http://172.16.204.213:8190/api)
     * @param string $serialNumber Device serial number
     * @param string $deviceSecret Device secret key
     * @param int $timeout Connection timeout in seconds
     */
    public function __construct($baseUrl = 'http://172.16.204.213:8190/api', $serialNumber = '', $deviceSecret = '', $timeout = 5) {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->serialNumber = $serialNumber;
        $this->deviceSecret = $deviceSecret;
        $this->timeout = $timeout;
    }

    /**
     * Make API request
     * 
     * @param string $endpoint API endpoint
     * @param array $params Request parameters
     * @return array Response data
     * @throws Exception if request fails
     */
    private function makeRequest($endpoint, $params = []) {
        $url = $this->baseUrl . '/' . ltrim($endpoint, '/');
        
        // Add authentication parameters
        $params['serial_number'] = $this->serialNumber;
        $params['device_secret'] = $this->deviceSecret;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("Connection error: " . $error);
        }

        if ($httpCode >= 400) {
            throw new Exception("API request failed with status code: " . $httpCode);
        }

        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON response: " . json_last_error_msg());
        }

        return $result;
    }

    /**
     * Test device connection
     * 
     * @return array Connection test result
     */
    public function testConnection() {
        return $this->makeRequest('device/test');
    }

    /**
     * Get device information
     * 
     * @return array Device information
     */
    public function getDeviceInfo() {
        return $this->makeRequest('device/get');
    }

    /**
     * Configure device
     * 
     * @param array $config Configuration parameters
     * @return array Command result
     */
    public function configureDevice($config) {
        return $this->makeRequest('device/setConfig', $config);
    }

    /**
     * Configure recognition settings
     * 
     * @param array $config Recognition configuration parameters
     * @return array Command result
     */
    public function configureRecognition($config) {
        return $this->makeRequest('device/setRecConfig', $config);
    }

    /**
     * Configure PCI settings
     * 
     * @param array $config PCI configuration parameters
     * @return array Command result
     */
    public function configurePCI($config) {
        return $this->makeRequest('device/setPciConfig', $config);
    }

    /**
     * Configure network settings
     * 
     * @param array $config Network configuration parameters
     * @return array Command result
     */
    public function configureNetwork($config) {
        return $this->makeRequest('device/setNetwork', $config);
    }

    /**
     * Reboot device
     * 
     * @return array Command result
     */
    public function rebootDevice() {
        return $this->makeRequest('device/reboot');
    }

    /**
     * Reset device
     * 
     * @param string $type Reset type (all, users, logs)
     * @return array Command result
     */
    public function resetDevice($type = 'all') {
        return $this->makeRequest('device/reset', ['reset_type' => $type]);
    }

    /**
     * Create person record
     * 
     * @param array $personData Person data
     * @return array Command result
     */
    public function createPerson($personData) {
        return $this->makeRequest('person/create', $personData);
    }

    /**
     * Update person record
     * 
     * @param array $personData Person data
     * @return array Command result
     */
    public function updatePerson($personData) {
        return $this->makeRequest('person/update', $personData);
    }

    /**
     * Delete person record
     * 
     * @param string $personId Person ID
     * @return array Command result
     */
    public function deletePerson($personId) {
        return $this->makeRequest('person/delete', ['person_id' => $personId]);
    }

    /**
     * Find person record
     * 
     * @param string $personId Person ID
     * @return array Person data
     */
    public function findPerson($personId) {
        return $this->makeRequest('person/find', ['person_id' => $personId]);
    }

    /**
     * Find list of persons
     * 
     * @param array $params Search parameters
     * @return array List of persons
     */
    public function findPersonList($params = []) {
        return $this->makeRequest('person/findList', $params);
    }

    /**
     * Merge face data
     * 
     * @param array $faceData Face data
     * @return array Command result
     */
    public function mergeFace($faceData) {
        return $this->makeRequest('face/merge', $faceData);
    }

    /**
     * Delete face data
     * 
     * @param string $faceId Face ID
     * @return array Command result
     */
    public function deleteFace($faceId) {
        return $this->makeRequest('face/delete', ['face_id' => $faceId]);
    }

    /**
     * Find face data
     * 
     * @param string $faceId Face ID
     * @return array Face data
     */
    public function findFace($faceId) {
        return $this->makeRequest('face/find', ['face_id' => $faceId]);
    }

    /**
     * Get attendance records
     * 
     * @param array $params Search parameters
     * @return array List of records
     */
    public function getRecords($params = []) {
        return $this->makeRequest('record/findList', $params);
    }

    /**
     * Delete attendance record
     * 
     * @param string $recordId Record ID
     * @return array Command result
     */
    public function deleteRecord($recordId) {
        return $this->makeRequest('record/delete', ['record_id' => $recordId]);
    }

    /**
     * Find attendance record
     * 
     * @param string $recordId Record ID
     * @return array Record data
     */
    public function findRecord($recordId) {
        return $this->makeRequest('record/find', ['record_id' => $recordId]);
    }
} 