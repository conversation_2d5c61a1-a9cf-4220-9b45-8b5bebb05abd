<?php 

require_once('includes/script.php');  
require_once('session/Login.php'); 

 $model = new Dashboard();
 $session = new AdministratorSession();
 $session->LoginSession();

 if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    header("location:index.php?utm_campaign=expired");
 }

 $model = new Dashboard();
 $password = $_SESSION['official_password'];
 $username = $_SESSION['official_username'];
 $uid = $_SESSION['official_id'];

 $connection = $model->TemporaryConnection();

// Get schedules data
$getSched = "SELECT * FROM `schedules` ORDER BY schedule_id DESC";
$res = mysqli_query($connection, $getSched);

 $query = $model->GetAdministrator($username, $password);
 $admin = mysqli_fetch_assoc($query);
        $id = $admin['id'];
        $firstname = $admin['firstname'];
        $lastname = $admin['lastname'];
        $photo = $admin['photo'];
        $create = $admin['created_on'];

 $generate = '';
 $stat = '';
 if(isset($_GET['status'])){
  $generate = $_GET['status'];
 }

     if($generate == '1' ){
      $stat = '<div class="alert alert-success alert-dismissible">
      <button type="button" class="close" data-dismiss="alert"></button>
      <i class="fe fe-check mr-2"></i>Schedule successfully deleted.
      </div>';
    } elseif($generate == '2') {
      $stat = '<div class="alert alert-success alert-dismissible">
      <button type="button" class="close" data-dismiss="alert"></button>
      <i class="fe fe-check mr-2"></i>Schedule successfully added.
      </div>';
    } elseif($generate == 'error') {
      $msg = $_GET['msg'] ?? 'unknown';
      $errorMsg = 'An error occurred';

      switch($msg) {
        case 'delete_failed':
          $errorMsg = 'Failed to delete schedule from database';
          break;
        case 'not_found':
          $errorMsg = 'Schedule not found';
          break;
        case 'no_id':
          $errorMsg = 'No schedule ID provided';
          break;
      }

      $stat = '<div class="alert alert-danger alert-dismissible">
      <button type="button" class="close" data-dismiss="alert"></button>
      <i class="fe fe-alert-triangle mr-2"></i>' . $errorMsg . '
      </div>';
    } else { }
?>
<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <title>Employee Schedules - Management System</title>
    <?php require_once('includes/script.php') ?>
  </head>
  <body class="">
    <div class="page">
      <div class="page-main">
        <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
          <div class="container">
            <div class="row align-items-center">
              <div class="d-flex">
                <?php require_once('includes/header.php') ?>
              </div>
              <div class="col-lg order-lg-first">
                <?php require_once('includes/subheader.php') ?>
              </div>
            </div>
          </div>
        </div>
        <div class="my-3 my-md-5">
          <div class="container">
            <?php echo $stat ?>
            <div class="page-header mb-4">
              <div class="row align-items-center">
                <div class="col">
                  <h1 class="page-title text-gradient">Employee Schedules</h1>
                  <p class="page-subtitle">Manage work schedules and time configurations</p>
                </div>
                <div class="col-auto">
                  <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-add-schedule">
                    <i class="fe fe-plus mr-2"></i>Add New Schedule
                  </button>
                </div>
              </div>
            </div>
            <?php require_once('modals/modal_add_schedule.php') ?>

            <div class="row">
              <div class="col-12">
                <div class="card shadow-custom">
                  <div class="card-header">
                    <h3 class="card-title">
                      <i class="fe fe-calendar mr-2"></i>Work Schedules
                    </h3>
                    <div class="card-options">
                      <span class="badge badge-primary"><?php echo mysqli_num_rows($res); ?> schedules</span>
                    </div>
                  </div>
                  <div class="card-body">
                    <?php if (mysqli_num_rows($res) == 0): ?>
                      <div class="text-center py-5">
                        <i class="fe fe-calendar" style="font-size: 4rem; color: #dee2e6;"></i>
                        <h4 class="mt-3 text-muted">No Schedules Found</h4>
                        <p class="text-muted">Create your first work schedule to get started.</p>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-add-schedule">
                          <i class="fe fe-plus mr-2"></i>Add First Schedule
                        </button>
                      </div>
                    <?php else: ?>
                    <div class="table-responsive">
                      <table class="table table-striped" id="dataTable">
                        <thead>
                          <tr>
                            <th width="120">Schedule ID</th>
                            <th>Morning In</th>
                            <th>Morning Out</th>
                            <th>Afternoon In</th>
                            <th>Afternoon Out</th>
                            <th>Total Hours</th>
                            <th width="100">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <?php
                          // Reset the result pointer
                          mysqli_data_seek($res, 0);
                          while($row = mysqli_fetch_assoc($res)) {
                            // Calculate total hours
                            $morning_start = strtotime($row['time_in_morning']);
                            $morning_end = strtotime($row['time_out_morning']);
                            $afternoon_start = strtotime($row['time_in_afternoon']);
                            $afternoon_end = strtotime($row['time_out_afternoon']);

                            $morning_hours = ($morning_end - $morning_start) / 3600;
                            $afternoon_hours = ($afternoon_end - $afternoon_start) / 3600;
                            $total_hours = $morning_hours + $afternoon_hours;
                          ?>
                          <tr>
                            <td>
                              <span class="badge badge-primary" style="font-size: 0.9rem; padding: 0.5rem 0.75rem;">
                                #<?php echo $row['schedule_id'] ?>
                              </span>
                            </td>
                            <td>
                              <i class="fe fe-sunrise text-warning mr-2"></i>
                              <?php echo date('h:i A', strtotime($row['time_in_morning'])) ?>
                            </td>
                            <td>
                              <i class="fe fe-sun text-orange mr-2"></i>
                              <?php echo date('h:i A', strtotime($row['time_out_morning'])) ?>
                            </td>
                            <td>
                              <i class="fe fe-sun text-info mr-2"></i>
                              <?php echo date('h:i A', strtotime($row['time_in_afternoon'])) ?>
                            </td>
                            <td>
                              <i class="fe fe-sunset text-purple mr-2"></i>
                              <?php echo date('h:i A', strtotime($row['time_out_afternoon'])) ?>
                            </td>
                            <td>
                              <span class="badge badge-success"><?php echo number_format($total_hours, 1); ?> hrs</span>
                            </td>
                            <td>
                              <div class="btn-group">
                                <button class="btn btn-sm btn-outline-danger" data-toggle="modal" data-target="#delete-<?php echo $row['schedule_id'] ?>" title="Delete Schedule">
                                  <i class="fe fe-trash-2"></i>
                                </button>
                              </div>
                            </td>
                          </tr>

                          <!-- Delete Modal -->
                          <div id="delete-<?php echo $row['schedule_id'] ?>" class="modal fade animate" data-backdrop="true">
                            <div class="modal-dialog" id="animate">
                              <div class="modal-content">
                                <div class="modal-header">
                                  <h5 class="modal-title">Delete Schedule</h5>
                                </div>
                                <div class="modal-body text-center p-lg">
                                  <p>Are you sure to execute this action?</p>
                                  <p style="font-size: 25px;"><b>Schedule #<?php echo $row['schedule_id'] ?></b></p>
                                  <div class="text-muted">
                                    <small>
                                      Morning: <?php echo date('h:i A', strtotime($row['time_in_morning'])) ?> - <?php echo date('h:i A', strtotime($row['time_out_morning'])) ?><br>
                                      Afternoon: <?php echo date('h:i A', strtotime($row['time_in_afternoon'])) ?> - <?php echo date('h:i A', strtotime($row['time_out_afternoon'])) ?>
                                    </small>
                                  </div>
                                </div>
                                <div class="modal-footer">
                                  <button type="button" class="btn dark-white p-x-md" data-dismiss="modal">No</button>
                                  <a href="delete/schedule.php?id=<?php echo $row['schedule_id'] ?>"><button type="button" class="btn danger p-x-md">Yes</button></a>
                                </div>
                              </div><!-- /.modal-content -->
                            </div>
                          </div>
                          <?php } ?>
                        </tbody>
                      </table>
                    </div>
                    <?php endif; ?>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <?php require_once('includes/footer.php') ?>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.22/js/dataTables.bootstrap4.min.js"></script>

    <script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "pageLength": 10,
            "order": [[0, "desc"]],
            "columnDefs": [
                { "orderable": false, "targets": [6] }
            ]
        });
    });
    </script>
  </body>
</html>