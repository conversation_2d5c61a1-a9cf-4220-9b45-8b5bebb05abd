<?php
require_once('../session/Login.php');

// Log the incoming request for debugging
file_put_contents('attendance_log.txt', date('Y-m-d H:i:s') . ' - ' . file_get_contents('php://input') . "\n", FILE_APPEND);

// Get the JSON data from the request
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

// Initialize database connection
$model = new Dashboard();
$connection = $model->TemporaryConnection();

// Check if data is valid
if ($data && isset($data['user_id']) && isset($data['timestamp'])) {
    // Extract data
    $employee_id = mysqli_real_escape_string($connection, $data['user_id']);
    $timestamp = mysqli_real_escape_string($connection, $data['timestamp']);
    $device_id = isset($data['device_id']) ? mysqli_real_escape_string($connection, $data['device_id']) : 'Unknown';
    
    // Convert timestamp to date and time
    $date = date('Y-m-d', strtotime($timestamp));
    $time = date('H:i:s', strtotime($timestamp));
    
    // Check if employee exists
    $employee_check = "SELECT id FROM employees WHERE employee_id = '$employee_id'";
    $employee_result = mysqli_query($connection, $employee_check);
    
    if (mysqli_num_rows($employee_result) > 0) {
        // Employee exists, record attendance
        $employee_data = mysqli_fetch_assoc($employee_result);
        $emp_id = $employee_data['id'];
        
        // Check if attendance already recorded for this date
        $attendance_check = "SELECT attendance_id FROM attendance 
                            WHERE employee_id = '$emp_id' AND date = '$date' AND status = 'in'";
        $attendance_result = mysqli_query($connection, $attendance_check);
        
        if (mysqli_num_rows($attendance_result) > 0) {
            // Already has clock-in, record as clock-out
            $insert = "INSERT INTO attendance (employee_id, date, time_in, time_out, status) 
                      VALUES ('$emp_id', '$date', NULL, '$time', 'out')";
        } else {
            // No clock-in yet, record as clock-in
            $insert = "INSERT INTO attendance (employee_id, date, time_in, time_out, status) 
                      VALUES ('$emp_id', '$date', '$time', NULL, 'in')";
        }
        
        if (mysqli_query($connection, $insert)) {
            // Success
            $response = [
                'status' => 'success',
                'message' => 'Attendance recorded successfully'
            ];
        } else {
            // Database error
            $response = [
                'status' => 'error',
                'message' => 'Database error: ' . mysqli_error($connection)
            ];
        }
    } else {
        // Employee not found
        $response = [
            'status' => 'error',
            'message' => 'Employee not found'
        ];
    }
} else {
    // Invalid data
    $response = [
        'status' => 'error',
        'message' => 'Invalid data received'
    ];
}

// Send response
header('Content-Type: application/json');
echo json_encode($response);

