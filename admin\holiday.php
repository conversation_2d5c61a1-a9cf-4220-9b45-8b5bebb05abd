<?php 

require_once('includes/script.php');  
require_once('session/Login.php'); 

 $model = new Dashboard();
 $session = new AdministratorSession();
 $session->LoginSession();

 if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    header("location:index.php?utm_campaign=expired");
 }

 $model = new Dashboard();
 $password = $_SESSION['official_password'];
 $username = $_SESSION['official_username'];
 $uid = $_SESSION['official_id'];

 $connection = $model->TemporaryConnection();

 $query = $model->GetAdministrator($username, $password);
 $admin = mysqli_fetch_assoc($query);
        $id = $admin['id'];
        $firstname = $admin['firstname'];
        $lastname = $admin['lastname'];
        $photo = $admin['photo'];
        $create = $admin['created_on'];

 $generate = '';
 $stat = '';
 if(isset($_GET['status'])){
  $generate = $_GET['status'];
 }

 if($generate == '1'){
  $stat = '<div class="alert alert-success alert-dismissible">
  <button type="button" class="close" data-dismiss="alert"></button>
  Holiday successfully added.
  </div>';
 } else if($generate == '2'){
  $stat = '<div class="alert alert-success alert-dismissible">
  <button type="button" class="close" data-dismiss="alert"></button>
  Holiday successfully deleted.
  </div>';
 }

 $queryHolidays = "SELECT * FROM holidays ORDER BY holiday_date DESC";
 $holidaysResult = mysqli_query($connection, $queryHolidays);

?>
<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <title>Holiday Management - Management System</title>
    <?php require_once('includes/script.php') ?>
  </head>
  <body class="">
    <div class="page">
      <div class="page-main">
        <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
          <div class="container">
            <div class="row align-items-center">
              <div class="d-flex">
                <?php require_once('includes/header.php') ?>
              </div>
              <div class="col-lg order-lg-first">
                <?php require_once('includes/subheader.php') ?>
              </div>
            </div>
          </div>
        </div>
        <div class="my-3 my-md-5">
          <div class="container">
            <?php echo $stat ?>
            <div class="page-header mb-4">
              <div class="row align-items-center">
                <div class="col">
                  <h1 class="page-title text-gradient">Holiday Management</h1>
                  <p class="page-subtitle">Manage company holidays and special dates</p>
                </div>
                <div class="col-auto">
                  <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-add-holiday">
                    <i class="fe fe-plus mr-2"></i>Add New Holiday
                  </button>
                </div>
              </div>
            </div>
            <?php require_once('modals/modal_add_holiday.php') ?>

            <div class="row">
              <div class="col-12">
                <div class="card shadow-custom">
                  <div class="card-header">
                    <h3 class="card-title">
                      <i class="fe fe-calendar mr-2"></i>Company Holidays
                    </h3>
                    <div class="card-options">
                      <span class="badge badge-primary"><?php echo mysqli_num_rows($holidaysResult); ?> holidays</span>
                    </div>
                  </div>
                  <div class="card-body">
                    <?php if (mysqli_num_rows($holidaysResult) == 0): ?>
                      <div class="text-center py-5">
                        <i class="fe fe-calendar" style="font-size: 4rem; color: #dee2e6;"></i>
                        <h4 class="mt-3 text-muted">No Holidays Found</h4>
                        <p class="text-muted">Add your first company holiday to get started.</p>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-add-holiday">
                          <i class="fe fe-plus mr-2"></i>Add First Holiday
                        </button>
                      </div>
                    <?php else: ?>
                    <div class="table-responsive">
                      <table class="table table-striped" id="dataTable">
                        <thead>
                          <tr>
                            <th>No.</th>
                            <th>Holiday Name</th>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Paid</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <?php
                          // Reset the result pointer
                          mysqli_data_seek($holidaysResult, 0);
                          $count = 0;
                          while($row = mysqli_fetch_assoc($holidaysResult)) {
                          $count++;
                           ?>
                          <tr>
                            <td><span class="text-muted"><?php echo $count ?></span></td>
                            <td>
                              <div class="d-flex align-items-center">
                                <i class="fe fe-calendar text-primary mr-2"></i>
                                <span class="font-weight-bold"><?php echo htmlspecialchars($row['holiday_name']) ?></span>
                              </div>
                            </td>
                            <td>
                              <span class="text-muted"><?php echo date('F d, Y', strtotime($row['holiday_date'])) ?></span>
                              <br><small class="text-muted"><?php echo date('l', strtotime($row['holiday_date'])) ?></small>
                            </td>
                            <td><?php echo htmlspecialchars($row['description']) ?></td>
                            <td>
                              <?php echo ($row['is_paid'] == 1) ? '<span class="badge badge-success">Paid</span>' : '<span class="badge badge-secondary">Unpaid</span>'; ?>
                            </td>
                            <td>
                              <button class="btn btn-sm btn-outline-danger" onclick="deleteHoliday('<?php echo $row['id'] ?>', '<?php echo addslashes($row['holiday_name']) ?>', '<?php echo date('F d, Y', strtotime($row['holiday_date'])) ?>')" title="Delete Holiday">
                                <i class="fe fe-trash-2"></i>
                              </button>
                            </td>
                          </tr>
                          <?php } ?>
                        </tbody>
                      </table>
                    </div>
                    <?php endif; ?>
                  </div>
                </div>
              </div>
            </div>

            <!-- Single Delete Modal -->
            <div id="deleteHolidayModal" class="modal fade" tabindex="-1" role="dialog">
              <div class="modal-dialog" role="document">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title">
                      <i class="fe fe-trash-2 text-danger mr-2"></i>Delete Holiday
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                      <span aria-hidden="true">&times;</span>
                    </button>
                  </div>
                  <div class="modal-body text-center">
                    <div class="mb-3">
                      <i class="fe fe-alert-triangle text-warning" style="font-size: 3rem;"></i>
                    </div>
                    <h4>Are you sure?</h4>
                    <p class="text-muted">
                      This will permanently delete the holiday <strong id="holidayNameText"></strong>
                    </p>
                    <div class="card bg-light">
                      <div class="card-body py-2">
                        <small class="text-muted">
                          <strong>Holiday Details:</strong><br>
                          <span id="holidayDetailsText"></span>
                        </small>
                      </div>
                    </div>
                    <div class="alert alert-warning mt-3">
                      <small><i class="fe fe-info mr-2"></i>This action cannot be undone</small>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                      <i class="fe fe-x mr-2"></i>Cancel
                    </button>
                    <a href="#" id="deleteHolidayConfirmBtn" class="btn btn-danger">
                      <i class="fe fe-trash-2 mr-2"></i>Yes, Delete Holiday
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <?php require_once('includes/footer.php') ?>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.22/js/dataTables.bootstrap4.min.js"></script>

    <script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "pageLength": 10,
            "order": [[2, "desc"]], // Sort by date
            "columnDefs": [
                { "orderable": false, "targets": [5] }
            ]
        });
    });

    // Delete holiday function
    function deleteHoliday(holidayId, holidayName, holidayDate) {
        // Update modal content
        document.getElementById('holidayNameText').textContent = holidayName;
        document.getElementById('holidayDetailsText').innerHTML = holidayName + '<br>Date: ' + holidayDate;
        document.getElementById('deleteHolidayConfirmBtn').href = 'delete/holiday.php?id=' + holidayId;

        // Show modal
        $('#deleteHolidayModal').modal('show');
    }
    </script>
  </body>
</html>
