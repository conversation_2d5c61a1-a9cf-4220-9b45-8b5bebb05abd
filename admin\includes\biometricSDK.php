<?php
/**
 * BiometricSDK - A class to interact with X05 HeyStar cloud Gateway HTTP API
 * 
 * This class provides methods to communicate with biometric devices
 * for attendance tracking and user management.
 */
class BiometricSDK {
    /**
     * @var string Base URL for API
     */
    private $baseUrl;
    
    /**
     * @var string Device serial number
     */
    private $serial_number;
    
    /**
     * @var string Device secret key for authentication
     */
    private $device_secret;
    
    /**
     * @var int Connection timeout in seconds
     */
    private $timeout;
    
    /**
     * Constructor
     * 
     * @param string $serial_number Device serial number
     * @param string $device_secret Device secret key
     * @param string $baseUrl Base URL for API (default: http://172.16.204.213:8190/api)
     * @param int $timeout Connection timeout in seconds
     */
    public function __construct($serial_number = '', $device_secret = '', $baseUrl = 'http://172.16.204.213:8190/api', $timeout = 5) {
        $this->serial_number = $serial_number;
        $this->device_secret = $device_secret;
        $this->baseUrl = $baseUrl;
        $this->timeout = $timeout;
    }
    
    /**
     * Make API request to device
     * 
     * @param string $endpoint API endpoint
     * @param array $params Parameters to send
     * @return array Response from device
     */
    private function makeApiRequest($endpoint, $params = []) {
        $url = "{$this->baseUrl}/{$endpoint}";
        
        // Add authentication parameters
        $params['serial_number'] = $this->serial_number;
        $params['device_secret'] = $this->device_secret;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        
        $response = curl_exec($ch);
        $error = curl_error($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        curl_close($ch);
        
        if ($error) {
            return [
                'status' => 'error',
                'message' => "Connection error: {$error}",
                'code' => 0
            ];
        }
        
        if ($http_code != 200) {
            return [
                'status' => 'error',
                'message' => "HTTP error: {$http_code}",
                'code' => $http_code
            ];
        }
        
        $result = json_decode($response, true);
        
        if (!$result) {
            return [
                'status' => 'error',
                'message' => "Invalid response format",
                'code' => 0
            ];
        }
        
        return $result;
    }
    
    /**
     * Get device information
     * 
     * @return array Device information
     */
    public function getDeviceInfo() {
        return $this->makeApiRequest('device/get');
    }
    
    /**
     * Test device connection
     * 
     * @return array Connection test result
     */
    public function testConnection() {
        return $this->makeApiRequest('device/test');
    }
    
    /**
     * Configure device
     * 
     * @param array $config Configuration parameters
     * @return array Command result
     */
    public function configureDevice($config) {
        return $this->makeApiRequest('device/setConfig', $config);
    }
    
    /**
     * Configure recognition settings
     * 
     * @param array $config Recognition configuration parameters
     * @return array Command result
     */
    public function configureRecognition($config) {
        return $this->makeApiRequest('device/setRecConfig', $config);
    }
    
    /**
     * Configure PCI settings
     * 
     * @param array $config PCI configuration parameters
     * @return array Command result
     */
    public function configurePCI($config) {
        return $this->makeApiRequest('device/setPciConfig', $config);
    }
    
    /**
     * Configure network settings
     * 
     * @param array $config Network configuration parameters
     * @return array Command result
     */
    public function configureNetwork($config) {
        return $this->makeApiRequest('device/setNetwork', $config);
    }
    
    /**
     * Reboot device
     * 
     * @return array Command result
     */
    public function rebootDevice() {
        return $this->makeApiRequest('device/reboot');
    }
    
    /**
     * Reset device
     * 
     * @param string $type Reset type (all, users, logs)
     * @return array Command result
     */
    public function resetDevice($type = 'all') {
        return $this->makeApiRequest('device/reset', ['reset_type' => $type]);
    }
    
    /**
     * Create person record
     * 
     * @param array $personData Person data
     * @return array Command result
     */
    public function createPerson($personData) {
        return $this->makeApiRequest('person/create', $personData);
    }
    
    /**
     * Update person record
     * 
     * @param array $personData Person data
     * @return array Command result
     */
    public function updatePerson($personData) {
        return $this->makeApiRequest('person/update', $personData);
    }
    
    /**
     * Delete person record
     * 
     * @param string $personId Person ID
     * @return array Command result
     */
    public function deletePerson($personId) {
        return $this->makeApiRequest('person/delete', ['person_id' => $personId]);
    }
    
    /**
     * Find person record
     * 
     * @param string $personId Person ID
     * @return array Person data
     */
    public function findPerson($personId) {
        return $this->makeApiRequest('person/find', ['person_id' => $personId]);
    }
    
    /**
     * Find list of persons
     * 
     * @param array $params Search parameters
     * @return array List of persons
     */
    public function findPersonList($params = []) {
        return $this->makeApiRequest('person/findList', $params);
    }
    
    /**
     * Merge face data
     * 
     * @param array $faceData Face data
     * @return array Command result
     */
    public function mergeFace($faceData) {
        return $this->makeApiRequest('face/merge', $faceData);
    }
    
    /**
     * Delete face data
     * 
     * @param string $faceId Face ID
     * @return array Command result
     */
    public function deleteFace($faceId) {
        return $this->makeApiRequest('face/delete', ['face_id' => $faceId]);
    }
    
    /**
     * Find face data
     * 
     * @param string $faceId Face ID
     * @return array Face data
     */
    public function findFace($faceId) {
        return $this->makeApiRequest('face/find', ['face_id' => $faceId]);
    }
    
    /**
     * Get attendance records
     * 
     * @param array $params Search parameters
     * @return array List of records
     */
    public function getRecords($params = []) {
        return $this->makeApiRequest('record/findList', $params);
    }
    
    /**
     * Delete attendance record
     * 
     * @param string $recordId Record ID
     * @return array Command result
     */
    public function deleteRecord($recordId) {
        return $this->makeApiRequest('record/delete', ['record_id' => $recordId]);
    }
    
    /**
     * Find attendance record
     * 
     * @param string $recordId Record ID
     * @return array Record data
     */
    public function findRecord($recordId) {
        return $this->makeApiRequest('record/find', ['record_id' => $recordId]);
    }
}
?>
