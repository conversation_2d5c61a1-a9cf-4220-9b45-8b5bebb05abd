/*! jQuery UI - v1.11.4 - 2015-03-13
* http://jqueryui.com
* Copyright jQuery Foundation and other contributors; Licensed MIT */
(function(t){"function"==typeof define&&define.amd?define(["../datepicker"],t):t(jQuery.datepicker)})(function(t){return t.regional.et={closeText:"Sulge",prevText:"Eelnev",nextText:"Järgnev",currentText:"<PERSON>äna",monthNames:["<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Aprill","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","August","September","Oktoober","November","Detsember"],monthNamesShort:["<PERSON><PERSON>","<PERSON>ee<PERSON><PERSON>","<PERSON><PERSON><PERSON>","Apr","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Aug","Sept","Okt","Nov","Det<PERSON>"],dayNames:["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],dayNamesShort:["<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>isip","Kolmap","Neljap","Reede","Laup"],dayNamesMin:["P","E","T","K","N","R","L"],weekHeader:"näd",dateFormat:"dd.mm.yy",firstDay:1,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""},t.setDefaults(t.regional.et),t.regional.et});