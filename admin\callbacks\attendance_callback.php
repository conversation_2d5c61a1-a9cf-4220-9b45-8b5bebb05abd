<?php
require_once '../includes/session.php';
require_once '../includes/conn.php';

// Get the JSON payload
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// Log the received data
file_put_contents('attendance_log.txt', date('Y-m-d H:i:s') . ' - ' . $json . PHP_EOL, FILE_APPEND);

// Validate the data
if (!$data || !isset($data['deviceKey']) || !isset($data['employeeId']) || !isset($data['timestamp'])) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Invalid data']);
    exit;
}

// Extract data
$device_key = $data['deviceKey'];
$employee_id = $data['employeeId'];
$timestamp = $data['timestamp'];
$date = date('Y-m-d', strtotime($timestamp));
$time = date('H:i:s', strtotime($timestamp));

// Get device ID from device key
$device_query = "SELECT id FROM biometric_devices WHERE serial_number = '$device_key'";
$device_result = mysqli_query($connection, $device_query);

if (mysqli_num_rows($device_result) == 0) {
    http_response_code(404);
    echo json_encode(['status' => 'error', 'message' => 'Device not found']);
    exit;
}

$device = mysqli_fetch_assoc($device_result);
$device_id = $device['id'];

// Insert into biometric_logs table (create if not exists)
$create_table = "CREATE TABLE IF NOT EXISTS biometric_logs (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    device_id INT(11) NOT NULL,
    employee_id VARCHAR(50) NOT NULL,
    timestamp DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES biometric_devices(id)
)";
mysqli_query($connection, $create_table);

// Insert the log
$insert = "INSERT INTO biometric_logs (device_id, employee_id, timestamp) 
           VALUES ($device_id, '$employee_id', '$timestamp')";
if (mysqli_query($connection, $insert)) {
    // Check if we need to create/update attendance record
    $attendance_query = "SELECT * FROM attendance WHERE employee_id = '$employee_id' AND date = '$date'";
    $attendance_result = mysqli_query($connection, $attendance_query);
    
    if (mysqli_num_rows($attendance_result) > 0) {
        // Update existing attendance record
        $attendance = mysqli_fetch_assoc($attendance_result);
        $attendance_id = $attendance['id'];
        
        // Determine if this is time in or time out
        $hour = (int)date('H', strtotime($timestamp));
        if ($hour < 12) {
            // Morning time
            if (empty($attendance['time_in_morning'])) {
                $update = "UPDATE attendance SET time_in_morning = '$time', status_morning = 'ontime' WHERE id = $attendance_id";
            } else {
                $update = "UPDATE attendance SET time_out_morning = '$time' WHERE id = $attendance_id";
            }
        } else {
            // Afternoon time
            if (empty($attendance['time_in_afternoon'])) {
                $update = "UPDATE attendance SET time_in_afternoon = '$time', status_afternoon = 'ontime' WHERE id = $attendance_id";
            } else {
                $update = "UPDATE attendance SET time_out_afternoon = '$time' WHERE id = $attendance_id";
            }
        }
        
        mysqli_query($connection, $update);
    } else {
        // Create new attendance record
        $hour = (int)date('H', strtotime($timestamp));
        if ($hour < 12) {
            // Morning time in
            $insert_attendance = "INSERT INTO attendance (employee_id, date, time_in_morning, status_morning) 
                                 VALUES ('$employee_id', '$date', '$time', 'ontime')";
        } else {
            // Afternoon time in
            $insert_attendance = "INSERT INTO attendance (employee_id, date, time_in_afternoon, status_afternoon) 
                                 VALUES ('$employee_id', '$date', '$time', 'ontime')";
        }
        
        mysqli_query($connection, $insert_attendance);
    }
    
    echo json_encode(['status' => 'success']);
} else {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Database error: ' . mysqli_error($connection)]);
}