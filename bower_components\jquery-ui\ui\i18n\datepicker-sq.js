/* Albanian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['sq'] = {
	closeText: 'mbylle',
	prevText: '&#x3C;mbrapa',
	nextText: 'Përpara&#x3E;',
	currentText: 'sot',
	monthNames: ['<PERSON><PERSON>','<PERSON>hkurt','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>',
	'<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','Nëntor','Dh<PERSON>or'],
	monthNamesShort: ['Jan','<PERSON>hk','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>',
	'<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>hj'],
	dayNames: ['<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','E Premte','E Shtune'],
	dayNamesShort: ['Di','Hë','Ma','Më','En','Pr','Sh'],
	dayNamesMin: ['Di','Hë','Ma','Më','En','Pr','Sh'],
	weekHeader: 'Ja',
	dateFormat: 'dd.mm.yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['sq']);

return datepicker.regional['sq'];

}));
