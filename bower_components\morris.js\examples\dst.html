<!doctype html>
<head>
  <script src="http://cdnjs.cloudflare.com/ajax/libs/jquery/2.0.3/jquery.min.js"></script>
  <script src="http://cdnjs.cloudflare.com/ajax/libs/raphael/2.1.2/raphael-min.js"></script>
  <script src="../morris.js"></script>
  <script src="http://cdnjs.cloudflare.com/ajax/libs/prettify/r224/prettify.min.js"></script>
  <script src="lib/example.js"></script>
  <link rel="stylesheet" href="lib/example.css">
  <link rel="stylesheet" href="http://cdnjs.cloudflare.com/ajax/libs/prettify/r224/prettify.min.css">
  <link rel="stylesheet" href="../morris.css">
</head>
<body>
<h1>Daylight-savings time</h1>
<div id="graph"></div>
<pre id="code" class="prettyprint linenums">
// This crosses a DST boundary in the UK.
Morris.Area({
  element: 'graph',
  data: [
    {x: '2013-03-30 22:00:00', y: 3, z: 3},
    {x: '2013-03-31 00:00:00', y: 2, z: 0},
    {x: '2013-03-31 02:00:00', y: 0, z: 2},
    {x: '2013-03-31 04:00:00', y: 4, z: 4}
  ],
  xkey: 'x',
  ykeys: ['y', 'z'],
  labels: ['Y', 'Z']
});
</pre>
</body>
