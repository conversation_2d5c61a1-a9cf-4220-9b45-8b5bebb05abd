<?php
require_once('../session/Login.php');

$model = new Dashboard();
$connection = $model->TemporaryConnection();

// Get date to check (default to yesterday)
$checkDate = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d', strtotime('-1 day'));

// Get all active employees
$employeeQuery = "SELECT id FROM employees";
$employeeResult = mysqli_query($connection, $employeeQuery);

// Get all employees who attended on the check date
$attendanceQuery = "SELECT DISTINCT employee_id FROM attendance WHERE date = '$checkDate'";
$attendanceResult = mysqli_query($connection, $attendanceQuery);

$attendedEmployees = array();
while ($row = mysqli_fetch_assoc($attendanceResult)) {
    $attendedEmployees[] = $row['employee_id'];
}

$absencesAdded = 0;

// Check each employee
while ($employee = mysqli_fetch_assoc($employeeResult)) {
    $employeeId = $employee['id'];
    
    // If employee didn't attend and doesn't already have an absence record
    if (!in_array($employeeId, $attendedEmployees)) {
        // Check if absence already recorded
        $checkQuery = "SELECT id FROM absences WHERE employee_id = '$employeeId' AND date = '$checkDate'";
        $checkResult = mysqli_query($connection, $checkQuery);
        
        if (mysqli_num_rows($checkResult) == 0) {
            // Record new absence
            $insertQuery = "INSERT INTO absences (employee_id, date, type) VALUES ('$employeeId', '$checkDate', 'Unexcused')";
            if (mysqli_query($connection, $insertQuery)) {
                $absencesAdded++;
            }
        }
    }
}

echo "Absence check completed for $checkDate. Added $absencesAdded new absence records.";
?>
