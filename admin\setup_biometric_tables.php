<?php
/**
 * Setup Biometric Integration Tables
 * Creates necessary database tables for biometric integration
 */

require_once('session/ModelController.php');

$model = new Dashboard();
$connection = $model->TemporaryConnection();

// Check if user is admin
session_start();
if(!isset($_SESSION['official_username'])) {
    die('Access denied');
}

$tables_created = [];
$errors = [];

// Function to create table if it doesn't exist
function createTableIfNotExists($connection, $tableName, $createSQL, &$tables_created, &$errors) {
    // Check if table exists
    $checkQuery = "SHOW TABLES LIKE '$tableName'";
    $result = mysqli_query($connection, $checkQuery);
    
    if (mysqli_num_rows($result) == 0) {
        // Table doesn't exist, create it
        if (mysqli_query($connection, $createSQL)) {
            $tables_created[] = "Created table '$tableName'";
        } else {
            $errors[] = "Failed to create table '$tableName': " . mysqli_error($connection);
        }
    } else {
        $tables_created[] = "Table '$tableName' already exists";
    }
}

echo "<h2>Biometric Integration Database Setup</h2>";

// 1. Create biometric_users table
$biometric_users_sql = "CREATE TABLE `biometric_users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `employee_id` varchar(50) NOT NULL,
    `fullname` varchar(255) NOT NULL,
    `biometric_id` varchar(100) DEFAULT NULL,
    `face_template` longtext DEFAULT NULL,
    `fingerprint_template` longtext DEFAULT NULL,
    `status` enum('pending','active','inactive','suspended') DEFAULT 'pending',
    `registered_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `employee_id` (`employee_id`),
    KEY `idx_status` (`status`),
    KEY `idx_biometric_id` (`biometric_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

createTableIfNotExists($connection, 'biometric_users', $biometric_users_sql, $tables_created, $errors);

// 2. Create payroll_records table
$payroll_records_sql = "CREATE TABLE `payroll_records` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `employee_id` varchar(50) NOT NULL,
    `month` int(2) NOT NULL,
    `year` int(4) NOT NULL,
    `total_days_worked` int(3) DEFAULT 0,
    `total_hours` decimal(8,2) DEFAULT 0.00,
    `regular_hours` decimal(8,2) DEFAULT 0.00,
    `overtime_hours` decimal(8,2) DEFAULT 0.00,
    `basic_pay` decimal(10,2) DEFAULT 0.00,
    `overtime_pay` decimal(10,2) DEFAULT 0.00,
    `gross_pay` decimal(10,2) DEFAULT 0.00,
    `deductions` decimal(10,2) DEFAULT 0.00,
    `net_pay` decimal(10,2) DEFAULT 0.00,
    `generated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `employee_month_year` (`employee_id`, `month`, `year`),
    KEY `idx_month_year` (`month`, `year`),
    KEY `idx_generated_at` (`generated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

createTableIfNotExists($connection, 'payroll_records', $payroll_records_sql, $tables_created, $errors);

// 3. Create biometric_attendance_logs table (for raw biometric data)
$biometric_logs_sql = "CREATE TABLE `biometric_attendance_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `device_id` int(11) DEFAULT NULL,
    `employee_id` varchar(50) NOT NULL,
    `biometric_id` varchar(100) DEFAULT NULL,
    `recognition_type` enum('face','fingerprint','card','manual') DEFAULT 'face',
    `event_type` enum('check_in','check_out','break_in','break_out') DEFAULT 'check_in',
    `event_time` timestamp NOT NULL,
    `confidence_score` decimal(5,3) DEFAULT NULL,
    `temperature` decimal(4,1) DEFAULT NULL,
    `mask_detected` tinyint(1) DEFAULT NULL,
    `photo_path` varchar(500) DEFAULT NULL,
    `processed` tinyint(1) DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_employee_id` (`employee_id`),
    KEY `idx_event_time` (`event_time`),
    KEY `idx_processed` (`processed`),
    KEY `idx_device_id` (`device_id`),
    FOREIGN KEY (`device_id`) REFERENCES `biometric_devices` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

createTableIfNotExists($connection, 'biometric_attendance_logs', $biometric_logs_sql, $tables_created, $errors);

// 4. Create attendance_rules table
$attendance_rules_sql = "CREATE TABLE `attendance_rules` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `rule_name` varchar(100) NOT NULL,
    `morning_start` time DEFAULT '08:00:00',
    `morning_end` time DEFAULT '12:00:00',
    `afternoon_start` time DEFAULT '13:00:00',
    `afternoon_end` time DEFAULT '17:00:00',
    `grace_period_minutes` int(3) DEFAULT 15,
    `late_threshold_minutes` int(3) DEFAULT 30,
    `minimum_work_hours` decimal(4,2) DEFAULT 8.00,
    `overtime_threshold` decimal(4,2) DEFAULT 8.00,
    `is_default` tinyint(1) DEFAULT 0,
    `status` enum('active','inactive') DEFAULT 'active',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_is_default` (`is_default`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

createTableIfNotExists($connection, 'attendance_rules', $attendance_rules_sql, $tables_created, $errors);

// 5. Insert default attendance rule
$default_rule_check = "SELECT COUNT(*) as count FROM attendance_rules WHERE is_default = 1";
$default_result = mysqli_query($connection, $default_rule_check);
$default_count = mysqli_fetch_assoc($default_result)['count'];

if ($default_count == 0) {
    $insert_default_rule = "INSERT INTO attendance_rules 
        (rule_name, morning_start, morning_end, afternoon_start, afternoon_end, grace_period_minutes, late_threshold_minutes, minimum_work_hours, is_default) 
        VALUES 
        ('Default Work Schedule', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 15, 30, 8.00, 1)";
    
    if (mysqli_query($connection, $insert_default_rule)) {
        $tables_created[] = "Inserted default attendance rule";
    } else {
        $errors[] = "Failed to insert default attendance rule: " . mysqli_error($connection);
    }
}

// 6. Create employee_schedules table
$employee_schedules_sql = "CREATE TABLE `employee_schedules` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `employee_id` varchar(50) NOT NULL,
    `rule_id` int(11) NOT NULL,
    `effective_date` date NOT NULL,
    `end_date` date DEFAULT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_employee_id` (`employee_id`),
    KEY `idx_effective_date` (`effective_date`),
    KEY `idx_is_active` (`is_active`),
    FOREIGN KEY (`rule_id`) REFERENCES `attendance_rules` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

createTableIfNotExists($connection, 'employee_schedules', $employee_schedules_sql, $tables_created, $errors);

// 7. Add indexes to existing attendance table for better performance
$add_indexes = [
    "ALTER TABLE `attendance` ADD INDEX `idx_employee_date` (`employee_id`, `date`)",
    "ALTER TABLE `attendance` ADD INDEX `idx_date_status` (`date`, `status_morning`, `status_afternoon`)",
    "ALTER TABLE `employees` ADD INDEX `idx_employee_id_fullname` (`employee_id`, `fullname`)"
];

foreach ($add_indexes as $index_sql) {
    if (mysqli_query($connection, $index_sql)) {
        $tables_created[] = "Added database index";
    } else {
        // Index might already exist, so we don't treat this as an error
        if (!strpos(mysqli_error($connection), 'Duplicate key name')) {
            $errors[] = "Index creation warning: " . mysqli_error($connection);
        }
    }
}

// Display results
echo "<h3>Setup Results:</h3>";

if (!empty($tables_created)) {
    echo "<h4 style='color: green;'>Successful Operations:</h4>";
    echo "<ul>";
    foreach ($tables_created as $success) {
        echo "<li style='color: green;'>$success</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h4 style='color: red;'>Errors:</h4>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>$error</li>";
    }
    echo "</ul>";
}

// Show table statistics
echo "<h3>Database Tables Status:</h3>";
$tables = [
    'biometric_users', 
    'payroll_records', 
    'biometric_attendance_logs', 
    'attendance_rules', 
    'employee_schedules',
    'biometric_devices',
    'employees',
    'attendance'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Table Name</th><th>Exists</th><th>Row Count</th></tr>";

foreach ($tables as $table) {
    $check_query = "SHOW TABLES LIKE '$table'";
    $check_result = mysqli_query($connection, $check_query);
    $exists = mysqli_num_rows($check_result) > 0;
    
    $row_count = 0;
    if ($exists) {
        $count_query = "SELECT COUNT(*) as count FROM `$table`";
        $count_result = mysqli_query($connection, $count_query);
        if ($count_result) {
            $row_count = mysqli_fetch_assoc($count_result)['count'];
        }
    }
    
    echo "<tr>";
    echo "<td>$table</td>";
    echo "<td style='color: " . ($exists ? 'green' : 'red') . ";'>" . ($exists ? 'Yes' : 'No') . "</td>";
    echo "<td>" . number_format($row_count) . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>Next Steps:</h3>";
echo "<ul>";
echo "<li>Visit <a href='biometric_integration.php'>Biometric Integration Dashboard</a> to start registering employees</li>";
echo "<li>Configure biometric devices in <a href='biometrics.php'>Device Management</a></li>";
echo "<li>Set up attendance rules and employee schedules as needed</li>";
echo "<li>Test the biometric attendance system</li>";
echo "</ul>";

mysqli_close($connection);
?>
