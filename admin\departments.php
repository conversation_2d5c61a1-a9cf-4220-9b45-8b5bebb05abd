<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once('includes/script.php');
require_once('session/Login.php');
require_once('includes/DepartmentAPI.php');

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

if (!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])) {
    header("location:index.php?utm_campaign=expired");
}

$model = new Dashboard();
$password = $_SESSION['official_password'];
$username = $_SESSION['official_username'];
$uid = $_SESSION['official_id'];

$connection = $model->TemporaryConnection();

$query = $model->GetAdministrator($username, $password);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];
$type = $admin['type'];

// Initialize DepartmentAPI
$departmentAPI = new DepartmentAPI();

// Handle sync request
$syncMessage = '';
if (isset($_POST['sync_departments'])) {
    $result = $departmentAPI->syncDepartments($connection);
    if ($result['success']) {
        $syncMessage = '<div class="alert alert-success">' . $result['message'] . '</div>';
    } else {
        $syncMessage = '<div class="alert alert-danger">' . $result['message'] . '</div>';
    }
}

// Handle create department
if (isset($_POST['create_department'])) {
    $name = mysqli_real_escape_string($connection, $_POST['department_name']);
    $parent_id = (int)$_POST['parent_department'];
    
    $query = "INSERT INTO departments (department_name, parent_department, created_at) 
              VALUES ('$name', $parent_id, NOW())";
    
    if (mysqli_query($connection, $query)) {
        $syncMessage = '<div class="alert alert-success">Department created successfully!</div>';
    } else {
        $syncMessage = '<div class="alert alert-danger">Failed to create department: ' . mysqli_error($connection) . '</div>';
    }
}

// Get departments from API
try {
    $departments = $departmentAPI->getDepartments();
} catch (Exception $e) {
    $departments = [];
    $syncMessage = '<div class="alert alert-danger">Failed to fetch departments: ' . $e->getMessage() . '</div>';
}
?>

<!doctype html>
<html lang="en" dir="ltr">
<head>
    <title>Profiling and Payroll Management System</title>

    <style>
        thead th {
            position: sticky;
            top: 0;
            background-color: white;
            z-index: 1;
        }
        .table-responsive {
            max-height: 500px;
            overflow-y: auto;
        }
    </style>
    <script>
        $(document).ready(function() {
            $('#departmentsTable').DataTable({
                "pageLength": 10,
                "order": [[ 1, "asc" ]]
            });
        });
    </script>

</head>
<body>
    <div class="page" id="app">
        <div class="page-main">
            <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="d-flex">
                            <?php require_once('includes/header.php') ?>
                        </div>
                        <div class="col-lg order-lg-first">
                            <?php require_once('includes/subheader.php') ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="my-3 my-md-5">
                <div class="container">
                    <?php echo $syncMessage; ?>
                    <div class="page-header">
                        <h1 class="page-title">
                            Departments
                        </h1>
                        <div class="page-options d-flex align-items-center">
                            <button type="button" class="btn btn-primary mr-2" data-toggle="modal" data-target="#createDepartmentModal">
                                <i class="fe fe-plus mr-2"></i>Create Department
                            </button>
                            <div>
                                <form method="post" style="padding-top: 15px;">
                                    <button type="submit" name="sync_departments" class="btn btn-primary">
                                        <i class="fe fe-refresh-cw mr-2"></i>Sync Departments
                                    </button>
                                </form>
                            </div>
                        </div>

                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="departmentsTable" class="table table-hover table-outline table-vcenter text-nowrap card-table">
                                    <thead>
                                        <tr>
                                            <th>Department Code</th>
                                            <th>Department Name</th>
                                            <th>Parent Department</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($departments as $dept): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($dept['department_code']); ?></td>
                                            <td><?php echo htmlspecialchars($dept['department_name']); ?></td>
                                            <td><?php echo $dept['parent_department'] == 0 ? 'None' : htmlspecialchars($dept['parent_department']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php require_once('includes/footer.php') ?>
    </div>
    <?php require_once('includes/datatables.php') ?>

    <!-- Create Department Modal -->
    <div class="modal fade" id="createDepartmentModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Create New Department</h5>
                        <button type="button" class="close" data-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>Department Name</label>
                            <input type="text" class="form-control" name="department_name" required>
                        </div>
                        <div class="form-group">
                            <label>Parent Department</label>
                            <select class="form-control" name="parent_department">
                                <option value="0">None</option>
                                <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo $dept['department_code']; ?>">
                                    <?php echo htmlspecialchars($dept['department_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        <button type="submit" name="create_department" class="btn btn-primary">Create Department</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html> 