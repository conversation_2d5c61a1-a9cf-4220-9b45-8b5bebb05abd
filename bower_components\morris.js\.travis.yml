language: node_js
node_js:
  - 0.10
before_script:
  - "npm install -g grunt-cli"
  - "npm install"
  - "cp -f bower.travis.json bower.json"
  - 'sed -i -e "s/JQUERY/$JQUERY/" bower.json'
  - 'sed -i -e "s/RAPHAEL/$RAPHAEL/" bower.json'
  - "bower install"
env:
  - JQUERY="~> 1.8.0" RAPHAEL="~> 2.0.0"
  - JQUERY="~> 1.9.0" RAPHAEL="~> 2.0.0"
  - JQUERY="~> 2.0.0" RAPHAEL="~> 2.0.0"
  - JQUERY="~> 2.1.0" RAPHAEL="~> 2.0.0"
  - JQUERY="~> 1.8.0" RAPHAEL="~> 2.1.0"
  - JQUERY="~> 1.9.0" RAPHAEL="~> 2.1.0"
  - JQUERY="~> 2.0.0" RAPHAEL="~> 2.1.0"
  - JQUERY="~> 2.1.0" RAPHAEL="~> 2.1.0"
