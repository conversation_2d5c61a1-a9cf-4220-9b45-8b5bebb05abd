<?php 
 if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    header("location:index.php?utm_campaign=expired");
 }

 if(isset($_POST['add'])){
  $holiday_name = $_POST['holiday_name'];
  $date = $_POST['holiday_year'].'-'.$_POST['holiday_month'].'-'.$_POST['holiday_day'];
  $description = $_POST['description'];
  $is_paid = isset($_POST['is_paid']) ? 1 : 0;

  $insert = "INSERT INTO `holidays` (`holiday_name`, `holiday_date`, `description`, `is_paid`) 
             VALUES ('$holiday_name', '$date', '$description', '$is_paid')";
  
  if(mysqli_query($connection, $insert)){
    echo "<script>window.location.href='holiday.php?status=1'</script>";
  }
 }
?>

<div id="modal-add-holiday" class="modal fade animate" data-backdrop="true">
  <div class="modal-dialog" id="animate">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Add Holiday</h5>
      </div>
      <div class="modal-body p-lg">
        <form action="" method="post">
          <div class="form-group">
            <label class="form-label">Holiday Name</label>
            <input type="text" class="form-control" name="holiday_name" required>
          </div>
          <div class="form-group">
            <label class="form-label">Date</label>
            <div class="row">
              <div class="col-md-4">
                <select class="form-control" name="holiday_month" required>
                  <option value="">Month</option>
                  <?php for($i = 1; $i <= 12; $i++): ?>
                    <option value="<?php echo str_pad($i, 2, '0', STR_PAD_LEFT); ?>"><?php echo date('F', mktime(0, 0, 0, $i, 1)); ?></option>
                  <?php endfor; ?>
                </select>
              </div>
              <div class="col-md-4">
                <select class="form-control" name="holiday_day" required>
                  <option value="">Day</option>
                  <?php for($i = 1; $i <= 31; $i++): ?>
                    <option value="<?php echo str_pad($i, 2, '0', STR_PAD_LEFT); ?>"><?php echo $i; ?></option>
                  <?php endfor; ?>
                </select>
              </div>
              <div class="col-md-4">
                <select class="form-control" name="holiday_year" required>
                  <option value="">Year</option>
                  <?php for($i = date('Y'); $i <= date('Y')+5; $i++): ?>
                    <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                  <?php endfor; ?>
                </select>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label class="form-label">Description</label>
            <textarea class="form-control" name="description" rows="3"></textarea>
          </div>
          <div class="form-group">
            <div class="custom-control custom-checkbox">
              <input type="checkbox" class="custom-control-input" id="is_paid" name="is_paid" checked>
              <label class="custom-control-label" for="is_paid">Paid Holiday</label>
            </div>
          </div>
          <div class="form-group">
            <button type="submit" name="add" class="btn btn-primary">Add Holiday</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>