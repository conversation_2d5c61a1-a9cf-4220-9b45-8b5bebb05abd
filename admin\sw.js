/**
 * Service Worker for ERP System
 * Handles caching for improved performance
 */

const CACHE_NAME = 'erp-system-v1';
const STATIC_CACHE = 'erp-static-v1';
const API_CACHE = 'erp-api-v1';

// Files to cache immediately
const STATIC_FILES = [
    '../assets/css/dashboard.css',
    '../assets/css/professional-theme.css',
    'js/performance-optimizer.js',
    'js/auto-sync.js',
    'includes/script.php',
    'includes/header.php'
];

// API endpoints to cache
const API_ENDPOINTS = [
    '/admin/includes/api_cache.php',
    '/admin/sync/auto_sync.php'
];

// Install event - cache static files
self.addEventListener('install', event => {
    event.waitUntil(
        Promise.all([
            caches.open(STATIC_CACHE).then(cache => {
                return cache.addAll(STATIC_FILES);
            }),
            caches.open(API_CACHE)
        ]).then(() => {
            self.skipWaiting();
        })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== STATIC_CACHE && cacheName !== API_CACHE) {
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            self.clients.claim();
        })
    );
});

// Fetch event - serve from cache when possible
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);
    
    // Handle different types of requests
    if (request.method === 'GET') {
        if (isStaticFile(url.pathname)) {
            event.respondWith(handleStaticFile(request));
        } else if (isApiRequest(url.pathname)) {
            event.respondWith(handleApiRequest(request));
        } else if (isPageRequest(request)) {
            event.respondWith(handlePageRequest(request));
        }
    }
});

// Check if request is for a static file
function isStaticFile(pathname) {
    return pathname.includes('.css') || 
           pathname.includes('.js') || 
           pathname.includes('.png') || 
           pathname.includes('.jpg') || 
           pathname.includes('.ico');
}

// Check if request is for API
function isApiRequest(pathname) {
    return pathname.includes('/api/') || 
           pathname.includes('api_cache.php') ||
           pathname.includes('auto_sync.php');
}

// Check if request is for a page
function isPageRequest(request) {
    return request.headers.get('accept').includes('text/html');
}

// Handle static file requests
async function handleStaticFile(request) {
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
        // Serve from cache
        return cachedResponse;
    }
    
    try {
        // Fetch from network and cache
        const response = await fetch(request);
        if (response.ok) {
            cache.put(request, response.clone());
        }
        return response;
    } catch (error) {
        // Return cached version if available
        return cachedResponse || new Response('File not available offline');
    }
}

// Handle API requests
async function handleApiRequest(request) {
    const cache = await caches.open(API_CACHE);
    const url = new URL(request.url);
    
    // For API requests, try network first, then cache
    try {
        const response = await fetch(request);
        
        if (response.ok) {
            // Cache successful API responses for 5 minutes
            const responseToCache = response.clone();
            const headers = new Headers(responseToCache.headers);
            headers.set('sw-cached-at', Date.now().toString());
            
            const cachedResponse = new Response(responseToCache.body, {
                status: responseToCache.status,
                statusText: responseToCache.statusText,
                headers: headers
            });
            
            cache.put(request, cachedResponse);
        }
        
        return response;
    } catch (error) {
        // Network failed, try cache
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            const cachedAt = cachedResponse.headers.get('sw-cached-at');
            const age = Date.now() - parseInt(cachedAt || '0');
            
            // Use cached response if less than 5 minutes old
            if (age < 5 * 60 * 1000) {
                return cachedResponse;
            }
        }
        
        // Return error response
        return new Response(JSON.stringify({
            error: 'Network unavailable',
            cached: false
        }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// Handle page requests
async function handlePageRequest(request) {
    try {
        // Always try network first for pages
        const response = await fetch(request);
        return response;
    } catch (error) {
        // Could implement offline page here
        return new Response('Page not available offline', {
            status: 503,
            headers: { 'Content-Type': 'text/html' }
        });
    }
}

// Background sync for API data
self.addEventListener('sync', event => {
    if (event.tag === 'api-sync') {
        event.waitUntil(syncApiData());
    }
});

async function syncApiData() {
    try {
        // Sync critical API data in background
        const response = await fetch('/admin/sync/auto_sync.php?manual_run=true');
        if (response.ok) {
            console.log('Background API sync completed');
        }
    } catch (error) {
        console.log('Background sync failed:', error);
    }
}

// Handle messages from main thread
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    } else if (event.data && event.data.type === 'CLEAR_CACHE') {
        clearAllCaches();
    }
});

async function clearAllCaches() {
    const cacheNames = await caches.keys();
    await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
    );
}
