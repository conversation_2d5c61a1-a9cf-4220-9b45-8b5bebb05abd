# Bootstrap Colorpicker 2

Fancy and customizable color picker plugin for Twitter Bootstrap

[![Build Status](https://travis-ci.org/itsjavi/bootstrap-colorpicker.svg?branch=master)](https://travis-ci.org/itsjavi/bootstrap-colorpicker)

## Installation
For downloading the source code, you have many choices:

- Downloading the [latest source code ZIP file](https://github.com/itsjavi/bootstrap-colorpicker/archive/master.zip)
- Cloning the source code: `git clone https://github.com/itsjavi/bootstrap-colorpicker.git`
- Installing via Bower: `bower install bootstrap-colorpicker`
- Installing via NPM: `npm install bootstrap-colorpicker`
- Installing via Composer: `composer require itsjavi/bootstrap-colorpicker`

## Getting started
- For using the plugin you will only need the files under the `dist` folder
- You can read the [documentation here](https://itsjavi.com/bootstrap-colorpicker/)

## Contributing and reporting issues
If you want to contribute to the source code or report issues and suggestions, please read the [CONTRIBUTING.md](CONTRIBUTING.md) guidelines first. Some steps are mandatory in order to accept a Pull Request.

## Credits
Originally written by [<PERSON>](http://www.eyecon.ro/)
