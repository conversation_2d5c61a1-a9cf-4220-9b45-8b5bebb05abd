/* Romanian initialisation for the jQuery UI date picker plugin.
 *
 * Written by <PERSON> (ll_ed<PERSON>@walla.com)
 * and <PERSON><PERSON> (<EMAIL>)
 */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['ro'] = {
	closeText: 'Închide',
	prevText: '&#xAB; Luna precedentă',
	nextText: 'Luna următoare &#xBB;',
	currentText: '<PERSON><PERSON>',
	monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>',
	'<PERSON>ulie','August','Septembrie','Octombrie','Noie<PERSON>rie','Decembrie'],
	monthNamesShort: ['<PERSON>', 'Feb', '<PERSON>', 'Apr', '<PERSON>', '<PERSON><PERSON>',
	'<PERSON><PERSON>', 'Aug', '<PERSON>', 'Oct', 'Nov', 'Dec'],
	dayNames: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>i', '<PERSON>eri', 'S<PERSON>mbăt<PERSON>'],
	dayNamesShort: ['Dum', 'Lun', 'Mar', 'Mie', 'Joi', 'Vin', 'Sâm'],
	dayNamesMin: ['Du','Lu','Ma','Mi','Jo','Vi','Sâ'],
	weekHeader: 'Săpt',
	dateFormat: 'dd.mm.yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['ro']);

return datepicker.regional['ro'];

}));
