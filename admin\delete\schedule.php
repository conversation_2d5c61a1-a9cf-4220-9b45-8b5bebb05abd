
<?php 

require_once('../includes/script.php');  
require_once('../session/Login.php'); 

 $model = new Dashboard();
 $session = new AdministratorSession();
 $session->LoginSession();

 if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    header("location:index.php?utm_campaign=expired");
 }

 $model = new Dashboard();
 $password = $_SESSION['official_password'];
 $username = $_SESSION['official_username'];
 $uid = $_SESSION['official_id'];

 $connection = $model->TemporaryConnection();

 $query = $model->GetAdministrator($username, $password);
 $admin = mysqli_fetch_assoc($query);
        $id = $admin['id'];
        $firstname = $admin['firstname'];
        $lastname = $admin['lastname'];
        $photo = $admin['photo'];
        $create = $admin['created_on'];
	

if(isset($_GET['id'])){
	$id = mysqli_real_escape_string($connection, $_GET['id']);

	// Check if schedule exists before deleting
	$check = "SELECT schedule_id FROM `schedules` WHERE `schedule_id` = '$id'";
	$checkResult = mysqli_query($connection, $check);

	if(mysqli_num_rows($checkResult) > 0) {
		$delete = "DELETE FROM `schedules` WHERE `schedule_id` = '$id'";
		$query = mysqli_query($connection, $delete);

		if($query) {
			header("location: ../schedule.php?status=1");
		} else {
			header("location: ../schedule.php?status=error&msg=delete_failed");
		}
	} else {
		header("location: ../schedule.php?status=error&msg=not_found");
	}
} else {
	header("location: ../schedule.php?status=error&msg=no_id");
}



?>