/* Azerbaijani (UTF-8) initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['az'] = {
	closeText: 'Bağla',
	prevText: '&#x3C;Geri',
	nextText: 'İrəli&#x3E;',
	currentText: 'Bugün',
	monthNames: ['<PERSON><PERSON>','Fevral','<PERSON>','Aprel','May','<PERSON>yun',
	'<PERSON>yul','Avqust','Sentyabr','Oktyabr','Noyabr','Dekabr'],
	monthNamesShort: ['Yan','Fev','<PERSON>','Apr','May','<PERSON>yun',
	'<PERSON>yu<PERSON>','Avq','<PERSON>','Okt','Noy','Dek'],
	dayNames: ['<PERSON><PERSON>','<PERSON><PERSON> ert<PERSON>','<PERSON>ərşənbə axşamı','Çərşənbə','Cümə axşamı','Cümə','Şənbə'],
	dayNamesShort: ['B','Be','Ça','Ç','Ca','C','Ş'],
	dayNamesMin: ['B','B','Ç','С','Ç','C','Ş'],
	weekHeader: 'Hf',
	dateFormat: 'dd.mm.yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['az']);

return datepicker.regional['az'];

}));
