/*! jQuery UI - v1.11.4 - 2015-03-13
* http://jqueryui.com
* Copyright jQuery Foundation and other contributors; Licensed MIT */
(function(t){"function"==typeof define&&define.amd?define(["jquery","./effect"],t):t(jQuery)})(function(t){return t.effects.effect.blind=function(e,i){var s,n,o,a=t(this),r=/up|down|vertical/,h=/up|left|vertical|horizontal/,l=["position","top","bottom","left","right","height","width"],c=t.effects.setMode(a,e.mode||"hide"),u=e.direction||"up",d=r.test(u),p=d?"height":"width",f=d?"top":"left",g=h.test(u),m={},v="show"===c;a.parent().is(".ui-effects-wrapper")?t.effects.save(a.parent(),l):t.effects.save(a,l),a.show(),s=t.effects.createWrapper(a).css({overflow:"hidden"}),n=s[p](),o=parseFloat(s.css(f))||0,m[p]=v?n:0,g||(a.css(d?"bottom":"right",0).css(d?"top":"left","auto").css({position:"absolute"}),m[f]=v?o:n+o),v&&(s.css(p,0),g||s.css(f,o+n)),s.animate(m,{duration:e.duration,easing:e.easing,queue:!1,complete:function(){"hide"===c&&a.hide(),t.effects.restore(a,l),t.effects.removeWrapper(a),i()}})}});