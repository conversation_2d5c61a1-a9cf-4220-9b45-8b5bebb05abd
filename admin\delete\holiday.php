<?php 

require_once('../includes/script.php');  
require_once('../session/Login.php'); 

 $model = new Dashboard();
 $session = new AdministratorSession();
 $session->LoginSession();

 if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    header("location:index.php?utm_campaign=expired");
 }

 $model = new Dashboard();
 $password = $_SESSION['official_password'];
 $username = $_SESSION['official_username'];
 $uid = $_SESSION['official_id'];

 $connection = $model->TemporaryConnection();

 $id = $_GET['id'];

 $delete = "DELETE FROM `holidays` WHERE `id` = '$id'";
 $query = mysqli_query($connection, $delete);

 header("location:../holiday.php?status=2");
?>