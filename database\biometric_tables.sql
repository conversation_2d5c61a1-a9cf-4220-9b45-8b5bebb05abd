-- Table structure for table `biometric_devices`
CREATE TABLE IF NOT EXISTS `biometric_devices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_name` varchar(255) NOT NULL,
  `device_model` varchar(255) NOT NULL DEFAULT 'SMT3568',
  `serial_number` varchar(255) NOT NULL COMMENT 'deviceKey in HeyStar SDK',
  `device_secret` varchar(255) NOT NULL COMMENT 'secret key for device communication',
  `ip_address` varchar(50) NOT NULL,
  `port` int(11) NOT NULL DEFAULT 8081,
  `location` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive','maintenance') NOT NULL DEFAULT 'active',
  `last_sync` datetime DEFAULT NULL,
  `last_heartbeat` datetime DEFAULT NULL,
  `firmware_version` varchar(50) DEFAULT NULL,
  `added_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `serial_number` (`serial_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `biometric_attendance_logs`
CREATE TABLE IF NOT EXISTS `biometric_attendance_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` int(11) NOT NULL,
  `employee_id` int(11) DEFAULT NULL,
  `record_type` enum('face','card','idcard','qrcode','palm','finger') NOT NULL,
  `record_time` datetime NOT NULL,
  `record_image` varchar(255) DEFAULT NULL,
  `temperature` decimal(5,2) DEFAULT NULL,
  `mask_status` tinyint(1) DEFAULT NULL,
  `verification_result` tinyint(1) NOT NULL,
  `raw_data` text DEFAULT NULL,
  `processed` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `device_id` (`device_id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `biometric_attendance_logs_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `biometric_devices` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `biometric_device_configs`
CREATE TABLE IF NOT EXISTS `biometric_device_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` int(11) NOT NULL,
  `config_type` enum('recognition','recognition_mode','peripheral','display','server','custom') NOT NULL,
  `config_data` text NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `device_id` (`device_id`),
  CONSTRAINT `biometric_device_configs_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `biometric_devices` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; 