/* Malaysian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['ms'] = {
	closeText: 'Tutup',
	prevText: '&#x3C;Sebelum',
	nextText: 'Selepas&#x3E;',
	currentText: 'hari ini',
	monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','April','<PERSON>','<PERSON>',
	'<PERSON><PERSON>','Ogos','September','Oktober','November','Disember'],
	monthNamesShort: ['Jan','Feb','Mac','Apr','<PERSON>','<PERSON>',
	'Jul','<PERSON><PERSON>','Sep','Okt','Nov','Di<PERSON>'],
	dayNames: ['Ah<PERSON>','Isnin','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','Sabtu'],
	dayNamesShort: ['Aha','Isn','Sel','Rab','kha','Jum','Sab'],
	dayNamesMin: ['Ah','Is','Se','Ra','Kh','Ju','Sa'],
	weekHeader: 'Mg',
	dateFormat: 'dd/mm/yy',
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['ms']);

return datepicker.regional['ms'];

}));
