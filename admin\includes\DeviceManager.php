<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

class DeviceManager {
    private $baseUrl;
    private $connection;
    private $apiVersion = 'v1';

    public function __construct($connection) {
        $this->baseUrl = 'http://**************:8190/api';
        $this->connection = $connection;
    }

    public function addDevice($deviceData) {
        // First add to database
        $query = "INSERT INTO biometric_devices (
            device_name, device_model, serial_number, device_secret,
            ip_address, port, location, status, added_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?)";
        
        $stmt = $this->connection->prepare($query);
        
        $deviceName = $deviceData['device_name'];
        $deviceModel = $deviceData['device_model'] ?? 'SMT3568';
        $serialNumber = $deviceData['serial_number'];
        $deviceSecret = $deviceData['device_secret'];
        $ipAddress = $deviceData['ip_address'];
        $port = $deviceData['port'];
        $location = $deviceData['location'];
        $addedBy = $deviceData['added_by'];
        
        $stmt->bind_param(
            "sssssisi", 
            $deviceName,
            $deviceModel,
            $serialNumber,
            $deviceSecret,
            $ipAddress,
            $port,
            $location,
            $addedBy
        );
        
        if (!$stmt->execute()) {
            throw new Exception("Database error: " . $stmt->error);
        }
        
        $deviceId = $stmt->insert_id;
        $stmt->close();

        // Test connection with device
        try {
            $result = $this->testDeviceConnection($deviceData);
            if (!$result['success']) {
                $this->updateDeviceStatus($deviceId, 'inactive');
                throw new Exception("Device connection test failed: " . $result['message']);
            }
            
            // Get device info
            $deviceInfo = $this->getDeviceInfoFromApi($deviceData);
            $this->updateDeviceInfo($deviceId, [
                'firmware_version' => $deviceInfo['version'] ?? null,
                'last_heartbeat' => date('Y-m-d H:i:s')
            ]);
            
            return $deviceId;
        } catch (Exception $e) {
            throw new Exception("Device connection error: " . $e->getMessage());
        }
    }

    private function testDeviceConnection($deviceData) {
        $url = "{$this->baseUrl}/device/test";
        $data = [
            'deviceKey' => $deviceData['serial_number'],
            'secret' => $deviceData['device_secret'],
            'ip' => $deviceData['ip_address'],
            'port' => $deviceData['port']
        ];

        try {
            $response = $this->makeApiRequest($url, $data);
            return [
                'success' => isset($response['success']) ? $response['success'] : false,
                'message' => isset($response['msg']) ? $response['msg'] : 'Unknown response from device',
                'code' => isset($response['code']) ? $response['code'] : '999'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'code' => '999'
            ];
        }
    }

    private function getDeviceInfoFromApi($deviceData) {
        $url = "{$this->baseUrl}/device/get";
        $data = [
            'deviceKey' => $deviceData['serial_number'],
            'secret' => $deviceData['device_secret']
        ];

        try {
            $response = $this->makeApiRequest($url, $data);
            if (!isset($response['data'])) {
                throw new Exception("No device data in response");
            }
            
            // Map the API response fields to our database fields
            return [
                'firmware_version' => $response['data']['firmwareVersion'] ?? null,
                'device_model' => $response['data']['manufacturer'] ?? null,
                'os_version' => $response['data']['osVersion'] ?? null,
                'face_algorithm_version' => $response['data']['faceAlgorithmVersion'] ?? null,
                'person_count' => $response['data']['personCount'] ?? 0,
                'photo_count' => $response['data']['photoCount'] ?? 0,
                'finger_count' => $response['data']['fingerCount'] ?? 0,
                'palm_count' => $response['data']['palmCount'] ?? 0,
                'ram_usage' => $response['data']['ram'] ?? null,
                'rom_usage' => $response['data']['rom'] ?? null,
                'mac_address' => $response['data']['wanMac'] ?? null,
                'wifi_mac' => $response['data']['wifiMac'] ?? null,
                'imei' => $response['data']['imei'] ?? null,
                'channel' => $response['data']['channel'] ?? null,
                'cpu_info' => $response['data']['cpu'] ?? null,
                'device_key' => $response['data']['deviceKey'] ?? null
            ];
        } catch (Exception $e) {
            throw new Exception("Failed to get device info: " . $e->getMessage());
        }
    }

    protected function makeApiRequest($url, $method = 'GET', $data = null) {
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            if ($method === 'POST') {
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
            }
            
            $response = curl_exec($ch);
            if(curl_errno($ch)) {
                error_log("cURL Error: " . curl_error($ch));
                return false;
            }
            curl_close($ch);
            return $response;
        } else {
            // Fallback to file_get_contents
            $context = stream_context_create([
                'http' => [
                    'method' => $method,
                    'header' => 'Content-Type: application/json',
                    'ignore_errors' => true,
                    'timeout' => 30,
                    'content' => $data ? json_encode($data) : null
                ]
            ]);
            
            $response = @file_get_contents($url, false, $context);
            if ($response === false) {
                error_log("Error making API request using file_get_contents");
                return false;
            }
            return $response;
        }
    }

    public function updateDevice($deviceId, $deviceData) {
        $query = "UPDATE biometric_devices SET 
            device_name = ?, 
            device_model = ?,
            serial_number = ?, 
            device_secret = ?, 
            ip_address = ?, 
            port = ?, 
            location = ?, 
            status = ?,
            updated_at = CURRENT_TIMESTAMP
            WHERE id = ?";
        
        $stmt = $this->connection->prepare($query);
        
        // Create variables for bind_param
        $deviceName = $deviceData['device_name'];
        $deviceModel = $deviceData['device_model'] ?? 'SMT3568';
        $serialNumber = $deviceData['serial_number'];
        $deviceSecret = $deviceData['device_secret'];
        $ipAddress = $deviceData['ip_address'];
        $port = $deviceData['port'];
        $location = $deviceData['location'];
        $status = $deviceData['status'];
        
        $stmt->bind_param(
            "sssssissi",
            $deviceName,
            $deviceModel,
            $serialNumber,
            $deviceSecret,
            $ipAddress,
            $port,
            $location,
            $status,
            $deviceId
        );
        
        if (!$stmt->execute()) {
            throw new Exception("Database error: " . $stmt->error);
        }
        $stmt->close();

        // Test connection with updated credentials
        try {
            $result = $this->testDeviceConnection($deviceData);
            if (!$result['success']) {
                $this->updateDeviceStatus($deviceId, 'inactive');
                throw new Exception("Device connection test failed: " . $result['message']);
            }
            
            // Update device info
            $deviceInfo = $this->getDeviceInfoFromApi($deviceData);
            $this->updateDeviceInfo($deviceId, [
                'firmware_version' => $deviceInfo['version'] ?? null,
                'last_heartbeat' => date('Y-m-d H:i:s')
            ]);
            
            return true;
        } catch (Exception $e) {
            throw new Exception("Device connection error: " . $e->getMessage());
        }
    }

    public function deleteDevice($deviceId) {
        // Get device info before deletion
        $device = $this->getDeviceInfoById($deviceId);
        
        // Delete from device first
        if ($device) {
            try {
                $url = "{$this->baseUrl}/device/delete";
                $data = [
                    'serial_number' => $device['serial_number'],
                    'device_secret' => $device['device_secret']
                ];
                $this->makeApiRequest($url, $data);
            } catch (Exception $e) {
                // Log error but continue with database deletion
                error_log("Failed to delete device from API: " . $e->getMessage());
            }
        }

        // Delete from database
        $query = "DELETE FROM biometric_devices WHERE id = ?";
        $stmt = $this->connection->prepare($query);
        $stmt->bind_param("i", $deviceId);
        
        if (!$stmt->execute()) {
            throw new Exception("Database error: " . $stmt->error);
        }
        $stmt->close();
        return true;
    }

    public function getDeviceInfo($deviceId) {
        $query = "SELECT * FROM biometric_devices WHERE id = ?";
        $stmt = $this->connection->prepare($query);
        $stmt->bind_param("i", $deviceId);
        $stmt->execute();
        $result = $stmt->get_result();
        $device = $result->fetch_assoc();
        $stmt->close();

        if (!$device) {
            throw new Exception("Device not found");
        }

        // Get live device info from API
        try {
            $deviceInfo = $this->getDeviceInfoFromApi([
                'serial_number' => $device['serial_number'],
                'device_secret' => $device['device_secret']
            ]);
            return array_merge($device, $deviceInfo);
        } catch (Exception $e) {
            return $device;
        }
    }

    private function updateDeviceStatus($deviceId, $status) {
        $query = "UPDATE biometric_devices SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $stmt = $this->connection->prepare($query);
        $stmt->bind_param("si", $status, $deviceId);
        $stmt->execute();
        $stmt->close();
    }

    private function updateDeviceInfo($deviceId, $info) {
        $query = "UPDATE biometric_devices SET 
            firmware_version = ?,
            last_heartbeat = ?,
            updated_at = CURRENT_TIMESTAMP
            WHERE id = ?";
        
        $stmt = $this->connection->prepare($query);
        $stmt->bind_param("ssi", 
            $info['firmware_version'],
            $info['last_heartbeat'],
            $deviceId
        );
        $stmt->execute();
        $stmt->close();
    }

    private function getDeviceInfoById($deviceId) {
        $query = "SELECT * FROM biometric_devices WHERE id = ?";
        $stmt = $this->connection->prepare($query);
        $stmt->bind_param("i", $deviceId);
        $stmt->execute();
        $result = $stmt->get_result();
        $device = $result->fetch_assoc();
        $stmt->close();
        return $device;
    }

    public function setUploadUrl($deviceData, $callbackUrl) {
        $url = "{$this->baseUrl}/device/setUploadUrl";
        $data = [
            'deviceKey' => $deviceData['serial_number'],
            'secret' => $deviceData['device_secret'],
            'data' => json_encode([
                [
                    'type' => 2, // Recognition/attendance
                    'url' => $callbackUrl
                ]
            ])
        ];

        try {
            $response = $this->makeApiRequest($url, $data);
            return [
                'success' => isset($response['success']) ? $response['success'] : false,
                'message' => isset($response['msg']) ? $response['msg'] : 'Unknown response from device',
                'code' => isset($response['code']) ? $response['code'] : '999'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'code' => '999'
            ];
        }
    }

    public function rebootDevice($deviceData) {
        $url = "{$this->baseUrl}/device/reboot";
        $data = [
            'deviceKey' => $deviceData['serial_number'],
            'secret' => $deviceData['device_secret']
        ];

        try {
            $response = $this->makeApiRequest($url, $data);
            return [
                'success' => isset($response['success']) ? $response['success'] : false,
                'message' => isset($response['msg']) ? $response['msg'] : 'Unknown response from device',
                'code' => isset($response['code']) ? $response['code'] : '999'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'code' => '999'
            ];
        }
    }

    public function setRecognitionConfig($deviceData, $config) {
        $url = "{$this->baseUrl}/device/setRecConfig";
        $data = [
            'deviceKey' => $deviceData['serial_number'],
            'secret' => $deviceData['device_secret'],
            'recThreshold1vN' => $config['face_threshold'] ?? 0.7,
            'recInterval' => $config['recognition_interval'] ?? 1,
            'livenessCheck' => $config['liveness_check'] ?? 1,
            'maskCheck' => $config['mask_detection'] ?? 0,
            'temperatureCheck' => $config['temperature_check'] ?? 0,
            'temperatureThreshold' => $config['temperature_threshold'] ?? 37.3
        ];

        try {
            $response = $this->makeApiRequest($url, $data);
            return [
                'success' => isset($response['success']) ? $response['success'] : false,
                'message' => isset($response['msg']) ? $response['msg'] : 'Unknown response from device',
                'code' => isset($response['code']) ? $response['code'] : '999'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'code' => '999'
            ];
        }
    }

    public function resetDevice($deviceData, $type = 'all') {
        $url = "{$this->baseUrl}/device/reset";
        $data = [
            'deviceKey' => $deviceData['serial_number'],
            'secret' => $deviceData['device_secret'],
            'type' => $type
        ];

        try {
            $response = $this->makeApiRequest($url, $data);
            return [
                'success' => isset($response['success']) ? $response['success'] : false,
                'message' => isset($response['msg']) ? $response['msg'] : 'Unknown response from device',
                'code' => isset($response['code']) ? $response['code'] : '999'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'code' => '999'
            ];
        }
    }

    public function syncUsers($deviceData, $users) {
        $url = "{$this->baseUrl}/person/batchCreate";
        $data = [
            'deviceKey' => $deviceData['serial_number'],
            'secret' => $deviceData['device_secret'],
            'data' => json_encode($users)
        ];

        try {
            $response = $this->makeApiRequest($url, $data);
            return [
                'success' => isset($response['success']) ? $response['success'] : false,
                'message' => isset($response['msg']) ? $response['msg'] : 'Unknown response from device',
                'code' => isset($response['code']) ? $response['code'] : '999'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'code' => '999'
            ];
        }
    }

    public function setPersonnelUploadUrl($deviceData, $callbackUrl) {
        $url = "{$this->baseUrl}/device/setUploadUrl";
        $data = [
            'deviceKey' => $deviceData['serial_number'],
            'secret' => $deviceData['device_secret'],
            'data' => json_encode([
                [
                    'type' => 3, // Personnel data
                    'url' => $callbackUrl
                ]
            ])
        ];

        try {
            $response = $this->makeApiRequest($url, $data);
            return [
                'success' => isset($response['success']) ? $response['success'] : false,
                'message' => isset($response['msg']) ? $response['msg'] : 'Unknown response from device',
                'code' => isset($response['code']) ? $response['code'] : '999'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'code' => '999'
            ];
        }
    }
} 