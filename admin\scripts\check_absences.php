<?php
// This script should be run via cron job at the end of each workday
require_once('../session/Login.php');

$model = new Dashboard();
$connection = $model->TemporaryConnection();

// Get yesterday's date
$yesterday = date('Y-m-d', strtotime('-1 day'));

// Get all active employees
$employeeQuery = "SELECT id FROM employees";
$employeeResult = mysqli_query($connection, $employeeQuery);

// Get all employees who attended yesterday
$attendanceQuery = "SELECT DISTINCT employee_id FROM attendance WHERE date = '$yesterday'";
$attendanceResult = mysqli_query($connection, $attendanceQuery);

$attendedEmployees = array();
while ($row = mysqli_fetch_assoc($attendanceResult)) {
    $attendedEmployees[] = $row['employee_id'];
}

// Check each employee
while ($employee = mysqli_fetch_assoc($employeeResult)) {
    $employeeId = $employee['id'];
    
    // If employee didn't attend and doesn't already have an absence record
    if (!in_array($employeeId, $attendedEmployees)) {
        // Check if absence already recorded
        $checkQuery = "SELECT id FROM absences WHERE employee_id = '$employeeId' AND date = '$yesterday'";
        $checkResult = mysqli_query($connection, $checkQuery);
        
        if (mysqli_num_rows($checkResult) == 0) {
            // Record new absence
            $insertQuery = "INSERT INTO absences (employee_id, date, type) VALUES ('$employeeId', '$yesterday', 'Unexcused')";
            mysqli_query($connection, $insertQuery);
        }
    }
}

echo "Absence check completed for $yesterday";
?>
