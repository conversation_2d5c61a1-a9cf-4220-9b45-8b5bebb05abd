<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once('includes/script.php');
require_once('session/Login.php');
require_once('includes/DeviceManager.php');
require_once('includes/HeystarClient.php');

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

// Check if user is logged in
if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    header("location:index.php?utm_campaign=expired");
}

// Get session variables
$password = $_SESSION['official_password'];
$username = $_SESSION['official_username'];
$uid = $_SESSION['official_id'];

// Get admin information
$connection = $model->TemporaryConnection();
$query = $model->GetAdministrator($username, $password);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];

// Initialize DeviceManager
$deviceManager = new DeviceManager($connection);

// Handle device addition
if(isset($_POST['add_device'])) {
    try {
        $deviceData = [
            'device_name' => mysqli_real_escape_string($connection, $_POST['device_name']),
            'device_model' => mysqli_real_escape_string($connection, $_POST['device_model']),
            'location' => mysqli_real_escape_string($connection, $_POST['location']),
            'ip_address' => mysqli_real_escape_string($connection, $_POST['ip_address']),
            'port' => intval($_POST['port']),
            'serial_number' => mysqli_real_escape_string($connection, $_POST['serial_number']),
            'device_secret' => mysqli_real_escape_string($connection, $_POST['device_secret']),
            'added_by' => $id
        ];
        
        $deviceId = $deviceManager->addDevice($deviceData);
        $status = "success";
        $message = "Device added and connected successfully";
    } catch (Exception $e) {
        $status = "error";
        $message = "Error: " . $e->getMessage();
    }
}

// Handle device update
if(isset($_POST['update_device'])) {
    try {
        $deviceId = intval($_POST['device_id']);
        $deviceData = [
            'device_name' => mysqli_real_escape_string($connection, $_POST['device_name']),
            'device_model' => mysqli_real_escape_string($connection, $_POST['device_model']),
            'location' => mysqli_real_escape_string($connection, $_POST['location']),
            'ip_address' => mysqli_real_escape_string($connection, $_POST['ip_address']),
            'port' => intval($_POST['port']),
            'serial_number' => mysqli_real_escape_string($connection, $_POST['serial_number']),
            'device_secret' => mysqli_real_escape_string($connection, $_POST['device_secret']),
            'status' => mysqli_real_escape_string($connection, $_POST['status'])
        ];
        
        $deviceManager->updateDevice($deviceId, $deviceData);
        $status = "success";
        $message = "Device updated and reconnected successfully";
    } catch (Exception $e) {
        $status = "error";
        $message = "Error: " . $e->getMessage();
    }
}

// Handle device deletion
if(isset($_GET['delete']) && isset($_GET['id'])) {
    try {
        $deviceId = intval($_GET['id']);
        $deviceManager->deleteDevice($deviceId);
        $status = "success";
        $message = "Device deleted successfully";
    } catch (Exception $e) {
        $status = "error";
        $message = "Error: " . $e->getMessage();
    }
}

// Get all devices
$devices_query = "SELECT * FROM biometric_devices ORDER BY id";
$devices_result = mysqli_query($connection, $devices_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biometric Devices - Payroll Management System</title>
    
    
    <!-- Required JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="page">
        <div class="page-main" id="app">
        <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="d-flex">
                            <?php require_once('includes/header.php') ?>
                        </div>
                        <div class="col-lg order-lg-first">
                            <?php require_once('includes/subheader.php') ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="my-3 my-md-5">
                <div class="container">
                    <div class="page-header">
                        <h1 class="page-title">Biometric Devices</h1>
                        <div class="page-options d-flex">
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addDeviceModal">
                                <i class="fe fe-plus mr-2"></i> Add Device
                            </button>
                        </div>
                    </div>
                    
                    <?php if(isset($status) && isset($message)): ?>
                        <div class="alert alert-<?php echo $status == 'success' ? 'success' : 'danger'; ?> alert-dismissible">
                            <button type="button" class="close" data-dismiss="alert"></button>
                            <?php echo $message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row row-cards">
                        <?php if(mysqli_num_rows($devices_result) > 0): ?>
                            <?php while($device = mysqli_fetch_assoc($devices_result)): ?>
                                <?php
                                try {
                                    $deviceInfo = $deviceManager->getDeviceInfo($device['id']);
                                    $device = array_merge($device, $deviceInfo);
                                } catch (Exception $e) {
                                    // If we can't get live info, just use the database info
                                }
                                ?>
                                <div class="col-md-6 col-lg-4">
                                    <div class="card">
                                        <div class="card-status <?php echo $device['status'] == 'active' ? 'bg-green' : 'bg-red'; ?>"></div>
                                        <div class="card-header">
                                            <h3 class="card-title"><?php echo $device['device_name']; ?></h3>
                                            <div class="card-options">
                                                <a href="#" class="btn btn-sm btn-outline-primary mr-2" data-toggle="modal" 
                                                   data-target="#editDeviceModal" 
                                                   data-id="<?php echo $device['id']; ?>"
                                                   data-name="<?php echo $device['device_name']; ?>"
                                                   data-model="<?php echo $device['device_model']; ?>"
                                                   data-location="<?php echo $device['location']; ?>"
                                                   data-ip="<?php echo $device['ip_address']; ?>"
                                                   data-port="<?php echo $device['port']; ?>"
                                                   data-serial="<?php echo $device['serial_number']; ?>"
                                                   data-secret="<?php echo $device['device_secret']; ?>"
                                                   data-status="<?php echo $device['status']; ?>">
                                                    <i class="fe fe-edit-2"></i>
                                                </a>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                                                        <i class="fe fe-more-vertical"></i>
                                                    </button>
                                                    <div class="dropdown-menu dropdown-menu-right">
                                                        <a href="biometric_config.php?id=<?php echo $device['id']; ?>" class="dropdown-item">
                                                            <i class="dropdown-icon fe fe-settings"></i> Configure
                                                        </a>
                                                        <a href="?delete=1&id=<?php echo $device['id']; ?>" class="dropdown-item text-danger"
                                                           onclick="return confirm('Are you sure you want to delete this device?');">
                                                            <i class="dropdown-icon fe fe-trash-2"></i> Delete
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-4">
                                                    <div class="text-center">
                                                        <i class="fe fe-monitor fa-3x text-primary mb-2"></i>
                                                        <div class="h5"><?php echo ucfirst($device['status']); ?></div>
                                                        <div class="text-muted">Status</div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="text-center">
                                                        <i class="fe fe-map-pin fa-3x text-info mb-2"></i>
                                                        <div class="h5"><?php echo $device['location']; ?></div>
                                                        <div class="text-muted">Location</div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="text-center">
                                                        <i class="fe fe-info fa-3x text-success mb-2"></i>
                                                        <div class="h5"><?php echo $device['device_model']; ?></div>
                                                        <div class="text-muted">Model</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <small class="text-muted">IP Address</small>
                                                        <div><?php echo $device['ip_address']; ?></div>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">Port</small>
                                                        <div><?php echo $device['port']; ?></div>
                                                    </div>
                                                </div>
                                                <div class="mt-2">
                                                    <small class="text-muted">Serial Number</small>
                                                    <div><?php echo $device['serial_number']; ?></div>
                                                </div>
                                                <?php if(isset($device['firmware_version'])): ?>
                                                <div class="mt-2">
                                                    <small class="text-muted">Firmware Version</small>
                                                    <div><?php echo $device['firmware_version']; ?></div>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if($device['last_heartbeat']): ?>
                                                <div class="mt-2">
                                                    <small class="text-muted">Last Heartbeat</small>
                                                    <div><?php echo date('M d, Y H:i', strtotime($device['last_heartbeat'])); ?></div>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="alert alert-info">
                                    No biometric devices found. Click "Add Device" to add a new device.
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                    </div>
                </div>
            </div>
        
        <!-- Add Device Modal -->
        <div class="modal fade" id="addDeviceModal" tabindex="-1" role="dialog" aria-labelledby="addDeviceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                    <h5 class="modal-title" id="addDeviceModalLabel">Add New Device</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <form method="POST" action="">
                        <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                            <div class="form-group">
                                    <label class="form-label">Device Name</label>
                                <input type="text" class="form-control" name="device_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Device Model</label>
                                    <input type="text" class="form-control" name="device_model" value="SMT3568" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Location</label>
                                    <input type="text" class="form-control" name="location" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                            <div class="form-group">
                                    <label class="form-label">IP Address</label>
                                <input type="text" class="form-control" name="ip_address" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                            <div class="form-group">
                                    <label class="form-label">Port</label>
                                    <input type="number" class="form-control" name="port" value="8081" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Serial Number</label>
                                    <input type="text" class="form-control" name="serial_number" required>
                                </div>
                            </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Device Secret</label>
                            <input type="text" class="form-control" name="device_secret" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="submit" name="add_device" class="btn btn-primary">Add Device</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Edit Device Modal -->
    <div class="modal fade" id="editDeviceModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                    <h5 class="modal-title">Edit Device</h5>
                    <button type="button" class="close" data-dismiss="modal"></button>
                    </div>
                <form method="POST">
                    <input type="hidden" name="device_id" id="edit_device_id">
                        <div class="modal-body">
                        <div class="form-group">
                            <label class="form-label">Device Name</label>
                            <input type="text" class="form-control" name="device_name" id="edit_device_name" required>
                        </div>
                            <div class="form-group">
                            <label class="form-label">Device Model</label>
                            <input type="text" class="form-control" name="device_model" id="edit_device_model" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Location</label>
                            <input type="text" class="form-control" name="location" id="edit_location" required>
                            </div>
                            <div class="form-group">
                            <label class="form-label">IP Address</label>
                            <input type="text" class="form-control" name="ip_address" id="edit_ip_address" required>
                            </div>
                            <div class="form-group">
                            <label class="form-label">Port</label>
                            <input type="number" class="form-control" name="port" id="edit_port" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Serial Number</label>
                            <input type="text" class="form-control" name="serial_number" id="edit_serial_number" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Device Secret</label>
                            <input type="text" class="form-control" name="device_secret" id="edit_device_secret" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Status</label>
                            <select class="form-control" name="status" id="edit_status">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="maintenance">Maintenance</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="submit" name="update_device" class="btn btn-primary">Update Device</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    
    <script>
        $(document).ready(function() {
            console.log('Document ready - initializing modals');
            
            // Initialize Bootstrap components
            $('[data-toggle="tooltip"]').tooltip();
            $('[data-toggle="popover"]').popover();
            
            // Debug modal initialization
            console.log('Add Device Modal:', $('#addDeviceModal').length);
            console.log('Edit Device Modal:', $('#editDeviceModal').length);
            
            // Handle add device button click
            $('.btn-primary[data-target="#addDeviceModal"]').on('click', function(e) {
                console.log('Add Device button clicked');
                e.preventDefault();
                $('#addDeviceModal').modal('show');
            });
            
            // Handle add device modal events
            $('#addDeviceModal').on('show.bs.modal', function () {
                console.log('Add Device modal showing');
            });
            
            $('#addDeviceModal').on('shown.bs.modal', function () {
                console.log('Add Device modal shown');
                $('input[name="device_name"]').focus();
            });
            
            // Handle edit modal data population
            $('#editDeviceModal').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget);
                var modal = $(this);
                modal.find('#edit_device_id').val(button.data('id'));
                modal.find('#edit_device_name').val(button.data('name'));
                modal.find('#edit_device_model').val(button.data('model'));
                modal.find('#edit_location').val(button.data('location'));
                modal.find('#edit_ip_address').val(button.data('ip'));
                modal.find('#edit_port').val(button.data('port'));
                modal.find('#edit_serial_number').val(button.data('serial'));
                modal.find('#edit_device_secret').val(button.data('secret'));
                modal.find('#edit_status').val(button.data('status'));
            });
        });
    </script>
</body>
</html>

