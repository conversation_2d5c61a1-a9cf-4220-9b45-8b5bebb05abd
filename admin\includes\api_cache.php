<?php
/**
 * API Cache Manager
 * Implements caching for API calls to improve performance
 */

class ApiCache {
    private $cacheDir;
    private $defaultTTL = 300; // 5 minutes default cache time
    
    public function __construct() {
        $this->cacheDir = __DIR__ . '/../cache/';
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * Get cached data or fetch from API
     */
    public function get($key, $callback, $ttl = null) {
        $ttl = $ttl ?: $this->defaultTTL;
        $cacheFile = $this->cacheDir . md5($key) . '.cache';
        
        // Check if cache exists and is valid
        if (file_exists($cacheFile)) {
            $cacheData = json_decode(file_get_contents($cacheFile), true);
            if ($cacheData && time() - $cacheData['timestamp'] < $ttl) {
                return $cacheData['data'];
            }
        }
        
        // Cache miss or expired, fetch new data
        $data = $callback();
        
        // Store in cache
        $cacheData = [
            'timestamp' => time(),
            'data' => $data
        ];
        file_put_contents($cacheFile, json_encode($cacheData));
        
        return $data;
    }
    
    /**
     * Clear specific cache entry
     */
    public function clear($key) {
        $cacheFile = $this->cacheDir . md5($key) . '.cache';
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
        }
    }
    
    /**
     * Clear all cache
     */
    public function clearAll() {
        $files = glob($this->cacheDir . '*.cache');
        foreach ($files as $file) {
            unlink($file);
        }
    }
    
    /**
     * Get cache statistics
     */
    public function getStats() {
        $files = glob($this->cacheDir . '*.cache');
        $totalSize = 0;
        $validCount = 0;
        $expiredCount = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            $cacheData = json_decode(file_get_contents($file), true);
            if ($cacheData && time() - $cacheData['timestamp'] < $this->defaultTTL) {
                $validCount++;
            } else {
                $expiredCount++;
            }
        }
        
        return [
            'total_files' => count($files),
            'valid_cache' => $validCount,
            'expired_cache' => $expiredCount,
            'total_size' => $totalSize
        ];
    }
}

/**
 * Optimized API functions with caching
 */

// Initialize cache
$apiCache = new ApiCache();

/**
 * Fast cached API employee fetch
 */
function fetchApiEmployeesCached($page = 1, $per_page = 50, $useCache = true) {
    global $apiCache;
    
    $cacheKey = "api_employees_{$page}_{$per_page}";
    
    if (!$useCache) {
        return fetchApiEmployeesOriginal($page, $per_page);
    }
    
    return $apiCache->get($cacheKey, function() use ($page, $per_page) {
        return fetchApiEmployeesOriginal($page, $per_page);
    }, 300); // 5 minute cache
}

/**
 * Get total employee count (cached)
 */
function getApiEmployeeCountCached($useCache = true) {
    global $apiCache;
    
    $cacheKey = "api_employee_count";
    
    if (!$useCache) {
        $response = fetchApiEmployeesOriginal(1, 1);
        return $response['total'] ?? 0;
    }
    
    return $apiCache->get($cacheKey, function() {
        $response = fetchApiEmployeesOriginal(1, 1);
        return $response['total'] ?? 0;
    }, 300);
}

/**
 * Original API function (renamed)
 */
function fetchApiEmployeesOriginal($page = 1, $per_page = 50) {
    // Ensure per_page is at least 1 to avoid division by zero
    $per_page = max(1, (int)$per_page);
    $page = max(1, (int)$page);

    $apiUrl = "http://196.189.151.125:8080/api/HRMAPI/get_employee?page=$page&pageSize=$per_page";
    
    $data = array();
    $total = 0;
    $current_page = $page;
    $total_pages = 1;
    
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Reduced timeout
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // Connection timeout
        curl_setopt($ch, CURLOPT_USERAGENT, 'ERP-System/1.0');
        
        $response = curl_exec($ch);
        
        if(curl_errno($ch)) {
            error_log("cURL Error: " . curl_error($ch));
        } else {
            $decoded = json_decode($response, true);
            
            if(json_last_error() === JSON_ERROR_NONE && isset($decoded['data'])) {
                $data = $decoded['data'];
                $total = isset($decoded['total']) ? (int)$decoded['total'] : count($data);
                $current_page = isset($decoded['current_page']) ? (int)$decoded['current_page'] : $page;
                $total_pages = max(1, ceil($total / $per_page));
            } else {
                error_log("JSON decode error or missing data field in API response");
            }
        }
        curl_close($ch);
    } else {
        // Fallback to file_get_contents with timeout
        $context = stream_context_create([
            'http' => [
                'ignore_errors' => true,
                'timeout' => 10,
                'user_agent' => 'ERP-System/1.0'
            ]
        ]);
        
        $response = file_get_contents($apiUrl, false, $context);
        
        if($response !== false) {
            $decoded = json_decode($response, true);
            
            if(json_last_error() === JSON_ERROR_NONE && isset($decoded['data'])) {
                $data = $decoded['data'];
                $total = isset($decoded['total']) ? (int)$decoded['total'] : count($data);
                $current_page = isset($decoded['current_page']) ? (int)$decoded['current_page'] : $page;
                $total_pages = max(1, ceil($total / $per_page));
            } else {
                error_log("JSON decode error or missing data field in API response");
            }
        } else {
            error_log("Failed to get response from API for URL: " . $apiUrl);
        }
    }
    
    return [
        'data' => $data,
        'total' => $total,
        'current_page' => $current_page,
        'per_page' => $per_page,
        'total_pages' => $total_pages
    ];
}

/**
 * Clear API cache (call when data needs to be refreshed)
 */
function clearApiCache() {
    global $apiCache;
    $apiCache->clearAll();
}

/**
 * Preload critical data
 */
function preloadCriticalData() {
    // Preload first page of employees
    fetchApiEmployeesCached(1, 50);
    
    // Preload employee count
    getApiEmployeeCountCached();
}
?>
