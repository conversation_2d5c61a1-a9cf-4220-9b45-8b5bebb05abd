[{"name": "read_data", "file_name": "/Users/<USER>/Maps/ne_10m_admin_1_states_provinces_lakes/ne_10m_admin_1_states_provinces_lakes.shp"}, {"name": "remove", "where": "iso_a2 != 'RU' or objectid_1 == '3637'"}, {"name": "join_data", "data": [["RU-AD", "2498", "Adygeya, Respublika"], ["RU-AL", "2544", "Altay, Respublika"], ["RU-ALT", "2543", "Altayskiy kray"], ["RU-AMU", "2718", "Amurskaya oblast'"], ["RU-ARK", "2486", "Arkhangel'skaya oblast'"], ["RU-AST", "2221", "Astrakhanskaya oblast'"], ["RU-BA", "2523", "Bashkortostan, Respublika"], ["RU-BEL", "2515", "Belgorodskaya oblast'"], ["RU-BRY", "2505", "Bryanskaya oblast'"], ["RU-BU", "2724", "Buryatiya, Respublika"], ["RU-CE", "2534", "Chechenskaya Respublika"], ["RU-CHE", "2535", "Chelyabinskaya oblast'"], ["RU-CHU", "2485", "Chukotskiy avtonomnyy okrug"], ["RU-CU", "2528", "Chuvashskaya Respublika"], ["RU-DA", "2185", "Dagestan, Respublika"], ["RU-IN", "2490", "Ingushetiya, Respublika"], ["RU-IRK", "2721", "Irkutskaya oblast'"], ["RU-IVA", "2491", "Ivanovskaya oblast'"], ["RU-KB", "2500", "Kabardino-Balkarskaya Respublika"], ["RU-KGD", "2362", "Kaliningradskaya oblast'"], ["RU-KL", "2529", "Kalmykiya, Respublika"], ["RU-KLU", "2497", "Kaluzhskaya oblast'"], ["RU-KAM", "3561", "Kamchatskiy kray"], ["RU-KC", "2499", "Karachayevo-Cherkesskaya Respublika"], ["RU-KR", "2484", "Kareliya, Respublika"], ["RU-KEM", "2545", "Kemerovskaya oblast'"], ["RU-KHA", "2709", "Khabarovskiy kray"], ["RU-KK", "2546", "Khakasiya, Respublika"], ["RU-KHM", "2541", "Khanty-Mansiyskiy avtonomnyy okrug"], ["RU-KIR", "2525", "Kirovskaya oblast'"], ["RU-KO", "2524", "Komi, Respublika"], ["RU-KOS", "2492", "Kostromskaya oblast'"], ["RU-KDA", "2516", "Krasnodarskiy kray"], ["RU-KYA", "2722", "Krasnoyarskiy kray"], ["RU-KGN", "2536", "Kurganskaya oblast'"], ["RU-KRS", "2507", "Kurskaya oblast'"], ["RU-LEN", "2482", "Leningradskaya oblast'"], ["RU-LIP", "2508", "Lipetskaya oblast'"], ["RU-MAG", "2716", "Magadanskaya oblast'"], ["RU-ME", "2526", "Mariy El, Respublika"], ["RU-MO", "2517", "Mordoviya, Respublika"], ["RU-MOS", "2509", "Moskovskaya oblast'"], ["RU-MOW", "2510", "<PERSON><PERSON><PERSON>"], ["RU-MUR", "2488", "Murmanskaya oblast'"], ["RU-NEN", "2489", "Nenetskiy avtonomnyy okrug"], ["RU-NIZ", "2493", "Nizhegorodskaya oblast'"], ["RU-NGR", "2503", "Novgorodskaya oblast'"], ["RU-NVS", "2547", "Novosibirskaya oblast'"], ["RU-OMS", "2542", "Omskaya oblast'"], ["RU-ORE", "2538", "Orenburgskaya oblast'"], ["RU-ORL", "2511", "Orlovskaya oblast'"], ["RU-PNZ", "2518", "Penzenskaya oblast'"], ["RU-PER", "3284", "Permskiy kray"], ["RU-PRI", "2689", "Primorskiy kray"], ["RU-PSK", "2504", "Pskovskaya oblast'"], ["RU-ROS", "2512", "Rostovskaya oblast'"], ["RU-RYA", "2519", "Ryazanskaya oblast'"], ["RU-SA", "2717", "Sakha, Respublika"], ["RU-SAK", "2551", "Sakhalinskaya oblast'"], ["RU-SAM", "2530", "Samarskaya oblast'"], ["RU-SPE", "2481", "Sankt-Peterburg"], ["RU-SAR", "2531", "Saratovskaya oblast'"], ["RU-SE", "2501", "Severnaya Osetiya-Alaniya, Respublika"], ["RU-SMO", "2506", "Smolenskaya oblast'"], ["RU-STA", "2502", "Stavropol'skiy kray"], ["RU-SVE", "2537", "Sverdlovskaya oblast'"], ["RU-TAM", "2520", "Tambovskaya oblast'"], ["RU-TA", "2532", "Tatarstan, Respublika"], ["RU-TOM", "2540", "Tomskaya oblast'"], ["RU-TUL", "2513", "Tul'skaya oblast'"], ["RU-TVE", "2494", "Tverskaya oblast'"], ["RU-TYU", "2539", "Tyumenskaya oblast'"], ["RU-TY", "2723", "Tyva, Respublika [Tuva]"], ["RU-UD", "2527", "Udmurtskaya Respublika"], ["RU-ULY", "2533", "Ul'yanovskaya oblast'"], ["RU-VLA", "2521", "Vladimirskaya oblast'"], ["RU-VGG", "2514", "Volgogradskaya oblast'"], ["RU-VLG", "2495", "Vologodskaya oblast'"], ["RU-VOR", "2522", "Voronezhskaya oblast'"], ["RU-YAN", "2487", "Yamalo-Nenetskiy avtonomnyy okrug"], ["RU-YAR", "2496", "Yaroslavskaya oblast'"], ["RU-YEV", "2719", "Yevreyskaya avtonomnaya oblast'"], ["RU-ZAB", "2727", "Zabaykal'skiy kray"]], "fields": [{"name": "iso_3166_2", "type": 4, "width": 10}, {"name": "OBJECTID_1", "type": 4, "width": 9}, {"name": "name", "type": 4, "width": 100}], "on": "OBJECTID_1"}, {"name": "remove_other_fields", "fields": ["iso_3166_2", "name"]}, {"name": "write_data", "format": "jvectormap", "file_name": "russia.js", "params": {"name_field": "name", "code_field": "iso_3166_2", "name": "ru", "longitude0": 11.5}}]