{"name": "jquery-knob", "version": "1.2.13", "description": "Nice, downward compatible, touchable, jQuery dial", "main": "dist/jquery.knob.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/aterrien/jQuery-Knob.git"}, "keywords": ["j<PERSON>y", "knob", "dial"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/aterrien/jQuery-Knob/issues"}, "homepage": "https://github.com/aterrien/jQuery-Knob#readme"}