/* Polish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['pl'] = {
	closeText: 'Zamknij',
	prevText: '&#x3C;Poprzedni',
	nextText: 'Następny&#x3E;',
	currentText: 'D<PERSON><PERSON>',
	monthNames: ['<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>wi<PERSON>',
	'<PERSON>pie<PERSON>','<PERSON>erpie<PERSON>','Wrzesi<PERSON><PERSON>','Październik','Listopad','<PERSON><PERSON><PERSON><PERSON>'],
	monthNamesShort: ['<PERSON><PERSON>','<PERSON>','<PERSON>','<PERSON>w','<PERSON>','<PERSON><PERSON>',
	'<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>'],
	dayNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>d<PERSON><PERSON><PERSON>','Wtorek','Środa','Czwartek','Piątek','Sobota'],
	dayNamesShort: ['Nie','Pn','Wt','Śr','Czw','Pt','So'],
	dayNamesMin: ['N','Pn','Wt','Śr','Cz','Pt','So'],
	weekHeader: 'Tydz',
	dateFormat: 'dd.mm.yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['pl']);

return datepicker.regional['pl'];

}));
