<?php
require_once('includes/script.php');

$model = new Dashboard();
$connection = $model->TemporaryConnection();

$employee_id = $_POST['employee_id'];
$from = $_POST['from'];
$to = $_POST['to'];

// Get all dates in range
$dates = [];
$current = strtotime($from);
$end = strtotime($to);
while ($current <= $end) {
    $dates[] = date('Y-m-d', $current);
    $current = strtotime('+1 day', $current);
}

// Get attendance data
$attSql = "SELECT date, status_morning, status_afternoon, num_hr_morning, num_hr_afternoon 
           FROM attendance 
           WHERE employee_id = '$employee_id' AND date BETWEEN '$from' AND '$to'
           ORDER BY date";
$attQuery = mysqli_query($connection, $attSql);

$attendance = [];
while ($row = mysqli_fetch_assoc($attQuery)) {
    $attendance[$row['date']] = [
        'morning' => $row['status_morning'],
        'afternoon' => $row['status_afternoon'],
        'morning_hours' => $row['num_hr_morning'],
        'afternoon_hours' => $row['num_hr_afternoon']
    ];
}

// Prepare response
$response = [
    'dates' => $dates,
    'attendance' => $attendance
];

header('Content-Type: application/json');
echo json_encode($response);
?> 