<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);

require_once('includes/script.php');
require_once('session/Login.php');

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

// Check if user is logged in
if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    header("location:index.php?utm_campaign=expired");
}

// Get session variables
$password = $_SESSION['official_password'];
$username = $_SESSION['official_username'];
$uid = $_SESSION['official_id'];

// Get admin information
$connection = $model->TemporaryConnection();
$query = $model->GetAdministrator($username, $password);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];

// Set default date range (current month)
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t');
$employee_id = isset($_GET['employee_id']) ? $_GET['employee_id'] : '';
$device_id = isset($_GET['device_id']) ? $_GET['device_id'] : '';

// Build a very simple query without joins to start with
$query = "SELECT a.attendance_id, a.employee_id, a.employee_id AS employee_name, 
          a.date, a.time_in, a.time_out, a.status, a.num_hr, 'Manual' AS device_name
          FROM attendance a
          WHERE a.date BETWEEN '$start_date' AND '$end_date'";

if(!empty($employee_id)) {
    $query .= " AND a.employee_id = '$employee_id'";
}

$query .= " ORDER BY a.date DESC, a.time_in ASC";
$result = mysqli_query($connection, $query);

// Get employees for filter - simplify this query too
$employees_query = "SELECT id, id AS employee_id, id AS name FROM attendance GROUP BY employee_id ORDER BY employee_id";
$employees_result = mysqli_query($connection, $employees_query);

// Get devices for filter
$devices_query = "SELECT id, name FROM biometric_devices ORDER BY id";
$devices_result = mysqli_query($connection, $devices_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php require_once('includes/head.php'); ?>
    <title>Biometric Attendance Reports - Payroll Management System</title>
</head>
<body>
    <div class="page">
        <div class="page-main" id="app">
            <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
              <div class="container">
                <div class="row align-items-center">
                  <div class="d-flex">
                    <?php require_once('includes/header.php') ?>
                  </div>
                  <div class="col-lg order-lg-first">
                    <?php require_once('includes/subheader.php') ?> 
                  </div>
                </div>
              </div>
            </div>
            <div class="my-3 my-md-5">
                <div class="container">
                    <div class="page-header">
                        <h1 class="page-title">Biometric Attendance Reports</h1>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Filter Options</h3>
                                </div>
                                <div class="card-body">
                                    <form action="" method="get">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label class="form-label">Date Range</label>
                                                    <input type="text" class="form-control" id="date-range" name="date_range">
                                                    <input type="hidden" id="start-date" name="start_date" value="<?php echo $start_date; ?>">
                                                    <input type="hidden" id="end-date" name="end_date" value="<?php echo $end_date; ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Employee</label>
                                                    <select class="form-control" name="employee_id">
                                                        <option value="">All Employees</option>
                                                        <?php while($employee = mysqli_fetch_assoc($employees_result)): ?>
                                                            <option value="<?php echo $employee['employee_id']; ?>" <?php if($employee_id == $employee['employee_id']) echo 'selected'; ?>>
                                                                <?php echo $employee['name']; ?> (<?php echo $employee['employee_id']; ?>)
                                                            </option>
                                                        <?php endwhile; ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Device</label>
                                                    <select class="form-control" name="device_id">
                                                        <option value="">All Devices</option>
                                                        <?php while($device = mysqli_fetch_assoc($devices_result)): ?>
                                                            <option value="<?php echo $device['id']; ?>" <?php if($device_id == $device['id']) echo 'selected'; ?>>
                                                                <?php echo $device['name']; ?>
                                                            </option>
                                                        <?php endwhile; ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label class="form-label">&nbsp;</label>
                                                    <button type="submit" class="btn btn-primary btn-block">Apply Filter</button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Attendance Records</h3>
                                    <div class="card-options">
                                        <button class="btn btn-secondary" onclick="exportTableToCSV('attendance_report.csv')">
                                            <i class="fe fe-download mr-2"></i> Export CSV
                                        </button>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table card-table table-vcenter text-nowrap" id="attendance-table">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Employee ID</th>
                                                <th>Name</th>
                                                <th>Time In</th>
                                                <th>Time Out</th>
                                                <th>Hours</th>
                                                <th>Status</th>
                                                <th>Device</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            if(mysqli_num_rows($result) > 0) {
                                                while($row = mysqli_fetch_assoc($result)) {
                                                    echo '<tr>';
                                                    echo '<td>' . $row['date'] . '</td>';
                                                    echo '<td>' . $row['employee_id'] . '</td>';
                                                    echo '<td>' . $row['employee_name'] . '</td>';
                                                    echo '<td>' . $row['time_in'] . '</td>';
                                                    echo '<td>' . $row['time_out'] . '</td>';
                                                    echo '<td>' . $row['num_hr'] . '</td>';
                                                    echo '<td>';
                                                    if($row['status'] == 'in') {
                                                        echo '<span class="badge badge-success">In</span>';
                                                    } else {
                                                        echo '<span class="badge badge-danger">Out</span>';
                                                    }
                                                    echo '</td>';
                                                    echo '<td>' . ($row['device_name'] ? $row['device_name'] : 'Manual') . '</td>';
                                                    echo '</tr>';
                                                }
                                            } else {
                                                echo '<tr><td colspan="8" class="text-center">No records found</td></tr>';
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <?php require_once('includes/footer.php'); ?>
    </div>
    
    <?php require_once('includes/datatables.php'); ?>
    <script src="assets/js/moment.min.js"></script>
    <script src="assets/js/daterangepicker.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#attendance-table').DataTable({
                responsive: true,
                "pageLength": 25
            });
            
            // Initialize date range picker
            $('#date-range').daterangepicker({
                startDate: moment('<?php echo $start_date; ?>'),
                endDate: moment('<?php echo $end_date; ?>'),
                ranges: {
                   'Today': [moment(), moment()],
                   'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                   'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                   'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                   'This Month': [moment().startOf('month'), moment().endOf('month')],
                   'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            }, function(start, end) {
                $('#start-date').val(start.format('YYYY-MM-DD'));
                $('#end-date').val(end.format('YYYY-MM-DD'));
            });
        });
        
        // Function to export table to CSV
        function exportTableToCSV(filename) {
            var csv = [];
            var rows = document.querySelectorAll('#attendance-table tr');
            
            for (var i = 0; i < rows.length; i++) {
                var row = [], cols = rows[i].querySelectorAll('td, th');
                
                for (var j = 0; j < cols.length; j++) {
                    // Replace HTML tags and clean text
                    var text = cols[j].innerText.replace(/(\r\n|\n|\r)/gm, '').replace(/(\s\s)/gm, ' ');
                    row.push('"' + text + '"');
                }
                
                csv.push(row.join(','));
            }
            
            // Download CSV file
            downloadCSV(csv.join('\n'), filename);
        }
        
        function downloadCSV(csv, filename) {
            var csvFile;
            var downloadLink;
            
            // CSV file
            csvFile = new Blob([csv], {type: 'text/csv'});
            
            // Download link
            downloadLink = document.createElement('a');
            
            // File name
            downloadLink.download = filename;
            
            // Create a link to the file
            downloadLink.href = window.URL.createObjectURL(csvFile);
            
            // Hide download link
            downloadLink.style.display = 'none';
            
            // Add the link to DOM
            document.body.appendChild(downloadLink);
            
            // Click download link
            downloadLink.click();
            
            // Remove link from DOM
            document.body.removeChild(downloadLink);
        }
    </script>
</body>
</html>




