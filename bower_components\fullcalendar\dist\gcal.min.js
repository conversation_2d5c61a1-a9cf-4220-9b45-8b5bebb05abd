/*!
 * FullCalendar v3.5.1 Google Calendar Plugin
 * Docs & License: https://fullcalendar.io/
 * (c) 2017 Adam Shaw
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?module.exports=e(require("jquery")):e(jQuery)}(function(e){function r(e){var r;return/^[^\/]+@([^\/\.]+\.)*(google|googlemail|gmail)\.com$/.test(e)?e:(r=/^https:\/\/www.googleapis.com\/calendar\/v3\/calendars\/([^\/]*)/.exec(e))||(r=/^https?:\/\/www.google.com\/calendar\/feeds\/([^\/]*)/.exec(e))?decodeURIComponent(r[1]):void 0}function a(e,r){return e.replace(/(\?.*?)?(#|$)/,function(e,a,t){return(a?a+"&":"?")+r+t})}var t=e.fullCalendar,o=t.Promise,n=t.EventSource,l=t.JsonFeedEventSource,i=t.EventSourceParser,s=t.applyAll,c=n.extend({googleCalendarApiKey:null,googleCalendarId:null,googleCalendarError:null,ajaxSettings:null,fetch:function(r,a,t){var n=this,i=this.buildUrl(),c=this.buildRequestParams(r,a,t),u=this.ajaxSettings,d=u.success;return c?o.construct(function(r,a){e.ajax(e.extend({},l.AJAX_DEFAULTS,u,{url:i,data:c,success:function(t){var o,l;t.error?(n.reportError("Google Calendar API: "+t.error.message,t.error.errors),a()):t.items&&(o=n.gcalItemsToRawEventDefs(t.items,c.timeZone),l=s(d,this,[o].concat(Array.prototype.slice.call(arguments,1))),e.isArray(l)&&(o=l),r(n.parseEventDefs(o)))}}))}):o.reject()},gcalItemsToRawEventDefs:function(e,r){var a=this;return e.map(function(e){return a.gcalItemToRawEventDef(e,r)})},gcalItemToRawEventDef:function(e,r){var t=e.htmlLink||null;return t&&r&&(t=a(t,"ctz="+r)),{id:e.id,title:e.summary,start:e.start.dateTime||e.start.date,end:e.end.dateTime||e.end.date,url:t,location:e.location,description:e.description}},buildUrl:function(){return c.API_BASE+"/"+encodeURIComponent(this.googleCalendarId)+"/events?callback=?"},buildRequestParams:function(r,a,t){var o,n=this.googleCalendarApiKey||this.calendar.opt("googleCalendarApiKey");return n?(r.hasZone()||(r=r.clone().utc().add(-1,"day")),a.hasZone()||(a=a.clone().utc().add(1,"day")),o=e.extend(this.ajaxSettings.data||{},{key:n,timeMin:r.format(),timeMax:a.format(),singleEvents:!0,maxResults:9999}),t&&"local"!==t&&(o.timeZone=t.replace(" ","_")),o):(this.reportError("Specify a googleCalendarApiKey. See http://fullcalendar.io/docs/google_calendar/"),null)},reportError:function(e,r){var a=this.calendar,o=a.opt("googleCalendarError"),n=r||[{message:e}];this.googleCalendarError&&this.googleCalendarError.apply(a,n),o&&o.apply(a,n),t.warn.apply(null,[e].concat(r||[]))},getPrimitive:function(){return this.googleCalendarId},applyManualRawProps:function(e){var a=n.prototype.applyManualRawProps.apply(this,arguments),t=e.googleCalendarId;return null==t&&e.url&&(t=r(e.url)),null!=t&&(this.googleCalendarId=t,a)},applyOtherRawProps:function(e){this.ajaxSettings=e}});c.API_BASE="https://www.googleapis.com/calendar/v3/calendars",c.allowRawProps({url:!1,googleCalendarId:!1,googleCalendarApiKey:!0,googleCalendarError:!0}),c.parse=function(e,r){var a;return"object"==typeof e?a=e:"string"==typeof e&&(a={url:e}),!!a&&n.parse.call(this,a,r)},i.registerClass(c),t.GcalEventSource=c});