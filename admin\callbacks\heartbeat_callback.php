<?php
require_once '../includes/session.php';
require_once '../includes/conn.php';

// Get the JSON payload
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// Log the received data
file_put_contents('heartbeat_log.txt', date('Y-m-d H:i:s') . ' - ' . $json . PHP_EOL, FILE_APPEND);

// Validate the data
if (!$data || !isset($data['deviceKey'])) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Invalid data']);
    exit;
}

// Extract data
$device_key = $data['deviceKey'];

// Update device last heartbeat
$update = "UPDATE biometric_devices SET last_heartbeat = NOW() WHERE serial_number = '$device_key'";
if (mysqli_query($connection, $update)) {
    echo json_encode(['status' => 'success']);
} else {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Database error: ' . mysqli_error($connection)]);
}