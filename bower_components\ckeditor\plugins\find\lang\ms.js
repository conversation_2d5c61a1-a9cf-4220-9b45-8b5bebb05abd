﻿CKEDITOR.plugins.setLang("find","ms",{find:"Cari",findOptions:"Find Options",findWhat:"Perkataan yang dicari:",matchCase:"Padanan case huruf",matchCyclic:"Match cyclic",matchWord:"Padana <PERSON> perkataan",notFoundMsg:"Text yang dicari tidak dijumpai.",replace:"Ganti",replaceAll:"Ganti semua",replaceSuccessMsg:"%1 occurrence(s) replaced.",replaceWith:"<PERSON><PERSON><PERSON> dengan:",title:"Find and Replace"});