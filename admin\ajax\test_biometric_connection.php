<?php
require_once('../includes/script.php');
require_once('../session/Login.php');
require_once('../includes/BiometricSDK.php');

// Check if user is logged in via session
if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
    exit;
}

// Check if device ID is provided
if(!isset($_POST['device_id']) || empty($_POST['device_id'])) {
    echo json_encode(['status' => 'error', 'message' => 'Device ID is required']);
    exit;
}

$device_id = intval($_POST['device_id']);

// Get device information from database
$model = new Dashboard();
$connection = $model->TemporaryConnection();

$query = "SELECT * FROM biometric_devices WHERE id = $device_id";
$result = mysqli_query($connection, $query);

if(mysqli_num_rows($result) == 0) {
    echo json_encode(['status' => 'error', 'message' => 'Device not found']);
    exit;
}

$device = mysqli_fetch_assoc($result);

// Initialize BiometricSDK with device information
$sdk = new BiometricSDK(
    $device['ip_address'],
    $device['port'],
    5, // 5 seconds timeout
    $device['serial_number'],
    $device['device_secret']
);

// Test connection by getting device info
$response = $sdk->getDeviceInfo();

// Check response
if(isset($response['status']) && $response['status'] == 'error') {
    echo json_encode(['status' => 'error', 'message' => $response['message']]);
    exit;
}

// Update device firmware version if available
if(isset($response['firmware_version'])) {
    $firmware_version = mysqli_real_escape_string($connection, $response['firmware_version']);
    $update_query = "UPDATE biometric_devices SET firmware_version = '$firmware_version' WHERE id = $device_id";
    mysqli_query($connection, $update_query);
}

// Return success response
echo json_encode([
    'status' => 'success',
    'message' => 'Connection successful',
    'device_info' => $response
]);
