/*!
* inputmask.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2017 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.3.8
*/

!function(a){"function"==typeof define&&define.amd?define(["./dependencyLibs/inputmask.dependencyLib","./global/window","./global/document"],a):"object"==typeof exports?module.exports=a(require("./dependencyLibs/inputmask.dependencyLib"),require("./global/window"),require("./global/document")):window.Inputmask=a(window.dependencyLib||jQuery,window,document)}(function(a,b,c,d){function e(b,c,g){if(!(this instanceof e))return new e(b,c,g);this.el=d,this.events={},this.maskset=d,this.refreshValue=!1,!0!==g&&(a.isPlainObject(b)?c=b:(c=c||{},c.alias=b),this.opts=a.extend(!0,{},this.defaults,c),this.noMasksCache=c&&c.definitions!==d,this.userOptions=c||{},this.isRTL=this.opts.numericInput,f(this.opts.alias,c,this.opts))}function f(b,c,g){var h=e.prototype.aliases[b];return h?(h.alias&&f(h.alias,d,g),a.extend(!0,g,h),a.extend(!0,g,c),!0):(null===g.mask&&(g.mask=b),!1)}function g(b,c){function f(b,f,g){var h=!1;if(null!==b&&""!==b||(h=null!==g.regex,h?(b=g.regex,b=b.replace(/^(\^)(.*)(\$)$/,"$2")):(h=!0,b=".*")),1===b.length&&!1===g.greedy&&0!==g.repeat&&(g.placeholder=""),g.repeat>0||"*"===g.repeat||"+"===g.repeat){var i="*"===g.repeat?0:"+"===g.repeat?1:g.repeat;b=g.groupmarker.start+b+g.groupmarker.end+g.quantifiermarker.start+i+","+g.repeat+g.quantifiermarker.end}var j,k=h?"regex_"+g.regex:g.numericInput?b.split("").reverse().join(""):b;return e.prototype.masksCache[k]===d||!0===c?(j={mask:b,maskToken:e.prototype.analyseMask(b,h,g),validPositions:{},_buffer:d,buffer:d,tests:{},metadata:f,maskLength:d},!0!==c&&(e.prototype.masksCache[k]=j,j=a.extend(!0,{},e.prototype.masksCache[k]))):j=a.extend(!0,{},e.prototype.masksCache[k]),j}if(a.isFunction(b.mask)&&(b.mask=b.mask(b)),a.isArray(b.mask)){if(b.mask.length>1){b.keepStatic=null===b.keepStatic||b.keepStatic;var g=b.groupmarker.start;return a.each(b.numericInput?b.mask.reverse():b.mask,function(c,e){g.length>1&&(g+=b.groupmarker.end+b.alternatormarker+b.groupmarker.start),e.mask===d||a.isFunction(e.mask)?g+=e:g+=e.mask}),g+=b.groupmarker.end,f(g,b.mask,b)}b.mask=b.mask.pop()}return b.mask&&b.mask.mask!==d&&!a.isFunction(b.mask.mask)?f(b.mask.mask,b.mask,b):f(b.mask,b.mask,b)}function h(f,g,i){function n(a,b,c){b=b||0;var e,f,g,h=[],j=0,k=q();do{!0===a&&o().validPositions[j]?(g=o().validPositions[j],f=g.match,e=g.locator.slice(),h.push(!0===c?g.input:!1===c?f.nativeDef:I(j,f))):(g=t(j,e,j-1),f=g.match,e=g.locator.slice(),(!1===i.jitMasking||j<k||"number"==typeof i.jitMasking&&isFinite(i.jitMasking)&&i.jitMasking>j)&&h.push(!1===c?f.nativeDef:I(j,f))),j++}while((U===d||j<U)&&(null!==f.fn||""!==f.def)||b>j);return""===h[h.length-1]&&h.pop(),o().maskLength=j+1,h}function o(){return g}function p(a){var b=o();b.buffer=d,!0!==a&&(b.validPositions={},b.p=0)}function q(a,b,c){var e=-1,f=-1,g=c||o().validPositions;a===d&&(a=-1);for(var h in g){var i=parseInt(h);g[i]&&(b||!0!==g[i].generatedInput)&&(i<=a&&(e=i),i>=a&&(f=i))}return-1!==e&&a-e>1||f<a?e:f}function r(b,c,e,f){var g,h=b,j=a.extend(!0,{},o().validPositions),k=!1;for(o().p=b,g=c-1;g>=h;g--)o().validPositions[g]!==d&&(!0!==e&&(!o().validPositions[g].match.optionality&&function(a){var b=o().validPositions[a];if(b!==d&&null===b.match.fn){var c=o().validPositions[a-1],e=o().validPositions[a+1];return c!==d&&e!==d}return!1}(g)||!1===i.canClearPosition(o(),g,q(),f,i))||delete o().validPositions[g]);for(p(!0),g=h+1;g<=q();){for(;o().validPositions[h]!==d;)h++;if(g<h&&(g=h+1),o().validPositions[g]===d&&D(g))g++;else{var l=t(g);!1===k&&j[h]&&j[h].match.def===l.match.def?(o().validPositions[h]=a.extend(!0,{},j[h]),o().validPositions[h].input=l.input,delete o().validPositions[g],g++):v(h,l.match.def)?!1!==C(h,l.input||I(g),!0)&&(delete o().validPositions[g],g++,k=!0):D(g)||(g++,h--),h++}}p(!0)}function s(a,b){for(var c,e=a,f=q(),g=o().validPositions[f]||w(0)[0],h=g.alternation!==d?g.locator[g.alternation].toString().split(","):[],j=0;j<e.length&&(c=e[j],!(c.match&&(i.greedy&&!0!==c.match.optionalQuantifier||(!1===c.match.optionality||!1===c.match.newBlockMarker)&&!0!==c.match.optionalQuantifier)&&(g.alternation===d||g.alternation!==c.alternation||c.locator[g.alternation]!==d&&B(c.locator[g.alternation].toString().split(","),h)))||!0===b&&(null!==c.match.fn||/[0-9a-bA-Z]/.test(c.match.def)));j++);return c}function t(a,b,c){return o().validPositions[a]||s(w(a,b?b.slice():b,c))}function u(a){return o().validPositions[a]?o().validPositions[a]:w(a)[0]}function v(a,b){for(var c=!1,d=w(a),e=0;e<d.length;e++)if(d[e].match&&d[e].match.def===b){c=!0;break}return c}function w(b,c,e){function f(c,e,g,j){function l(g,j,q){function r(b,c){var d=0===a.inArray(b,c.matches);return d||a.each(c.matches,function(a,e){if(!0===e.isQuantifier&&(d=r(b,c.matches[a-1])))return!1}),d}function s(b,c,e){var f,g;if(o().validPositions[b-1]&&e&&o().tests[b])for(var h=o().validPositions[b-1].locator,i=o().tests[b][0].locator,j=0;j<e;j++)if(h[j]!==i[j])return h.slice(e+1);return(o().tests[b]||o().validPositions[b])&&a.each(o().tests[b]||[o().validPositions[b]],function(a,b){var h=e!==d?e:b.alternation,i=b.locator[h]!==d?b.locator[h].toString().indexOf(c):-1;(g===d||i<g)&&-1!==i&&(f=b,g=i)}),f?f.locator.slice((e!==d?e:f.alternation)+1):e!==d?s(b,c):d}if(k>1e4)throw"Inputmask: There is probably an error in your mask definition or in the code. Create an issue on github with an example of the mask you are using. "+o().mask;if(k===b&&g.matches===d)return m.push({match:g,locator:j.reverse(),cd:p}),!0;if(g.matches!==d){if(g.isGroup&&q!==g){if(g=l(c.matches[a.inArray(g,c.matches)+1],j))return!0}else if(g.isOptional){var t=g;if(g=f(g,e,j,q)){if(h=m[m.length-1].match,!r(h,t))return!0;n=!0,k=b}}else if(g.isAlternator){var u,v=g,w=[],x=m.slice(),y=j.length,z=e.length>0?e.shift():-1;if(-1===z||"string"==typeof z){var A,B=k,C=e.slice(),D=[];if("string"==typeof z)D=z.split(",");else for(A=0;A<v.matches.length;A++)D.push(A);for(var E=0;E<D.length;E++){if(A=parseInt(D[E]),m=[],e=s(k,A,y)||C.slice(),!0!==(g=l(v.matches[A]||c.matches[A],[A].concat(j),q)||g)&&g!==d&&D[D.length-1]<v.matches.length){var F=a.inArray(g,c.matches)+1;c.matches.length>F&&(g=l(c.matches[F],[F].concat(j.slice(1,j.length)),q))&&(D.push(F.toString()),a.each(m,function(a,b){b.alternation=j.length-1}))}u=m.slice(),k=B,m=[];for(var G=0;G<u.length;G++){var H=u[G],I=!1;H.alternation=H.alternation||y;for(var J=0;J<w.length;J++){var K=w[J];if("string"!=typeof z||-1!==a.inArray(H.locator[H.alternation].toString(),D)){if(function(a,b){return a.match.nativeDef===b.match.nativeDef||a.match.def===b.match.nativeDef||a.match.nativeDef===b.match.def}(H,K)){I=!0,H.alternation===K.alternation&&-1===K.locator[K.alternation].toString().indexOf(H.locator[H.alternation])&&(K.locator[K.alternation]=K.locator[K.alternation]+","+H.locator[H.alternation],K.alternation=H.alternation),H.match.nativeDef===K.match.def&&(H.locator[H.alternation]=K.locator[K.alternation],w.splice(w.indexOf(K),1,H));break}if(H.match.def===K.match.def){I=!1;break}if(function(a,c){return null===a.match.fn&&null!==c.match.fn&&c.match.fn.test(a.match.def,o(),b,!1,i,!1)}(H,K)||function(a,c){return null!==a.match.fn&&null!==c.match.fn&&c.match.fn.test(a.match.def.replace(/[\[\]]/g,""),o(),b,!1,i,!1)}(H,K)){H.alternation===K.alternation&&-1===H.locator[H.alternation].toString().indexOf(K.locator[K.alternation].toString().split("")[0])&&(H.na=H.na||H.locator[H.alternation].toString(),-1===H.na.indexOf(H.locator[H.alternation].toString().split("")[0])&&(H.na=H.na+","+H.locator[K.alternation].toString().split("")[0]),I=!0,H.locator[H.alternation]=K.locator[K.alternation].toString().split("")[0]+","+H.locator[H.alternation],w.splice(w.indexOf(K),0,H));break}}}I||w.push(H)}}"string"==typeof z&&(w=a.map(w,function(b,c){if(isFinite(c)){var e=b.alternation,f=b.locator[e].toString().split(",");b.locator[e]=d,b.alternation=d;for(var g=0;g<f.length;g++)-1!==a.inArray(f[g],D)&&(b.locator[e]!==d?(b.locator[e]+=",",b.locator[e]+=f[g]):b.locator[e]=parseInt(f[g]),b.alternation=e);if(b.locator[e]!==d)return b}})),m=x.concat(w),k=b,n=m.length>0,g=w.length>0,e=C.slice()}else g=l(v.matches[z]||c.matches[z],[z].concat(j),q);if(g)return!0}else if(g.isQuantifier&&q!==c.matches[a.inArray(g,c.matches)-1])for(var L=g,M=e.length>0?e.shift():0;M<(isNaN(L.quantifier.max)?M+1:L.quantifier.max)&&k<=b;M++){var N=c.matches[a.inArray(L,c.matches)-1];if(g=l(N,[M].concat(j),N)){if(h=m[m.length-1].match,h.optionalQuantifier=M>L.quantifier.min-1,r(h,N)){if(M>L.quantifier.min-1){n=!0,k=b;break}return!0}return!0}}else if(g=f(g,e,j,q))return!0}else k++}for(var q=e.length>0?e.shift():0;q<c.matches.length;q++)if(!0!==c.matches[q].isQuantifier){var r=l(c.matches[q],[q].concat(g),j);if(r&&k===b)return r;if(k>b)break}}function g(a){if(i.keepStatic&&b>0&&a.length>1+(""===a[a.length-1].match.def?1:0)&&!0!==a[0].match.optionality&&!0!==a[0].match.optionalQuantifier&&null===a[0].match.fn&&!/[0-9a-bA-Z]/.test(a[0].match.def)){if(o().validPositions[b-1]===d)return[s(a)];if(o().validPositions[b-1].alternation===a[0].alternation)return[s(a)];if(o().validPositions[b-1])return[s(a)]}return a}var h,j=o().maskToken,k=c?e:0,l=c?c.slice():[0],m=[],n=!1,p=c?c.join(""):"";if(b>-1){if(c===d){for(var q,r=b-1;(q=o().validPositions[r]||o().tests[r])===d&&r>-1;)r--;q!==d&&r>-1&&(l=function(b){var c=[];return a.isArray(b)||(b=[b]),b.length>0&&(b[0].alternation===d?(c=s(b.slice()).locator.slice(),0===c.length&&(c=b[0].locator.slice())):a.each(b,function(a,b){if(""!==b.def)if(0===c.length)c=b.locator.slice();else for(var d=0;d<c.length;d++)b.locator[d]&&-1===c[d].toString().indexOf(b.locator[d])&&(c[d]+=","+b.locator[d])})),c}(q),p=l.join(""),k=r)}if(o().tests[b]&&o().tests[b][0].cd===p)return g(o().tests[b]);for(var t=l.shift();t<j.length;t++){if(f(j[t],l,[t])&&k===b||k>b)break}}return(0===m.length||n)&&m.push({match:{fn:null,cardinality:0,optionality:!0,casing:null,def:"",placeholder:""},locator:[],cd:p}),c!==d&&o().tests[b]?g(a.extend(!0,[],m)):(o().tests[b]=a.extend(!0,[],m),g(o().tests[b]))}function x(){return o()._buffer===d&&(o()._buffer=n(!1,1),o().buffer===d&&(o().buffer=o()._buffer.slice())),o()._buffer}function y(a){return o().buffer!==d&&!0!==a||(o().buffer=n(!0,q(),!0)),o().buffer}function z(a,b,c){var e,f;if(!0===a)p(),a=0,b=c.length;else for(e=a;e<b;e++)delete o().validPositions[e];for(f=a,e=a;e<b;e++)if(p(!0),c[e]!==i.skipOptionalPartCharacter){var g=C(f,c[e],!0,!0);!1!==g&&(p(!0),f=g.caret!==d?g.caret:g.pos+1)}}function A(b,c,d){switch(i.casing||c.casing){case"upper":b=b.toUpperCase();break;case"lower":b=b.toLowerCase();break;case"title":var f=o().validPositions[d-1];b=0===d||f&&f.input===String.fromCharCode(e.keyCode.SPACE)?b.toUpperCase():b.toLowerCase();break;default:if(a.isFunction(i.casing)){var g=Array.prototype.slice.call(arguments);g.push(o().validPositions),b=i.casing.apply(this,g)}}return b}function B(b,c,e){for(var f,g=i.greedy?c:c.slice(0,1),h=!1,j=e!==d?e.split(","):[],k=0;k<j.length;k++)-1!==(f=b.indexOf(j[k]))&&b.splice(f,1);for(var l=0;l<b.length;l++)if(-1!==a.inArray(b[l],g)){h=!0;break}return h}function C(b,c,f,g,h,j){function k(a){var b=Y?a.begin-a.end>1||a.begin-a.end==1:a.end-a.begin>1||a.end-a.begin==1;return b&&0===a.begin&&a.end===o().maskLength?"full":b}function l(c,e,f){var h=!1;return a.each(w(c),function(j,l){for(var n=l.match,s=e?1:0,t="",u=n.cardinality;u>s;u--)t+=G(c-(u-1));if(e&&(t+=e),y(!0),!1!==(h=null!=n.fn?n.fn.test(t,o(),c,f,i,k(b)):(e===n.def||e===i.skipOptionalPartCharacter)&&""!==n.def&&{c:I(c,n,!0)||n.def,pos:c})){var v=h.c!==d?h.c:e;v=v===i.skipOptionalPartCharacter&&null===n.fn?I(c,n,!0)||n.def:v;var w=c,x=y();if(h.remove!==d&&(a.isArray(h.remove)||(h.remove=[h.remove]),a.each(h.remove.sort(function(a,b){return b-a}),function(a,b){r(b,b+1,!0)})),h.insert!==d&&(a.isArray(h.insert)||(h.insert=[h.insert]),a.each(h.insert.sort(function(a,b){return a-b}),function(a,b){C(b.pos,b.c,!0,g)})),h.refreshFromBuffer){var B=h.refreshFromBuffer;if(z(!0===B?B:B.start,B.end,x),h.pos===d&&h.c===d)return h.pos=q(),!1;if((w=h.pos!==d?h.pos:c)!==c)return h=a.extend(h,C(w,v,!0,g)),!1}else if(!0!==h&&h.pos!==d&&h.pos!==c&&(w=h.pos,z(c,w,y().slice()),w!==c))return h=a.extend(h,C(w,v,!0)),!1;return(!0===h||h.pos!==d||h.c!==d)&&(j>0&&p(!0),m(w,a.extend({},l,{input:A(v,n,w)}),g,k(b))||(h=!1),!1)}}),h}function m(b,c,e,f){if(f||i.insertMode&&o().validPositions[b]!==d&&e===d){var g,h=a.extend(!0,{},o().validPositions),j=q(d,!0);for(g=b;g<=j;g++)delete o().validPositions[g];o().validPositions[b]=a.extend(!0,{},c);var k,l=!0,m=o().validPositions,r=!1,s=o().maskLength;for(g=k=b;g<=j;g++){var t=h[g];if(t!==d)for(var u=k;u<o().maskLength&&(null===t.match.fn&&m[g]&&(!0===m[g].match.optionalQuantifier||!0===m[g].match.optionality)||null!=t.match.fn);){if(u++,!1===r&&h[u]&&h[u].match.def===t.match.def)o().validPositions[u]=a.extend(!0,{},h[u]),o().validPositions[u].input=t.input,n(u),k=u,l=!0;else if(v(u,t.match.def)){var w=C(u,t.input,!0,!0);l=!1!==w,k=w.caret||w.insert?q():u,r=!0}else if(!(l=!0===t.generatedInput)&&u>=o().maskLength-1)break;if(o().maskLength<s&&(o().maskLength=s),l)break}if(!l)break}if(!l)return o().validPositions=a.extend(!0,{},h),p(!0),!1}else o().validPositions[b]=a.extend(!0,{},c);return p(!0),!0}function n(b){for(var c=b-1;c>-1&&!o().validPositions[c];c--);var e,f;for(c++;c<b;c++)o().validPositions[c]===d&&(!1===i.jitMasking||i.jitMasking>c)&&(f=w(c,t(c-1).locator,c-1).slice(),""===f[f.length-1].match.def&&f.pop(),(e=s(f))&&(e.match.def===i.radixPointDefinitionSymbol||!D(c,!0)||a.inArray(i.radixPoint,y())<c&&e.match.fn&&e.match.fn.test(I(c),o(),c,!1,i))&&!1!==(x=l(c,I(c,e.match,!0)||(null==e.match.fn?e.match.def:""!==I(c)?I(c):y()[c]),!0))&&(o().validPositions[x.pos||c].generatedInput=!0))}f=!0===f;var u=b;b.begin!==d&&(u=Y&&!k(b)?b.end:b.begin);var x=!0,F=a.extend(!0,{},o().validPositions);if(a.isFunction(i.preValidation)&&!f&&!0!==g&&!0!==j&&(x=i.preValidation(y(),u,c,k(b),i)),!0===x){if(n(u),k(b)&&(P(d,e.keyCode.DELETE,b,!0,!0),u=o().p),u<o().maskLength&&(U===d||u<U)&&(x=l(u,c,f),(!f||!0===g)&&!1===x&&!0!==j)){var H=o().validPositions[u];if(!H||null!==H.match.fn||H.match.def!==c&&c!==i.skipOptionalPartCharacter){if((i.insertMode||o().validPositions[E(u)]===d)&&!D(u,!0))for(var J=u+1,K=E(u);J<=K;J++)if(!1!==(x=l(J,c,f))){!function(b,c){var e=o().validPositions[c];if(e)for(var f=e.locator,g=f.length,h=b;h<c;h++)if(o().validPositions[h]===d&&!D(h,!0)){var i=w(h).slice(),j=s(i,!0),k=-1;""===i[i.length-1].match.def&&i.pop(),a.each(i,function(a,b){for(var c=0;c<g;c++){if(b.locator[c]===d||!B(b.locator[c].toString().split(","),f[c].toString().split(","),b.na)){var e=f[c],h=j.locator[c],i=b.locator[c];e-h>Math.abs(e-i)&&(j=b);break}k<c&&(k=c,j=b)}}),j=a.extend({},j,{input:I(h,j.match,!0)||j.match.def}),j.generatedInput=!0,m(h,j,!0),o().validPositions[c]=d,l(c,e.input,!0)}}(u,x.pos!==d?x.pos:J),u=J;break}}else x={caret:E(u)}}!1===x&&i.keepStatic&&!f&&!0!==h&&(x=function(b,c,e){var f,h,j,k,l,m,n,r,s=a.extend(!0,{},o().validPositions),t=!1,u=q();for(k=o().validPositions[u];u>=0;u--)if((j=o().validPositions[u])&&j.alternation!==d){if(f=u,h=o().validPositions[f].alternation,k.locator[j.alternation]!==j.locator[j.alternation])break;k=j}if(h!==d){r=parseInt(f);var v=k.locator[k.alternation||h]!==d?k.locator[k.alternation||h]:n[0];v.length>0&&(v=v.split(",")[0]);var x=o().validPositions[r],y=o().validPositions[r-1];a.each(w(r,y?y.locator:d,r-1),function(f,j){n=j.locator[h]?j.locator[h].toString().split(","):[];for(var k=0;k<n.length;k++){var u=[],w=0,y=0,z=!1;if(v<n[k]&&(j.na===d||-1===a.inArray(n[k],j.na.split(","))||-1===a.inArray(v.toString(),n))){o().validPositions[r]=a.extend(!0,{},j);var A=o().validPositions[r].locator;for(o().validPositions[r].locator[h]=parseInt(n[k]),null==j.match.fn?(x.input!==j.match.def&&(z=!0,!0!==x.generatedInput&&u.push(x.input)),y++,o().validPositions[r].generatedInput=!/[0-9a-bA-Z]/.test(j.match.def),o().validPositions[r].input=j.match.def):o().validPositions[r].input=x.input,l=r+1;l<q(d,!0)+1;l++)m=o().validPositions[l],m&&!0!==m.generatedInput&&/[0-9a-bA-Z]/.test(m.input)?u.push(m.input):l<b&&w++,delete o().validPositions[l];for(z&&u[0]===j.match.def&&u.shift(),p(!0),t=!0;u.length>0;){var B=u.shift();if(B!==i.skipOptionalPartCharacter&&!(t=C(q(d,!0)+1,B,!1,g,!0)))break}if(t){o().validPositions[r].locator=A;var D=q(b)+1;for(l=r+1;l<q()+1;l++)((m=o().validPositions[l])===d||null==m.match.fn)&&l<b+(y-w)&&y++;b+=y-w,t=C(b>D?D:b,c,e,g,!0)}if(t)return!1;p(),o().validPositions=a.extend(!0,{},s)}}})}return t}(u,c,f)),!0===x&&(x={pos:u})}if(a.isFunction(i.postValidation)&&!1!==x&&!f&&!0!==g&&!0!==j){var L=i.postValidation(y(!0),x,i);if(L.refreshFromBuffer&&L.buffer){var M=L.refreshFromBuffer;z(!0===M?M:M.start,M.end,L.buffer)}x=!0===L?x:L}return x&&x.pos===d&&(x.pos=u),!1!==x&&!0!==j||(p(!0),o().validPositions=a.extend(!0,{},F)),x}function D(a,b){var c=t(a).match;if(""===c.def&&(c=u(a).match),null!=c.fn)return c.fn;if(!0!==b&&a>-1){var d=w(a);return d.length>1+(""===d[d.length-1].match.def?1:0)}return!1}function E(a,b){var c=o().maskLength;if(a>=c)return c;var d=a;for(w(c+1).length>1&&(n(!0,c+1,!0),c=o().maskLength);++d<c&&(!0===b&&(!0!==u(d).match.newBlockMarker||!D(d))||!0!==b&&!D(d)););return d}function F(a,b){var c,d=a;if(d<=0)return 0;for(;--d>0&&(!0===b&&!0!==u(d).match.newBlockMarker||!0!==b&&!D(d)&&(c=w(d),c.length<2||2===c.length&&""===c[1].match.def)););return d}function G(a){return o().validPositions[a]===d?I(a):o().validPositions[a].input}function H(b,c,e,f,g){if(f&&a.isFunction(i.onBeforeWrite)){var h=i.onBeforeWrite.call(W,f,c,e,i);if(h){if(h.refreshFromBuffer){var j=h.refreshFromBuffer;z(!0===j?j:j.start,j.end,h.buffer||c),c=y(!0)}e!==d&&(e=h.caret!==d?h.caret:e)}}b!==d&&(b.inputmask._valueSet(c.join("")),e===d||f!==d&&"blur"===f.type?R(b,e,0===c.length):m&&f&&"input"===f.type?setTimeout(function(){L(b,e)},0):L(b,e),!0===g&&($=!0,a(b).trigger("input")))}function I(b,c,e){if(c=c||u(b).match,c.placeholder!==d||!0===e)return a.isFunction(c.placeholder)?c.placeholder(i):c.placeholder;if(null===c.fn){if(b>-1&&o().validPositions[b]===d){var f,g=w(b),h=[];if(g.length>1+(""===g[g.length-1].match.def?1:0))for(var j=0;j<g.length;j++)if(!0!==g[j].match.optionality&&!0!==g[j].match.optionalQuantifier&&(null===g[j].match.fn||f===d||!1!==g[j].match.fn.test(f.match.def,o(),b,!0,i))&&(h.push(g[j]),null===g[j].match.fn&&(f=g[j]),h.length>1&&/[0-9a-bA-Z]/.test(h[0].match.def)))return i.placeholder.charAt(b%i.placeholder.length)}return c.def}return i.placeholder.charAt(b%i.placeholder.length)}function J(b,f,g,h,j){function k(a,b){return-1!==x().slice(a,E(a)).join("").indexOf(b)&&!D(a)&&u(a).match.nativeDef===b.charAt(b.length-1)}var l=h.slice(),m="",n=-1,r=d;if(p(),g||!0===i.autoUnmask)n=E(n);else{var s=x().slice(0,E(-1)).join(""),v=l.join("").match(new RegExp("^"+e.escapeRegex(s),"g"));v&&v.length>0&&(l.splice(0,v.length*s.length),n=E(n))}if(-1===n?(o().p=E(n),n=0):o().p=n,a.each(l,function(c,e){if(e!==d)if(o().validPositions[c]===d&&l[c]===I(c)&&D(c,!0)&&!1===C(c,l[c],!0,d,d,!0))o().p++;else{var f=new a.Event("_checkval");f.which=e.charCodeAt(0),m+=e;var h=q(d,!0),j=o().validPositions[h],s=t(h+1,j?j.locator.slice():d,h);if(!k(n,m)||g||i.autoUnmask){var u=g?c:null==s.match.fn&&s.match.optionality&&h+1<o().p?h+1:o().p;r=ca.keypressEvent.call(b,f,!0,!1,g,u),n=u+1,m=""}else r=ca.keypressEvent.call(b,f,!0,!1,!0,h+1);if(!1!==r&&!g&&a.isFunction(i.onBeforeWrite)){var v=r;if(r=i.onBeforeWrite.call(W,f,y(),r.forwardPosition,i),(r=a.extend(v,r))&&r.refreshFromBuffer){var w=r.refreshFromBuffer;z(!0===w?w:w.start,w.end,r.buffer),p(!0),r.caret&&(o().p=r.caret,r.forwardPosition=r.caret)}}}}),f){var w=d;c.activeElement===b&&r&&(w=i.numericInput?F(r.forwardPosition):r.forwardPosition),H(b,y(),w,j||new a.Event("checkval"),j&&"input"===j.type)}}function K(b){if(b){if(b.inputmask===d)return b.value;b.inputmask&&b.inputmask.refreshValue&&ca.setValueEvent.call(b)}var c=[],e=o().validPositions;for(var f in e)e[f].match&&null!=e[f].match.fn&&c.push(e[f].input);var g=0===c.length?"":(Y?c.reverse():c).join("");if(a.isFunction(i.onUnMask)){var h=(Y?y().slice().reverse():y()).join("");g=i.onUnMask.call(W,h,g,i)}return g}function L(a,e,f,g){function h(a){if(!0!==g&&Y&&"number"==typeof a&&(!i.greedy||""!==i.placeholder)){a=y().join("").length-a}return a}var k;if(e===d)return a.setSelectionRange?(e=a.selectionStart,f=a.selectionEnd):b.getSelection?(k=b.getSelection().getRangeAt(0),k.commonAncestorContainer.parentNode!==a&&k.commonAncestorContainer!==a||(e=k.startOffset,f=k.endOffset)):c.selection&&c.selection.createRange&&(k=c.selection.createRange(),e=0-k.duplicate().moveStart("character",-a.inputmask._valueGet().length),f=e+k.text.length),{begin:h(e),end:h(f)};if(e.begin!==d&&(f=e.end,e=e.begin),"number"==typeof e){e=h(e),f=h(f),f="number"==typeof f?f:e;var l=parseInt(((a.ownerDocument.defaultView||b).getComputedStyle?(a.ownerDocument.defaultView||b).getComputedStyle(a,null):a.currentStyle).fontSize)*f;if(a.scrollLeft=l>a.scrollWidth?l:0,j||!1!==i.insertMode||e!==f||f++,a.setSelectionRange)a.selectionStart=e,a.selectionEnd=f;else if(b.getSelection){if(k=c.createRange(),a.firstChild===d||null===a.firstChild){var m=c.createTextNode("");a.appendChild(m)}k.setStart(a.firstChild,e<a.inputmask._valueGet().length?e:a.inputmask._valueGet().length),k.setEnd(a.firstChild,f<a.inputmask._valueGet().length?f:a.inputmask._valueGet().length),k.collapse(!0);var n=b.getSelection();n.removeAllRanges(),n.addRange(k)}else a.createTextRange&&(k=a.createTextRange(),k.collapse(!0),k.moveEnd("character",f),k.moveStart("character",e),k.select());R(a,{begin:e,end:f})}}function M(b){var c,e,f=y(),g=f.length,h=q(),i={},j=o().validPositions[h],k=j!==d?j.locator.slice():d;for(c=h+1;c<f.length;c++)e=t(c,k,c-1),k=e.locator.slice(),i[c]=a.extend(!0,{},e);var l=j&&j.alternation!==d?j.locator[j.alternation]:d;for(c=g-1;c>h&&(e=i[c],(e.match.optionality||e.match.optionalQuantifier&&e.match.newBlockMarker||l&&(l!==i[c].locator[j.alternation]&&null!=e.match.fn||null===e.match.fn&&e.locator[j.alternation]&&B(e.locator[j.alternation].toString().split(","),l.toString().split(","))&&""!==w(c)[0].def))&&f[c]===I(c,e.match));c--)g--;return b?{l:g,def:i[g]?i[g].match:d}:g}function N(a){for(var b,c=M(),e=a.length,f=o().validPositions[q()];c<e&&!D(c,!0)&&(b=f!==d?t(c,f.locator.slice(""),f):u(c))&&!0!==b.match.optionality&&(!0!==b.match.optionalQuantifier&&!0!==b.match.newBlockMarker||c+1===e&&""===(f!==d?t(c+1,f.locator.slice(""),f):u(c+1)).match.def);)c++;for(;(b=o().validPositions[c-1])&&b&&b.match.optionality&&b.input===i.skipOptionalPartCharacter;)c--;return a.splice(c),a}function O(b){if(a.isFunction(i.isComplete))return i.isComplete(b,i);if("*"===i.repeat)return d;var c=!1,e=M(!0),f=F(e.l);if(e.def===d||e.def.newBlockMarker||e.def.optionality||e.def.optionalQuantifier){c=!0;for(var g=0;g<=f;g++){var h=t(g).match;if(null!==h.fn&&o().validPositions[g]===d&&!0!==h.optionality&&!0!==h.optionalQuantifier||null===h.fn&&b[g]!==I(g,h)){c=!1;break}}}return c}function P(b,c,f,g,h){if((i.numericInput||Y)&&(c===e.keyCode.BACKSPACE?c=e.keyCode.DELETE:c===e.keyCode.DELETE&&(c=e.keyCode.BACKSPACE),Y)){var j=f.end;f.end=f.begin,f.begin=j}c===e.keyCode.BACKSPACE&&(f.end-f.begin<1||!1===i.insertMode)?(f.begin=F(f.begin),o().validPositions[f.begin]!==d&&o().validPositions[f.begin].input===i.groupSeparator&&f.begin--):c===e.keyCode.DELETE&&f.begin===f.end&&(f.end=D(f.end,!0)&&o().validPositions[f.end]&&o().validPositions[f.end].input!==i.radixPoint?f.end+1:E(f.end)+1,o().validPositions[f.begin]!==d&&o().validPositions[f.begin].input===i.groupSeparator&&f.end++),r(f.begin,f.end,!1,g),!0!==g&&function(){if(i.keepStatic){for(var c=[],e=q(-1,!0),f=a.extend(!0,{},o().validPositions),g=o().validPositions[e];e>=0;e--){var h=o().validPositions[e];if(h){if(!0!==h.generatedInput&&/[0-9a-bA-Z]/.test(h.input)&&c.push(h.input),delete o().validPositions[e],h.alternation!==d&&h.locator[h.alternation]!==g.locator[h.alternation])break;g=h}}if(e>-1)for(o().p=E(q(-1,!0));c.length>0;){var j=new a.Event("keypress");j.which=c.pop().charCodeAt(0),ca.keypressEvent.call(b,j,!0,!1,!1,o().p)}else o().validPositions=a.extend(!0,{},f)}}();var k=q(f.begin,!0);if(k<f.begin)o().p=E(k);else if(!0!==g&&(o().p=f.begin,!0!==h))for(;o().p<k&&o().validPositions[o().p]===d;)o().p++}function Q(d){function e(a){var b,e=c.createElement("span");for(var g in f)isNaN(g)&&-1!==g.indexOf("font")&&(e.style[g]=f[g]);e.style.textTransform=f.textTransform,e.style.letterSpacing=f.letterSpacing,e.style.position="absolute",e.style.height="auto",e.style.width="auto",e.style.visibility="hidden",e.style.whiteSpace="nowrap",c.body.appendChild(e);var h,i=d.inputmask._valueGet(),j=0;for(b=0,h=i.length;b<=h;b++){if(e.innerHTML+=i.charAt(b)||"_",e.offsetWidth>=a){var k=a-j,l=e.offsetWidth-a;e.innerHTML=i.charAt(b),k-=e.offsetWidth/3,b=k<l?b-1:b;break}j=e.offsetWidth}return c.body.removeChild(e),b}var f=(d.ownerDocument.defaultView||b).getComputedStyle(d,null),g=c.createElement("div");g.style.width=f.width,g.style.textAlign=f.textAlign,V=c.createElement("div"),V.className="im-colormask",d.parentNode.insertBefore(V,d),d.parentNode.removeChild(d),V.appendChild(g),V.appendChild(d),d.style.left=g.offsetLeft+"px",a(d).on("click",function(a){return L(d,e(a.clientX)),ca.clickEvent.call(d,[a])}),a(d).on("keydown",function(a){a.shiftKey||!1===i.insertMode||setTimeout(function(){R(d)},0)})}function R(a,b,e){function f(){m||null!==h.fn&&j.input!==d?m&&(null!==h.fn&&j.input!==d||""===h.def)&&(m=!1,l+="</span>"):(m=!0,l+="<span class='im-static'>")}function g(d){!0!==d&&n!==b.begin||c.activeElement!==a||(l+="<span class='im-caret' style='border-right-width: 1px;border-right-style: solid;'></span>")}var h,j,k,l="",m=!1,n=0;if(V!==d){var p=y();if(b===d?b=L(a):b.begin===d&&(b={begin:b,end:b}),!0!==e){var r=q();do{g(),o().validPositions[n]?(j=o().validPositions[n],h=j.match,k=j.locator.slice(),f(),l+=p[n]):(j=t(n,k,n-1),h=j.match,k=j.locator.slice(),(!1===i.jitMasking||n<r||"number"==typeof i.jitMasking&&isFinite(i.jitMasking)&&i.jitMasking>n)&&(f(),l+=I(n,h))),n++}while((U===d||n<U)&&(null!==h.fn||""!==h.def)||r>n||m);-1===l.indexOf("im-caret")&&g(!0),m&&f()}var s=V.getElementsByTagName("div")[0];s.innerHTML=l,a.inputmask.positionColorMask(a,s)}}g=g||this.maskset,i=i||this.opts;var S,T,U,V,W=this,X=this.el,Y=this.isRTL,Z=!1,$=!1,_=!1,aa=!1,ba={on:function(b,c,f){var g=function(b){if(this.inputmask===d&&"FORM"!==this.nodeName){var c=a.data(this,"_inputmask_opts");c?new e(c).mask(this):ba.off(this)}else{if("setvalue"===b.type||"FORM"===this.nodeName||!(this.disabled||this.readOnly&&!("keydown"===b.type&&b.ctrlKey&&67===b.keyCode||!1===i.tabThrough&&b.keyCode===e.keyCode.TAB))){switch(b.type){case"input":if(!0===$)return $=!1,b.preventDefault();break;case"keydown":Z=!1,$=!1;break;case"keypress":if(!0===Z)return b.preventDefault();Z=!0;break;case"click":if(k||l){var g=this,h=arguments;return setTimeout(function(){f.apply(g,h)},0),!1}}var j=f.apply(this,arguments);return!1===j&&(b.preventDefault(),b.stopPropagation()),j}b.preventDefault()}};b.inputmask.events[c]=b.inputmask.events[c]||[],b.inputmask.events[c].push(g),-1!==a.inArray(c,["submit","reset"])?null!==b.form&&a(b.form).on(c,g):a(b).on(c,g)},off:function(b,c){if(b.inputmask&&b.inputmask.events){var d;c?(d=[],d[c]=b.inputmask.events[c]):d=b.inputmask.events,a.each(d,function(c,d){for(;d.length>0;){var e=d.pop();-1!==a.inArray(c,["submit","reset"])?null!==b.form&&a(b.form).off(c,e):a(b).off(c,e)}delete b.inputmask.events[c]})}}},ca={keydownEvent:function(b){var d=this,f=a(d),g=b.keyCode,h=L(d);if(g===e.keyCode.BACKSPACE||g===e.keyCode.DELETE||l&&g===e.keyCode.BACKSPACE_SAFARI||b.ctrlKey&&g===e.keyCode.X&&!function(a){var b=c.createElement("input"),d="on"+a,e=d in b;return e||(b.setAttribute(d,"return;"),e="function"==typeof b[d]),b=null,e}("cut"))b.preventDefault(),P(d,g,h),H(d,y(!0),o().p,b,d.inputmask._valueGet()!==y().join("")),d.inputmask._valueGet()===x().join("")?f.trigger("cleared"):!0===O(y())&&f.trigger("complete");else if(g===e.keyCode.END||g===e.keyCode.PAGE_DOWN){b.preventDefault();var j=E(q());i.insertMode||j!==o().maskLength||b.shiftKey||j--,L(d,b.shiftKey?h.begin:j,j,!0)}else g===e.keyCode.HOME&&!b.shiftKey||g===e.keyCode.PAGE_UP?(b.preventDefault(),L(d,0,b.shiftKey?h.begin:0,!0)):(i.undoOnEscape&&g===e.keyCode.ESCAPE||90===g&&b.ctrlKey)&&!0!==b.altKey?(J(d,!0,!1,S.split("")),f.trigger("click")):g!==e.keyCode.INSERT||b.shiftKey||b.ctrlKey?!0===i.tabThrough&&g===e.keyCode.TAB?(!0===b.shiftKey?(null===u(h.begin).match.fn&&(h.begin=E(h.begin)),h.end=F(h.begin,!0),h.begin=F(h.end,!0)):(h.begin=E(h.begin,!0),h.end=E(h.begin,!0),h.end<o().maskLength&&h.end--),h.begin<o().maskLength&&(b.preventDefault(),L(d,h.begin,h.end))):b.shiftKey||!1===i.insertMode&&(g===e.keyCode.RIGHT?setTimeout(function(){var a=L(d);L(d,a.begin)},0):g===e.keyCode.LEFT&&setTimeout(function(){var a=L(d);L(d,Y?a.begin+1:a.begin-1)},0)):(i.insertMode=!i.insertMode,L(d,i.insertMode||h.begin!==o().maskLength?h.begin:h.begin-1));i.onKeyDown.call(this,b,y(),L(d).begin,i),_=-1!==a.inArray(g,i.ignorables)},keypressEvent:function(b,c,f,g,h){var j=this,k=a(j),l=b.which||b.charCode||b.keyCode;if(!(!0===c||b.ctrlKey&&b.altKey)&&(b.ctrlKey||b.metaKey||_))return l===e.keyCode.ENTER&&S!==y().join("")&&(S=y().join(""),setTimeout(function(){k.trigger("change")},0)),!0;if(l){46===l&&!1===b.shiftKey&&""!==i.radixPoint&&(l=i.radixPoint.charCodeAt(0));var m,n=c?{begin:h,end:h}:L(j),q=String.fromCharCode(l);o().writeOutBuffer=!0;var r=C(n,q,g);if(!1!==r&&(p(!0),m=r.caret!==d?r.caret:c?r.pos+1:E(r.pos),o().p=m),!1!==f&&(setTimeout(function(){i.onKeyValidation.call(j,l,r,i)},0),o().writeOutBuffer&&!1!==r)){var s=y();H(j,s,i.numericInput&&r.caret===d?F(m):m,b,!0!==c),!0!==c&&setTimeout(function(){!0===O(s)&&k.trigger("complete")},0)}if(b.preventDefault(),c)return!1!==r&&(r.forwardPosition=m),r}},pasteEvent:function(c){var d,e=this,f=c.originalEvent||c,g=a(e),h=e.inputmask._valueGet(!0),j=L(e);Y&&(d=j.end,j.end=j.begin,j.begin=d);var k=h.substr(0,j.begin),l=h.substr(j.end,h.length);if(k===(Y?x().reverse():x()).slice(0,j.begin).join("")&&(k=""),l===(Y?x().reverse():x()).slice(j.end).join("")&&(l=""),Y&&(d=k,k=l,l=d),b.clipboardData&&b.clipboardData.getData)h=k+b.clipboardData.getData("Text")+l;else{if(!f.clipboardData||!f.clipboardData.getData)return!0;h=k+f.clipboardData.getData("text/plain")+l}var m=h;if(a.isFunction(i.onBeforePaste)){if(!1===(m=i.onBeforePaste.call(W,h,i)))return c.preventDefault();m||(m=h)}return J(e,!1,!1,Y?m.split("").reverse():m.toString().split("")),H(e,y(),E(q()),c,S!==y().join("")),!0===O(y())&&g.trigger("complete"),c.preventDefault()},inputFallBackEvent:function(b){var c=this,d=c.inputmask._valueGet();if(y().join("")!==d){var f=L(c);if(!1===function(b,c,d){if("."===c.charAt(d.begin-1)&&""!==i.radixPoint&&(c=c.split(""),c[d.begin-1]=i.radixPoint.charAt(0),c=c.join("")),c.charAt(d.begin-1)===i.radixPoint&&c.length>y().length){var e=new a.Event("keypress");return e.which=i.radixPoint.charCodeAt(0),ca.keypressEvent.call(b,e,!0,!0,!1,d.begin-1),!1}}(c,d,f))return!1;if(d=d.replace(new RegExp("("+e.escapeRegex(x().join(""))+")*"),""),!1===function(b,c,d){if(k){var e=c.replace(y().join(""),"");if(1===e.length){var f=new a.Event("keypress");return f.which=e.charCodeAt(0),ca.keypressEvent.call(b,f,!0,!0,!1,o().validPositions[d.begin-1]?d.begin:d.begin-1),!1}}}(c,d,f))return!1;f.begin>d.length&&(L(c,d.length),f=L(c));var g=y().join(""),h=d.substr(0,f.begin),j=d.substr(f.begin),l=g.substr(0,f.begin),m=g.substr(f.begin),n=f,p="",q=!1;if(h!==l){n.begin=0;for(var r=(q=h.length>=l.length)?h.length:l.length,s=0;h.charAt(s)===l.charAt(s)&&s<r;s++)n.begin++;q&&(p+=h.slice(n.begin,n.end))}j!==m&&(j.length>m.length?q&&(n.end=n.begin):j.length<m.length?n.end+=m.length-j.length:j.charAt(0)!==m.charAt(0)&&n.end++),H(c,y(),n),p.length>0?a.each(p.split(""),function(b,d){var e=new a.Event("keypress");e.which=d.charCodeAt(0),_=!1,ca.keypressEvent.call(c,e)}):(n.begin===n.end-1&&L(c,F(n.begin+1),n.end),
b.keyCode=e.keyCode.DELETE,ca.keydownEvent.call(c,b)),b.preventDefault()}},setValueEvent:function(b){this.inputmask.refreshValue=!1;var c=this,d=c.inputmask._valueGet(!0);a.isFunction(i.onBeforeMask)&&(d=i.onBeforeMask.call(W,d,i)||d),d=d.split(""),J(c,!0,!1,Y?d.reverse():d),S=y().join(""),(i.clearMaskOnLostFocus||i.clearIncomplete)&&c.inputmask._valueGet()===x().join("")&&c.inputmask._valueSet("")},focusEvent:function(a){var b=this,c=b.inputmask._valueGet();i.showMaskOnFocus&&(!i.showMaskOnHover||i.showMaskOnHover&&""===c)&&(b.inputmask._valueGet()!==y().join("")?H(b,y(),E(q())):!1===aa&&L(b,E(q()))),!0===i.positionCaretOnTab&&!1===aa&&""!==c&&(H(b,y(),L(b)),ca.clickEvent.apply(b,[a,!0])),S=y().join("")},mouseleaveEvent:function(a){var b=this;if(aa=!1,i.clearMaskOnLostFocus&&c.activeElement!==b){var d=y().slice(),e=b.inputmask._valueGet();e!==b.getAttribute("placeholder")&&""!==e&&(-1===q()&&e===x().join("")?d=[]:N(d),H(b,d))}},clickEvent:function(b,e){function f(b){if(""!==i.radixPoint){var c=o().validPositions;if(c[b]===d||c[b].input===I(b)){if(b<E(-1))return!0;var e=a.inArray(i.radixPoint,y());if(-1!==e){for(var f in c)if(e<f&&c[f].input!==I(f))return!1;return!0}}}return!1}var g=this;setTimeout(function(){if(c.activeElement===g){var a=L(g);if(e&&(Y?a.end=a.begin:a.begin=a.end),a.begin===a.end)switch(i.positionCaretOnClick){case"none":break;case"radixFocus":if(f(a.begin)){var b=y().join("").indexOf(i.radixPoint);L(g,i.numericInput?E(b):b);break}default:var h=a.begin,j=q(h,!0),k=E(j);if(h<k)L(g,D(h,!0)||D(h-1,!0)?h:E(h));else{var l=o().validPositions[j],m=t(k,l?l.match.locator:d,l),n=I(k,m.match);if(""!==n&&y()[k]!==n&&!0!==m.match.optionalQuantifier&&!0!==m.match.newBlockMarker||!D(k,!0)&&m.match.def===n){var p=E(k);(h>=p||h===k)&&(k=p)}L(g,k)}}}},0)},dblclickEvent:function(a){var b=this;setTimeout(function(){L(b,0,E(q()))},0)},cutEvent:function(d){var f=this,g=a(f),h=L(f),i=d.originalEvent||d,j=b.clipboardData||i.clipboardData,k=Y?y().slice(h.end,h.begin):y().slice(h.begin,h.end);j.setData("text",Y?k.reverse().join(""):k.join("")),c.execCommand&&c.execCommand("copy"),P(f,e.keyCode.DELETE,h),H(f,y(),o().p,d,S!==y().join("")),f.inputmask._valueGet()===x().join("")&&g.trigger("cleared")},blurEvent:function(b){var c=a(this),e=this;if(e.inputmask){var f=e.inputmask._valueGet(),g=y().slice();""!==f&&(i.clearMaskOnLostFocus&&(-1===q()&&f===x().join("")?g=[]:N(g)),!1===O(g)&&(setTimeout(function(){c.trigger("incomplete")},0),i.clearIncomplete&&(p(),g=i.clearMaskOnLostFocus?[]:x().slice())),H(e,g,d,b)),S!==y().join("")&&(S=g.join(""),c.trigger("change"))}},mouseenterEvent:function(a){var b=this;aa=!0,c.activeElement!==b&&i.showMaskOnHover&&b.inputmask._valueGet()!==y().join("")&&H(b,y())},submitEvent:function(a){S!==y().join("")&&T.trigger("change"),i.clearMaskOnLostFocus&&-1===q()&&X.inputmask._valueGet&&X.inputmask._valueGet()===x().join("")&&X.inputmask._valueSet(""),i.removeMaskOnSubmit&&(X.inputmask._valueSet(X.inputmask.unmaskedvalue(),!0),setTimeout(function(){H(X,y())},0))},resetEvent:function(a){X.inputmask.refreshValue=!0,setTimeout(function(){T.trigger("setvalue")},0)}};e.prototype.positionColorMask=function(a,b){a.style.left=b.offsetLeft+"px"};var da;if(f!==d)switch(f.action){case"isComplete":return X=f.el,O(y());case"unmaskedvalue":return X!==d&&f.value===d||(da=f.value,da=(a.isFunction(i.onBeforeMask)?i.onBeforeMask.call(W,da,i)||da:da).split(""),J(d,!1,!1,Y?da.reverse():da),a.isFunction(i.onBeforeWrite)&&i.onBeforeWrite.call(W,d,y(),0,i)),K(X);case"mask":!function(b){ba.off(b);var e=function(b,e){var f=b.getAttribute("type"),g="INPUT"===b.tagName&&-1!==a.inArray(f,e.supportsInputType)||b.isContentEditable||"TEXTAREA"===b.tagName;if(!g)if("INPUT"===b.tagName){var h=c.createElement("input");h.setAttribute("type",f),g="text"===h.type,h=null}else g="partial";return!1!==g?function(b){function f(){return this.inputmask?this.inputmask.opts.autoUnmask?this.inputmask.unmaskedvalue():-1!==q()||!0!==e.nullable?c.activeElement===this&&e.clearMaskOnLostFocus?(Y?N(y().slice()).reverse():N(y().slice())).join(""):h.call(this):"":h.call(this)}function g(b){i.call(this,b),this.inputmask&&a(this).trigger("setvalue")}var h,i;if(!b.inputmask.__valueGet){if(!0!==e.noValuePatching){if(Object.getOwnPropertyDescriptor){"function"!=typeof Object.getPrototypeOf&&(Object.getPrototypeOf="object"==typeof"test".__proto__?function(a){return a.__proto__}:function(a){return a.constructor.prototype});var j=Object.getPrototypeOf?Object.getOwnPropertyDescriptor(Object.getPrototypeOf(b),"value"):d;j&&j.get&&j.set?(h=j.get,i=j.set,Object.defineProperty(b,"value",{get:f,set:g,configurable:!0})):"INPUT"!==b.tagName&&(h=function(){return this.textContent},i=function(a){this.textContent=a},Object.defineProperty(b,"value",{get:f,set:g,configurable:!0}))}else c.__lookupGetter__&&b.__lookupGetter__("value")&&(h=b.__lookupGetter__("value"),i=b.__lookupSetter__("value"),b.__defineGetter__("value",f),b.__defineSetter__("value",g));b.inputmask.__valueGet=h,b.inputmask.__valueSet=i}b.inputmask._valueGet=function(a){return Y&&!0!==a?h.call(this.el).split("").reverse().join(""):h.call(this.el)},b.inputmask._valueSet=function(a,b){i.call(this.el,null===a||a===d?"":!0!==b&&Y?a.split("").reverse().join(""):a)},h===d&&(h=function(){return this.value},i=function(a){this.value=a},function(b){if(a.valHooks&&(a.valHooks[b]===d||!0!==a.valHooks[b].inputmaskpatch)){var c=a.valHooks[b]&&a.valHooks[b].get?a.valHooks[b].get:function(a){return a.value},f=a.valHooks[b]&&a.valHooks[b].set?a.valHooks[b].set:function(a,b){return a.value=b,a};a.valHooks[b]={get:function(a){if(a.inputmask){if(a.inputmask.opts.autoUnmask)return a.inputmask.unmaskedvalue();var b=c(a);return-1!==q(d,d,a.inputmask.maskset.validPositions)||!0!==e.nullable?b:""}return c(a)},set:function(b,c){var d,e=a(b);return d=f(b,c),b.inputmask&&e.trigger("setvalue"),d},inputmaskpatch:!0}}}(b.type),function(b){ba.on(b,"mouseenter",function(b){var c=a(this);this.inputmask._valueGet()!==y().join("")&&c.trigger("setvalue")})}(b))}}(b):b.inputmask=d,g}(b,i);if(!1!==e&&(X=b,T=a(X),U=X!==d?X.maxLength:d,-1===U&&(U=d),!0===i.colorMask&&Q(X),m&&(X.hasOwnProperty("inputmode")&&(X.inputmode=i.inputmode,X.setAttribute("inputmode",i.inputmode)),"rtfm"===i.androidHack&&(!0!==i.colorMask&&Q(X),X.type="password")),!0===e&&(ba.on(X,"submit",ca.submitEvent),ba.on(X,"reset",ca.resetEvent),ba.on(X,"mouseenter",ca.mouseenterEvent),ba.on(X,"blur",ca.blurEvent),ba.on(X,"focus",ca.focusEvent),ba.on(X,"mouseleave",ca.mouseleaveEvent),!0!==i.colorMask&&ba.on(X,"click",ca.clickEvent),ba.on(X,"dblclick",ca.dblclickEvent),ba.on(X,"paste",ca.pasteEvent),ba.on(X,"dragdrop",ca.pasteEvent),ba.on(X,"drop",ca.pasteEvent),ba.on(X,"cut",ca.cutEvent),ba.on(X,"complete",i.oncomplete),ba.on(X,"incomplete",i.onincomplete),ba.on(X,"cleared",i.oncleared),m||!0===i.inputEventOnly?X.removeAttribute("maxLength"):(ba.on(X,"keydown",ca.keydownEvent),ba.on(X,"keypress",ca.keypressEvent)),ba.on(X,"compositionstart",a.noop),ba.on(X,"compositionupdate",a.noop),ba.on(X,"compositionend",a.noop),ba.on(X,"keyup",a.noop),ba.on(X,"input",ca.inputFallBackEvent),ba.on(X,"beforeinput",a.noop)),ba.on(X,"setvalue",ca.setValueEvent),S=x().join(""),""!==X.inputmask._valueGet(!0)||!1===i.clearMaskOnLostFocus||c.activeElement===X)){var f=a.isFunction(i.onBeforeMask)?i.onBeforeMask.call(W,X.inputmask._valueGet(!0),i)||X.inputmask._valueGet(!0):X.inputmask._valueGet(!0);""!==f&&J(X,!0,!1,Y?f.split("").reverse():f.split(""));var g=y().slice();S=g.join(""),!1===O(g)&&i.clearIncomplete&&p(),i.clearMaskOnLostFocus&&c.activeElement!==X&&(-1===q()?g=[]:N(g)),H(X,g),c.activeElement===X&&L(X,E(q()))}}(X);break;case"format":return da=(a.isFunction(i.onBeforeMask)?i.onBeforeMask.call(W,f.value,i)||f.value:f.value).split(""),J(d,!0,!1,Y?da.reverse():da),f.metadata?{value:Y?y().slice().reverse().join(""):y().join(""),metadata:h.call(this,{action:"getmetadata"},g,i)}:Y?y().slice().reverse().join(""):y().join("");case"isValid":f.value?(da=f.value.split(""),J(d,!0,!0,Y?da.reverse():da)):f.value=y().join("");for(var ea=y(),fa=M(),ga=ea.length-1;ga>fa&&!D(ga);ga--);return ea.splice(fa,ga+1-fa),O(ea)&&f.value===y().join("");case"getemptymask":return x().join("");case"remove":if(X&&X.inputmask){T=a(X),X.inputmask._valueSet(i.autoUnmask?K(X):X.inputmask._valueGet(!0)),ba.off(X);Object.getOwnPropertyDescriptor&&Object.getPrototypeOf?Object.getOwnPropertyDescriptor(Object.getPrototypeOf(X),"value")&&X.inputmask.__valueGet&&Object.defineProperty(X,"value",{get:X.inputmask.__valueGet,set:X.inputmask.__valueSet,configurable:!0}):c.__lookupGetter__&&X.__lookupGetter__("value")&&X.inputmask.__valueGet&&(X.__defineGetter__("value",X.inputmask.__valueGet),X.__defineSetter__("value",X.inputmask.__valueSet)),X.inputmask=d}return X;case"getmetadata":if(a.isArray(g.metadata)){var ha=n(!0,0,!1).join("");return a.each(g.metadata,function(a,b){if(b.mask===ha)return ha=b,!1}),ha}return g.metadata}}var i=navigator.userAgent,j=/mobile/i.test(i),k=/iemobile/i.test(i),l=/iphone/i.test(i)&&!k,m=/android/i.test(i)&&!k;return e.prototype={dataAttribute:"data-inputmask",defaults:{placeholder:"_",optionalmarker:{start:"[",end:"]"},quantifiermarker:{start:"{",end:"}"},groupmarker:{start:"(",end:")"},alternatormarker:"|",escapeChar:"\\",mask:null,regex:null,oncomplete:a.noop,onincomplete:a.noop,oncleared:a.noop,repeat:0,greedy:!0,autoUnmask:!1,removeMaskOnSubmit:!1,clearMaskOnLostFocus:!0,insertMode:!0,clearIncomplete:!1,alias:null,onKeyDown:a.noop,onBeforeMask:null,onBeforePaste:function(b,c){return a.isFunction(c.onBeforeMask)?c.onBeforeMask.call(this,b,c):b},onBeforeWrite:null,onUnMask:null,showMaskOnFocus:!0,showMaskOnHover:!0,onKeyValidation:a.noop,skipOptionalPartCharacter:" ",numericInput:!1,rightAlign:!1,undoOnEscape:!0,radixPoint:"",radixPointDefinitionSymbol:d,groupSeparator:"",keepStatic:null,positionCaretOnTab:!0,tabThrough:!1,supportsInputType:["text","tel","password"],ignorables:[8,9,13,19,27,33,34,35,36,37,38,39,40,45,46,93,112,113,114,115,116,117,118,119,120,121,122,123,0,229],isComplete:null,canClearPosition:a.noop,preValidation:null,postValidation:null,staticDefinitionSymbol:d,jitMasking:!1,nullable:!0,inputEventOnly:!1,noValuePatching:!1,positionCaretOnClick:"lvp",casing:null,inputmode:"verbatim",colorMask:!1,androidHack:!1,importDataAttributes:!0},definitions:{9:{validator:"[0-9\uff11-\uff19]",cardinality:1,definitionSymbol:"*"},a:{validator:"[A-Za-z\u0410-\u044f\u0401\u0451\xc0-\xff\xb5]",cardinality:1,definitionSymbol:"*"},"*":{validator:"[0-9\uff11-\uff19A-Za-z\u0410-\u044f\u0401\u0451\xc0-\xff\xb5]",cardinality:1}},aliases:{},masksCache:{},mask:function(i){function j(c,e,g,h){function i(a,e){null!==(e=e!==d?e:c.getAttribute(h+"-"+a))&&("string"==typeof e&&(0===a.indexOf("on")?e=b[e]:"false"===e?e=!1:"true"===e&&(e=!0)),g[a]=e)}if(!0===e.importDataAttributes){var j,k,l,m,n=c.getAttribute(h);if(n&&""!==n&&(n=n.replace(new RegExp("'","g"),'"'),k=JSON.parse("{"+n+"}")),k){l=d;for(m in k)if("alias"===m.toLowerCase()){l=k[m];break}}i("alias",l),g.alias&&f(g.alias,g,e);for(j in e){if(k){l=d;for(m in k)if(m.toLowerCase()===j.toLowerCase()){l=k[m];break}}i(j,l)}}return a.extend(!0,e,g),("rtl"===c.dir||e.rightAlign)&&(c.style.textAlign="right"),("rtl"===c.dir||e.numericInput)&&(c.dir="ltr",c.removeAttribute("dir"),e.isRTL=!0),e}var k=this;return"string"==typeof i&&(i=c.getElementById(i)||c.querySelectorAll(i)),i=i.nodeName?[i]:i,a.each(i,function(b,c){var f=a.extend(!0,{},k.opts);j(c,f,a.extend(!0,{},k.userOptions),k.dataAttribute);var i=g(f,k.noMasksCache);i!==d&&(c.inputmask!==d&&(c.inputmask.opts.autoUnmask=!0,c.inputmask.remove()),c.inputmask=new e(d,d,!0),c.inputmask.opts=f,c.inputmask.noMasksCache=k.noMasksCache,c.inputmask.userOptions=a.extend(!0,{},k.userOptions),c.inputmask.isRTL=f.isRTL||f.numericInput,c.inputmask.el=c,c.inputmask.maskset=i,a.data(c,"_inputmask_opts",f),h.call(c.inputmask,{action:"mask"}))}),i&&i[0]?i[0].inputmask||this:this},option:function(b,c){return"string"==typeof b?this.opts[b]:"object"==typeof b?(a.extend(this.userOptions,b),this.el&&!0!==c&&this.mask(this.el),this):void 0},unmaskedvalue:function(a){return this.maskset=this.maskset||g(this.opts,this.noMasksCache),h.call(this,{action:"unmaskedvalue",value:a})},remove:function(){return h.call(this,{action:"remove"})},getemptymask:function(){return this.maskset=this.maskset||g(this.opts,this.noMasksCache),h.call(this,{action:"getemptymask"})},hasMaskedValue:function(){return!this.opts.autoUnmask},isComplete:function(){return this.maskset=this.maskset||g(this.opts,this.noMasksCache),h.call(this,{action:"isComplete"})},getmetadata:function(){return this.maskset=this.maskset||g(this.opts,this.noMasksCache),h.call(this,{action:"getmetadata"})},isValid:function(a){return this.maskset=this.maskset||g(this.opts,this.noMasksCache),h.call(this,{action:"isValid",value:a})},format:function(a,b){return this.maskset=this.maskset||g(this.opts,this.noMasksCache),h.call(this,{action:"format",value:a,metadata:b})},analyseMask:function(b,c,f){function g(a,b,c,d){this.matches=[],this.openGroup=a||!1,this.alternatorGroup=!1,this.isGroup=a||!1,this.isOptional=b||!1,this.isQuantifier=c||!1,this.isAlternator=d||!1,this.quantifier={min:1,max:1}}function h(b,g,h){h=h!==d?h:b.matches.length;var i=b.matches[h-1];if(c)0===g.indexOf("[")||u&&/\\d|\\s|\\w]/i.test(g)||"."===g?b.matches.splice(h++,0,{fn:new RegExp(g,f.casing?"i":""),cardinality:1,optionality:b.isOptional,newBlockMarker:i===d||i.def!==g,casing:null,def:g,placeholder:d,nativeDef:g}):(u&&(g=g[g.length-1]),a.each(g.split(""),function(a,c){i=b.matches[h-1],b.matches.splice(h++,0,{fn:null,cardinality:0,optionality:b.isOptional,newBlockMarker:i===d||i.def!==c&&null!==i.fn,casing:null,def:f.staticDefinitionSymbol||c,placeholder:f.staticDefinitionSymbol!==d?c:d,nativeDef:c})})),u=!1;else{var j=(f.definitions?f.definitions[g]:d)||e.prototype.definitions[g];if(j&&!u){for(var k=j.prevalidator,l=k?k.length:0,m=1;m<j.cardinality;m++){var n=l>=m?k[m-1]:[],o=n.validator,p=n.cardinality;b.matches.splice(h++,0,{fn:o?"string"==typeof o?new RegExp(o,f.casing?"i":""):new function(){this.test=o}:new RegExp("."),cardinality:p||1,optionality:b.isOptional,newBlockMarker:i===d||i.def!==(j.definitionSymbol||g),casing:j.casing,def:j.definitionSymbol||g,placeholder:j.placeholder,nativeDef:g}),i=b.matches[h-1]}b.matches.splice(h++,0,{fn:j.validator?"string"==typeof j.validator?new RegExp(j.validator,f.casing?"i":""):new function(){this.test=j.validator}:new RegExp("."),cardinality:j.cardinality,optionality:b.isOptional,newBlockMarker:i===d||i.def!==(j.definitionSymbol||g),casing:j.casing,def:j.definitionSymbol||g,placeholder:j.placeholder,nativeDef:g})}else b.matches.splice(h++,0,{fn:null,cardinality:0,optionality:b.isOptional,newBlockMarker:i===d||i.def!==g&&null!==i.fn,casing:null,def:f.staticDefinitionSymbol||g,placeholder:f.staticDefinitionSymbol!==d?g:d,nativeDef:g}),u=!1}}function i(b){b&&b.matches&&a.each(b.matches,function(a,e){var g=b.matches[a+1];(g===d||g.matches===d||!1===g.isQuantifier)&&e&&e.isGroup&&(e.isGroup=!1,c||(h(e,f.groupmarker.start,0),!0!==e.openGroup&&h(e,f.groupmarker.end))),i(e)})}function j(){if(w.length>0){if(o=w[w.length-1],h(o,m),o.isAlternator){p=w.pop();for(var a=0;a<p.matches.length;a++)p.matches[a].isGroup=!1;w.length>0?(o=w[w.length-1],o.matches.push(p)):v.matches.push(p)}}else h(v,m)}function k(a){a.matches=a.matches.reverse();for(var b in a.matches)if(a.matches.hasOwnProperty(b)){var c=parseInt(b);if(a.matches[b].isQuantifier&&a.matches[c+1]&&a.matches[c+1].isGroup){var e=a.matches[b];a.matches.splice(b,1),a.matches.splice(c+1,0,e)}a.matches[b].matches!==d?a.matches[b]=k(a.matches[b]):a.matches[b]=function(a){return a===f.optionalmarker.start?a=f.optionalmarker.end:a===f.optionalmarker.end?a=f.optionalmarker.start:a===f.groupmarker.start?a=f.groupmarker.end:a===f.groupmarker.end&&(a=f.groupmarker.start),a}(a.matches[b])}return a}var l,m,n,o,p,q,r,s=/(?:[?*+]|\{[0-9\+\*]+(?:,[0-9\+\*]*)?\})|[^.?*+^${[]()|\\]+|./g,t=/\[\^?]?(?:[^\\\]]+|\\[\S\s]?)*]?|\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9][0-9]*|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|c[A-Za-z]|[\S\s]?)|\((?:\?[:=!]?)?|(?:[?*+]|\{[0-9]+(?:,[0-9]*)?\})\??|[^.?*+^${[()|\\]+|./g,u=!1,v=new g,w=[],x=[];for(c&&(f.optionalmarker.start=d,f.optionalmarker.end=d);l=c?t.exec(b):s.exec(b);){if(m=l[0],c)switch(m.charAt(0)){case"?":m="{0,1}";break;case"+":case"*":m="{"+m+"}"}if(u)j();else switch(m.charAt(0)){case f.escapeChar:u=!0,c&&j();break;case f.optionalmarker.end:case f.groupmarker.end:if(n=w.pop(),n.openGroup=!1,n!==d)if(w.length>0){if(o=w[w.length-1],o.matches.push(n),o.isAlternator){p=w.pop();for(var y=0;y<p.matches.length;y++)p.matches[y].isGroup=!1,p.matches[y].alternatorGroup=!1;w.length>0?(o=w[w.length-1],o.matches.push(p)):v.matches.push(p)}}else v.matches.push(n);else j();break;case f.optionalmarker.start:w.push(new g(!1,!0));break;case f.groupmarker.start:w.push(new g(!0));break;case f.quantifiermarker.start:var z=new g(!1,!1,!0);m=m.replace(/[{}]/g,"");var A=m.split(","),B=isNaN(A[0])?A[0]:parseInt(A[0]),C=1===A.length?B:isNaN(A[1])?A[1]:parseInt(A[1]);if("*"!==C&&"+"!==C||(B="*"===C?0:1),z.quantifier={min:B,max:C},w.length>0){var D=w[w.length-1].matches;l=D.pop(),l.isGroup||(r=new g(!0),r.matches.push(l),l=r),D.push(l),D.push(z)}else l=v.matches.pop(),l.isGroup||(c&&null===l.fn&&"."===l.def&&(l.fn=new RegExp(l.def,f.casing?"i":"")),r=new g(!0),r.matches.push(l),l=r),v.matches.push(l),v.matches.push(z);break;case f.alternatormarker:if(w.length>0){o=w[w.length-1];var E=o.matches[o.matches.length-1];q=o.openGroup&&(E.matches===d||!1===E.isGroup&&!1===E.isAlternator)?w.pop():o.matches.pop()}else q=v.matches.pop();if(q.isAlternator)w.push(q);else if(q.alternatorGroup?(p=w.pop(),q.alternatorGroup=!1):p=new g(!1,!1,!1,!0),p.matches.push(q),w.push(p),q.openGroup){q.openGroup=!1;var F=new g(!0);F.alternatorGroup=!0,w.push(F)}break;default:j()}}for(;w.length>0;)n=w.pop(),v.matches.push(n);return v.matches.length>0&&(i(v),x.push(v)),(f.numericInput||f.isRTL)&&k(x[0]),x}},e.extendDefaults=function(b){a.extend(!0,e.prototype.defaults,b)},e.extendDefinitions=function(b){a.extend(!0,e.prototype.definitions,b)},e.extendAliases=function(b){a.extend(!0,e.prototype.aliases,b)},e.format=function(a,b,c){return e(b).format(a,c)},e.unmask=function(a,b){return e(b).unmaskedvalue(a)},e.isValid=function(a,b){return e(b).isValid(a)},e.remove=function(b){a.each(b,function(a,b){b.inputmask&&b.inputmask.remove()})},e.escapeRegex=function(a){var b=["/",".","*","+","?","|","(",")","[","]","{","}","\\","$","^"];return a.replace(new RegExp("(\\"+b.join("|\\")+")","gim"),"\\$1")},e.keyCode={ALT:18,BACKSPACE:8,BACKSPACE_SAFARI:127,CAPS_LOCK:20,COMMA:188,COMMAND:91,COMMAND_LEFT:91,COMMAND_RIGHT:93,CONTROL:17,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,INSERT:45,LEFT:37,MENU:93,NUMPAD_ADD:107,NUMPAD_DECIMAL:110,NUMPAD_DIVIDE:111,NUMPAD_ENTER:108,NUMPAD_MULTIPLY:106,NUMPAD_SUBTRACT:109,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SHIFT:16,SPACE:32,TAB:9,UP:38,WINDOWS:91,X:88},e});