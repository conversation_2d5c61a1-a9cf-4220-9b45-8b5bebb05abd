<?php
require_once('includes/script.php');
require_once('session/Login.php');
require_once('includes/api_cache.php');
require_once('includes/DeviceManager.php');

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

// Check if user is logged in
if(!isset($_SESSION['official_username'])){
    header('location: index.php');
    exit;
}

$connection = $model->TemporaryConnection();
$deviceManager = new DeviceManager($connection);

// Get ERP employees with caching
$all_employees = [];
try {
    $all_employees = $apiCache->get('all_employees_biometric', function() {
        $employees = [];
        $page = 1;
        $per_page = 100;
        
        do {
            $response = fetchApiEmployeesCached($page, $per_page);
            if (!empty($response['data'])) {
                $employees = array_merge($employees, $response['data']);
                $page++;
            } else {
                break;
            }
        } while (count($response['data']) == $per_page && $page <= 10); // Safety limit
        
        return $employees;
    }, 600); // 10 minute cache
} catch (Exception $e) {
    error_log("Failed to get ERP employees: " . $e->getMessage());
}

// Get biometric registration status
$biometric_registered_query = "SELECT employee_id FROM biometric_users WHERE status = 'active'";
$biometric_result = mysqli_query($connection, $biometric_registered_query);
$biometric_registered = [];
while ($row = mysqli_fetch_assoc($biometric_result)) {
    $biometric_registered[] = $row['employee_id'];
}

// Calculate statistics
$total_erp_employees = count($all_employees);
$total_biometric_registered = count($biometric_registered);
$not_registered = $total_erp_employees - $total_biometric_registered;

// Get today's attendance from biometric
$today = date('Y-m-d');
$attendance_query = "SELECT 
    COUNT(DISTINCT employee_id) as present_count,
    COUNT(CASE WHEN time_out_morning IS NULL OR time_out_afternoon IS NULL THEN 1 END) as incomplete_count
    FROM attendance 
    WHERE date = '$today' AND (status_morning = 1 OR status_afternoon = 1)";
$attendance_result = mysqli_query($connection, $attendance_query);
$attendance_stats = mysqli_fetch_assoc($attendance_result);

$present_today = $attendance_stats['present_count'] ?? 0;
$incomplete_today = $attendance_stats['incomplete_count'] ?? 0;
$absent_today = $total_biometric_registered - $present_today;

// Handle bulk registration
if (isset($_POST['bulk_register'])) {
    $selected_employees = $_POST['selected_employees'] ?? [];
    $success_count = 0;
    $error_count = 0;
    
    foreach ($selected_employees as $employee_id) {
        try {
            // Find employee in ERP data
            $employee_data = null;
            foreach ($all_employees as $emp) {
                if ($emp['employee_id'] == $employee_id) {
                    $employee_data = $emp;
                    break;
                }
            }
            
            if ($employee_data) {
                // Register in biometric_users table
                $insert_query = "INSERT INTO biometric_users (employee_id, fullname, status, created_at) 
                               VALUES (?, ?, 'pending', NOW()) 
                               ON DUPLICATE KEY UPDATE status = 'pending', updated_at = NOW()";
                $stmt = mysqli_prepare($connection, $insert_query);
                $fullname = trim(($employee_data['first_name'] ?? '') . ' ' . ($employee_data['last_name'] ?? ''));
                mysqli_stmt_bind_param($stmt, 'ss', $employee_id, $fullname);
                
                if (mysqli_stmt_execute($stmt)) {
                    $success_count++;
                } else {
                    $error_count++;
                }
                mysqli_stmt_close($stmt);
            }
        } catch (Exception $e) {
            $error_count++;
            error_log("Bulk registration error for $employee_id: " . $e->getMessage());
        }
    }
    
    $bulk_message = "Bulk registration completed: $success_count successful, $error_count errors";
}

// Handle payroll generation
if (isset($_POST['generate_payroll'])) {
    $payroll_month = $_POST['payroll_month'];
    $payroll_year = $_POST['payroll_year'];
    
    // Generate payroll based on biometric attendance
    $payroll_query = "INSERT INTO payroll_records (employee_id, month, year, total_days_worked, total_hours, basic_pay, generated_at)
                     SELECT 
                         a.employee_id,
                         '$payroll_month' as month,
                         '$payroll_year' as year,
                         COUNT(DISTINCT a.date) as total_days_worked,
                         SUM(
                             CASE 
                                 WHEN a.time_out_morning IS NOT NULL AND a.time_in_morning IS NOT NULL 
                                 THEN TIMESTAMPDIFF(HOUR, a.time_in_morning, a.time_out_morning)
                                 ELSE 0
                             END +
                             CASE 
                                 WHEN a.time_out_afternoon IS NOT NULL AND a.time_in_afternoon IS NOT NULL 
                                 THEN TIMESTAMPDIFF(HOUR, a.time_in_afternoon, a.time_out_afternoon)
                                 ELSE 0
                             END
                         ) as total_hours,
                         (COUNT(DISTINCT a.date) * 500) as basic_pay,
                         NOW() as generated_at
                     FROM attendance a
                     INNER JOIN biometric_users bu ON a.employee_id = bu.employee_id
                     WHERE MONTH(a.date) = '$payroll_month' 
                       AND YEAR(a.date) = '$payroll_year'
                       AND (a.status_morning = 1 OR a.status_afternoon = 1)
                     GROUP BY a.employee_id
                     ON DUPLICATE KEY UPDATE 
                         total_days_worked = VALUES(total_days_worked),
                         total_hours = VALUES(total_hours),
                         basic_pay = VALUES(basic_pay),
                         updated_at = NOW()";
    
    if (mysqli_query($connection, $payroll_query)) {
        $affected_rows = mysqli_affected_rows($connection);
        $payroll_message = "Payroll generated successfully for $affected_rows employees";
    } else {
        $payroll_message = "Error generating payroll: " . mysqli_error($connection);
    }
}
?>

<!doctype html>
<html lang="en" dir="ltr">
<head>
    <title>Biometric Integration - ERP Sync</title>
    <?php require_once('includes/script.php') ?>
</head>
<body class="">
    <div class="page">
        <div class="page-main">
            <?php require_once('includes/header.php') ?>
            <div class="my-3 my-md-5">
                <div class="container">
                    <div class="page-header mb-4">
                        <div class="row align-items-center">
                            <div class="col">
                                <h1 class="page-title text-gradient">Biometric Integration Dashboard</h1>
                                <p class="page-subtitle">ERP employee registration, attendance tracking, and payroll generation</p>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group">
                                    <button class="btn btn-primary" onclick="refreshData()">
                                        <i class="fe fe-refresh-cw mr-2"></i>Refresh Data
                                    </button>
                                    <a href="biometrics.php" class="btn btn-outline-primary">
                                        <i class="fe fe-settings mr-2"></i>Device Management
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (isset($bulk_message)): ?>
                        <div class="alert alert-info alert-dismissible">
                            <button type="button" class="close" data-dismiss="alert"></button>
                            <?php echo $bulk_message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($payroll_message)): ?>
                        <div class="alert alert-success alert-dismissible">
                            <button type="button" class="close" data-dismiss="alert"></button>
                            <?php echo $payroll_message; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Statistics Overview -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stat-card fade-in-up">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-gradient-primary text-white rounded-circle p-3 mr-3">
                                        <i class="fe fe-users" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <div class="stat-number"><?php echo number_format($total_erp_employees); ?></div>
                                        <div class="stat-label">Total ERP Employees</div>
                                        <small class="text-muted">From ERP System</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card fade-in-up" style="animation-delay: 0.1s;">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-success text-white rounded-circle p-3 mr-3">
                                        <i class="fe fe-check-circle" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <div class="stat-number"><?php echo number_format($total_biometric_registered); ?></div>
                                        <div class="stat-label">Biometric Registered</div>
                                        <small class="text-success"><?php echo round(($total_biometric_registered/$total_erp_employees)*100, 1); ?>% Coverage</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card fade-in-up" style="animation-delay: 0.2s;">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-warning text-white rounded-circle p-3 mr-3">
                                        <i class="fe fe-user-plus" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <div class="stat-number"><?php echo number_format($not_registered); ?></div>
                                        <div class="stat-label">Not Registered</div>
                                        <small class="text-warning">Pending Registration</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card fade-in-up" style="animation-delay: 0.3s;">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-info text-white rounded-circle p-3 mr-3">
                                        <i class="fe fe-clock" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <div class="stat-number"><?php echo number_format($present_today); ?></div>
                                        <div class="stat-label">Present Today</div>
                                        <small class="text-info"><?php echo date('M d, Y'); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Today's Attendance Summary -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card shadow-custom">
                                <div class="card-header bg-success text-white">
                                    <h3 class="card-title mb-0">
                                        <i class="fe fe-check mr-2"></i>Present Today
                                    </h3>
                                </div>
                                <div class="card-body text-center">
                                    <div class="h1 text-success"><?php echo $present_today; ?></div>
                                    <p class="text-muted">Employees checked in</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card shadow-custom">
                                <div class="card-header bg-danger text-white">
                                    <h3 class="card-title mb-0">
                                        <i class="fe fe-x mr-2"></i>Absent Today
                                    </h3>
                                </div>
                                <div class="card-body text-center">
                                    <div class="h1 text-danger"><?php echo $absent_today; ?></div>
                                    <p class="text-muted">Registered but absent</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card shadow-custom">
                                <div class="card-header bg-warning text-white">
                                    <h3 class="card-title mb-0">
                                        <i class="fe fe-clock mr-2"></i>Incomplete
                                    </h3>
                                </div>
                                <div class="card-body text-center">
                                    <div class="h1 text-warning"><?php echo $incomplete_today; ?></div>
                                    <p class="text-muted">Missing check-out</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs" id="biometricTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="registration-tab" data-toggle="tab" href="#registration" role="tab">
                                <i class="fe fe-user-plus mr-2"></i>Employee Registration
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="attendance-tab" data-toggle="tab" href="#attendance" role="tab">
                                <i class="fe fe-calendar mr-2"></i>Attendance Tracking
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="payroll-tab" data-toggle="tab" href="#payroll" role="tab">
                                <i class="fe fe-dollar-sign mr-2"></i>Payroll Generation
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content" id="biometricTabsContent">
                        <!-- Employee Registration Tab -->
                        <div class="tab-pane fade show active" id="registration" role="tabpanel">
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h3 class="card-title">ERP Employee Registration Status</h3>
                                    <div class="card-options">
                                        <button class="btn btn-primary btn-sm" onclick="selectAllUnregistered()">
                                            Select All Unregistered
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form method="POST" id="bulkRegistrationForm">
                                        <div class="table-responsive">
                                            <table class="table table-striped" id="employeeRegistrationTable">
                                                <thead>
                                                    <tr>
                                                        <th width="50">
                                                            <input type="checkbox" id="selectAll">
                                                        </th>
                                                        <th>Employee ID</th>
                                                        <th>Full Name</th>
                                                        <th>Department</th>
                                                        <th>Position</th>
                                                        <th>Status</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($all_employees as $employee): ?>
                                                        <?php 
                                                        $employee_id = $employee['employee_id'] ?? '';
                                                        $is_registered = in_array($employee_id, $biometric_registered);
                                                        $fullname = trim(($employee['first_name'] ?? '') . ' ' . ($employee['last_name'] ?? ''));
                                                        ?>
                                                        <tr class="<?php echo $is_registered ? 'table-success' : 'table-warning'; ?>">
                                                            <td>
                                                                <?php if (!$is_registered): ?>
                                                                    <input type="checkbox" name="selected_employees[]" value="<?php echo htmlspecialchars($employee_id); ?>" class="employee-checkbox">
                                                                <?php endif; ?>
                                                            </td>
                                                            <td><?php echo htmlspecialchars($employee_id); ?></td>
                                                            <td><?php echo htmlspecialchars($fullname); ?></td>
                                                            <td><?php echo htmlspecialchars($employee['department_name'] ?? 'N/A'); ?></td>
                                                            <td><?php echo htmlspecialchars($employee['position_name'] ?? 'N/A'); ?></td>
                                                            <td>
                                                                <?php if ($is_registered): ?>
                                                                    <span class="badge badge-success">Registered</span>
                                                                <?php else: ?>
                                                                    <span class="badge badge-warning">Not Registered</span>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td>
                                                                <?php if (!$is_registered): ?>
                                                                    <button type="button" class="btn btn-sm btn-primary" onclick="registerSingle('<?php echo htmlspecialchars($employee_id); ?>')">
                                                                        Register
                                                                    </button>
                                                                <?php else: ?>
                                                                    <span class="text-success">✓ Registered</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="card-footer">
                                            <button type="submit" name="bulk_register" class="btn btn-primary">
                                                <i class="fe fe-users mr-2"></i>Bulk Register Selected
                                            </button>
                                            <span class="ml-3 text-muted">
                                                <span id="selectedCount">0</span> employees selected
                                            </span>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Attendance Tracking Tab -->
                        <div class="tab-pane fade" id="attendance" role="tabpanel">
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h3 class="card-title">Biometric Attendance Tracking</h3>
                                    <div class="card-options">
                                        <input type="date" id="attendanceDate" class="form-control form-control-sm" value="<?php echo date('Y-m-d'); ?>" onchange="loadAttendanceData()">
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped" id="attendanceTable">
                                            <thead>
                                                <tr>
                                                    <th>Employee ID</th>
                                                    <th>Full Name</th>
                                                    <th>Morning In</th>
                                                    <th>Morning Out</th>
                                                    <th>Afternoon In</th>
                                                    <th>Afternoon Out</th>
                                                    <th>Total Hours</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $attendance_detail_query = "SELECT
                                                    a.employee_id,
                                                    bu.fullname,
                                                    a.time_in_morning,
                                                    a.time_out_morning,
                                                    a.time_in_afternoon,
                                                    a.time_out_afternoon,
                                                    a.status_morning,
                                                    a.status_afternoon
                                                FROM attendance a
                                                INNER JOIN biometric_users bu ON a.employee_id = bu.employee_id
                                                WHERE a.date = '$today'
                                                ORDER BY a.time_in_morning ASC";

                                                $attendance_detail_result = mysqli_query($connection, $attendance_detail_query);

                                                while ($row = mysqli_fetch_assoc($attendance_detail_result)):
                                                    $total_hours = 0;
                                                    $status = 'Absent';

                                                    if ($row['time_in_morning'] && $row['time_out_morning']) {
                                                        $morning_hours = (strtotime($row['time_out_morning']) - strtotime($row['time_in_morning'])) / 3600;
                                                        $total_hours += $morning_hours;
                                                    }

                                                    if ($row['time_in_afternoon'] && $row['time_out_afternoon']) {
                                                        $afternoon_hours = (strtotime($row['time_out_afternoon']) - strtotime($row['time_in_afternoon'])) / 3600;
                                                        $total_hours += $afternoon_hours;
                                                    }

                                                    if ($row['status_morning'] || $row['status_afternoon']) {
                                                        if (($row['time_out_morning'] && $row['time_out_afternoon']) ||
                                                            ($row['time_out_morning'] && !$row['time_in_afternoon'])) {
                                                            $status = 'Complete';
                                                        } else {
                                                            $status = 'Incomplete';
                                                        }
                                                    }
                                                ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($row['employee_id']); ?></td>
                                                    <td><?php echo htmlspecialchars($row['fullname']); ?></td>
                                                    <td><?php echo $row['time_in_morning'] ? date('H:i', strtotime($row['time_in_morning'])) : '-'; ?></td>
                                                    <td><?php echo $row['time_out_morning'] ? date('H:i', strtotime($row['time_out_morning'])) : '-'; ?></td>
                                                    <td><?php echo $row['time_in_afternoon'] ? date('H:i', strtotime($row['time_in_afternoon'])) : '-'; ?></td>
                                                    <td><?php echo $row['time_out_afternoon'] ? date('H:i', strtotime($row['time_out_afternoon'])) : '-'; ?></td>
                                                    <td><?php echo number_format($total_hours, 2); ?> hrs</td>
                                                    <td>
                                                        <span class="badge badge-<?php
                                                            echo $status == 'Complete' ? 'success' :
                                                                ($status == 'Incomplete' ? 'warning' : 'danger');
                                                        ?>">
                                                            <?php echo $status; ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payroll Generation Tab -->
                        <div class="tab-pane fade" id="payroll" role="tabpanel">
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h3 class="card-title">Biometric-Based Payroll Generation</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h4 class="card-title">Generate New Payroll</h4>
                                                </div>
                                                <div class="card-body">
                                                    <form method="POST" id="payrollForm">
                                                        <div class="form-group">
                                                            <label class="form-label">Payroll Month</label>
                                                            <select name="payroll_month" class="form-control" required>
                                                                <?php for ($i = 1; $i <= 12; $i++): ?>
                                                                    <option value="<?php echo $i; ?>" <?php echo $i == date('n') ? 'selected' : ''; ?>>
                                                                        <?php echo date('F', mktime(0, 0, 0, $i, 1)); ?>
                                                                    </option>
                                                                <?php endfor; ?>
                                                            </select>
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="form-label">Payroll Year</label>
                                                            <select name="payroll_year" class="form-control" required>
                                                                <?php for ($year = date('Y') - 2; $year <= date('Y') + 1; $year++): ?>
                                                                    <option value="<?php echo $year; ?>" <?php echo $year == date('Y') ? 'selected' : ''; ?>>
                                                                        <?php echo $year; ?>
                                                                    </option>
                                                                <?php endfor; ?>
                                                            </select>
                                                        </div>
                                                        <button type="submit" name="generate_payroll" class="btn btn-primary">
                                                            <i class="fe fe-dollar-sign mr-2"></i>Generate Payroll
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h4 class="card-title">Payroll Summary</h4>
                                                </div>
                                                <div class="card-body">
                                                    <?php
                                                    $current_month = date('n');
                                                    $current_year = date('Y');

                                                    $payroll_summary_query = "SELECT
                                                        COUNT(*) as total_employees,
                                                        SUM(total_days_worked) as total_days,
                                                        SUM(total_hours) as total_hours,
                                                        SUM(basic_pay) as total_payroll
                                                    FROM payroll_records
                                                    WHERE month = '$current_month' AND year = '$current_year'";

                                                    $payroll_summary_result = mysqli_query($connection, $payroll_summary_query);
                                                    $payroll_summary = mysqli_fetch_assoc($payroll_summary_result);
                                                    ?>

                                                    <div class="row">
                                                        <div class="col-6">
                                                            <div class="text-center">
                                                                <div class="h3 text-primary"><?php echo $payroll_summary['total_employees'] ?? 0; ?></div>
                                                                <div class="text-muted">Employees</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="text-center">
                                                                <div class="h3 text-success">₱<?php echo number_format($payroll_summary['total_payroll'] ?? 0, 2); ?></div>
                                                                <div class="text-muted">Total Payroll</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.22/js/dataTables.bootstrap4.min.js"></script>

    <script>
    $(document).ready(function() {
        // Initialize DataTables
        $('#employeeRegistrationTable').DataTable({
            "pageLength": 25,
            "order": [[5, "desc"]], // Sort by status (registered first)
            "columnDefs": [
                { "orderable": false, "targets": [0, 6] }
            ]
        });

        $('#attendanceTable').DataTable({
            "pageLength": 25,
            "order": [[2, "asc"]] // Sort by morning in time
        });

        // Handle select all checkbox
        $('#selectAll').change(function() {
            $('.employee-checkbox').prop('checked', this.checked);
            updateSelectedCount();
        });

        // Handle individual checkboxes
        $('.employee-checkbox').change(function() {
            updateSelectedCount();

            // Update select all checkbox
            var totalCheckboxes = $('.employee-checkbox').length;
            var checkedCheckboxes = $('.employee-checkbox:checked').length;
            $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
        });

        // Update selected count
        function updateSelectedCount() {
            var count = $('.employee-checkbox:checked').length;
            $('#selectedCount').text(count);
        }

        // Initialize count
        updateSelectedCount();
    });

    // Select all unregistered employees
    function selectAllUnregistered() {
        $('.employee-checkbox').prop('checked', true);
        $('#selectAll').prop('checked', true);
        updateSelectedCount();
    }

    // Register single employee
    function registerSingle(employeeId) {
        if (confirm('Register employee ' + employeeId + ' for biometric access?')) {
            // Create a temporary form to submit
            var form = $('<form method="POST">' +
                '<input type="hidden" name="selected_employees[]" value="' + employeeId + '">' +
                '<input type="hidden" name="bulk_register" value="1">' +
                '</form>');
            $('body').append(form);
            form.submit();
        }
    }

    // Refresh data
    function refreshData() {
        location.reload();
    }

    // Load attendance data for specific date
    function loadAttendanceData() {
        var selectedDate = $('#attendanceDate').val();
        if (selectedDate) {
            window.location.href = '?date=' + selectedDate + '#attendance';
        }
    }

    // View payroll detail
    function viewPayrollDetail(employeeId, month, year) {
        alert('Payroll detail for Employee: ' + employeeId + ' - ' + month + '/' + year);
        // You can implement a modal or redirect to detailed view
    }

    // Auto-refresh attendance data every 5 minutes
    setInterval(function() {
        if ($('#attendance-tab').hasClass('active')) {
            loadAttendanceData();
        }
    }, 300000); // 5 minutes

    // Handle tab switching
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        var target = $(e.target).attr("href");

        if (target === '#attendance') {
            // Refresh attendance data when switching to attendance tab
            setTimeout(function() {
                $('#attendanceTable').DataTable().ajax.reload();
            }, 100);
        }
    });

    // Form validation
    $('#payrollForm').submit(function(e) {
        var month = $('select[name="payroll_month"]').val();
        var year = $('select[name="payroll_year"]').val();

        if (!confirm('Generate payroll for ' + $('select[name="payroll_month"] option:selected').text() + ' ' + year + '?')) {
            e.preventDefault();
        }
    });

    // Bulk registration form validation
    $('#bulkRegistrationForm').submit(function(e) {
        var selectedCount = $('.employee-checkbox:checked').length;

        if (selectedCount === 0) {
            alert('Please select at least one employee to register.');
            e.preventDefault();
            return false;
        }

        if (!confirm('Register ' + selectedCount + ' employees for biometric access?')) {
            e.preventDefault();
            return false;
        }
    });
    </script>
</body>
</html>
