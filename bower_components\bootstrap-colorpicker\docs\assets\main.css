.container {
  background: #fff;
}

h4 ~ p {
  padding-left: 20px;
}

.well .markup,
.example-content .example-code {
  background: #2B2B2B;
  color: #BABABA;
  position: relative;
  padding: 15px 15px 15px;
  margin: 15px 0 0 0;
  border-radius: 0 0 4px 4px;
  box-shadow: none;
  font-size: 12px;
  line-height: 1.42857143;
  word-break: break-all;
  word-wrap: break-word;
  border: 1px solid #000;
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
  white-space: pre;
  overflow: auto;
}

.well .markup::after {
  content: "Source code";
  position: absolute;
  top: 15px;
  left: 15px;
  font-size: 12px;
  font-weight: bold;
  color: #eee;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.share-btn {
  vertical-align: middle;
  display: inline-block;
  font-size: 12px;
  line-height: 1.5;
}


.example-title{
  font-size: 14px;
  margin: 10px 0 10px 2px;
  font-weight: bold;
}
.example-description{
  margin: 10px 0 10px 2px;
}
.example-code{
  display:none;
}
.example-content-widget{
  margin-bottom: 2px;
}
