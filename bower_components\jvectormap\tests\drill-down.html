<!DOCTYPE html>
<html>
<head>
  <title>jVectorMap demo</title>
  <link rel="stylesheet" media="all" href="../jquery-jvectormap.css"/>
  <script src="assets/jquery-1.8.2.js"></script>
  <script src="../jquery-jvectormap.js"></script>
  <script src="../lib/jquery-mousewheel.js"></script>

  <script src="../src/jvectormap.js"></script>

  <script src="../src/abstract-element.js"></script>
  <script src="../src/abstract-canvas-element.js"></script>
  <script src="../src/abstract-shape-element.js"></script>

  <script src="../src/svg-element.js"></script>
  <script src="../src/svg-group-element.js"></script>
  <script src="../src/svg-canvas-element.js"></script>
  <script src="../src/svg-shape-element.js"></script>
  <script src="../src/svg-path-element.js"></script>
  <script src="../src/svg-circle-element.js"></script>
  <script src="../src/svg-image-element.js"></script>
  <script src="../src/svg-text-element.js"></script>

  <script src="../src/vml-element.js"></script>
  <script src="../src/vml-group-element.js"></script>
  <script src="../src/vml-canvas-element.js"></script>
  <script src="../src/vml-shape-element.js"></script>
  <script src="../src/vml-path-element.js"></script>
  <script src="../src/vml-circle-element.js"></script>

  <script src="../src/map-object.js"></script>
  <script src="../src/region.js"></script>
  <script src="../src/marker.js"></script>

  <script src="../src/vector-canvas.js"></script>
  <script src="../src/simple-scale.js"></script>
  <script src="../src/numeric-scale.js"></script>
  <script src="../src/ordinal-scale.js"></script>
  <script src="../src/color-scale.js"></script>
  <script src="../src/data-series.js"></script>
  <script src="../src/proj.js"></script>
  <script src="../src/map.js"></script>
  <script src="../src/multimap.js"></script>

  <script src="assets/jquery-jvectormap-data-us-lcc-en.js"></script>
  <script>
    $(function(){
      var map1,
          map2;

      new jvm.MultiMap({
        container: $('#map1'),
        maxLevel: 1,
        mapUrlByCode: function(code, multiMap){
          return 'assets/us/jquery-jvectormap-data-'+code.toLowerCase()+'-'+multiMap.defaultProjection+'-en.js';
        },
        main: {
          map: 'us_lcc_en'
        }
      });
    });
  </script>
</head>
<body>
  <div id="map1" style="width: 900px; height: 600px"></div>
</body>
</html>