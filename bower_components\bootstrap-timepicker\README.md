Timepicker for Twitter Bootstrap
=======
[![Build Status](https://travis-ci.org/jdewit/bootstrap-timepicker.svg?branch=gh-pages)](https://travis-ci.org/jdewit/bootstrap-timepicker)

A simple timepicker component for Twitter Bootstrap.

Status
======
Please take a look at the `CHANGELOG.md` and the issues tab for issues we're
working on and their relative priorities.

Installation
============

This project is registered as a <a href="http://bower.io">Bower</a> package,
and can be installed with the following command:

```bash
bower install bootstrap-timepicker
```

You can also download our latest release (and any previous release) 
<a href="https://github.com/jdewit/bootstrap-timepicker/releases">here</a>.

Demos & Documentation
=====================

View <a href="http://jdewit.github.com/bootstrap-timepicker">demos & documentation</a>.

Support
=======

If you make money using this timepicker, please consider 
supporting its development.

<a href="http://www.pledgie.com/campaigns/19125"><img alt="Click here to support bootstrap-timepicker!" src="http://www.pledgie.com/campaigns/19125.png?skin_name=chrome" border="0" target="_blank"/></a> <a class="FlattrButton" style="display:none;" rev="flattr;button:compact;" href="http://jdewit.github.com/bootstrap-timepicker"></a> <noscript><a href="http://flattr.com/thing/1116513/Bootstrap-Timepicker" target="_blank"> <img src="http://api.flattr.com/button/flattr-badge-large.png" alt="Flattr this" title="Flattr this" border="0" /></a></noscript>

Contributing
============

1. Install <a href="www.nodejs.org">NodeJS</a> and <a href="www.npmjs.org">Node Package Manager</a>.

2. Install packages

```bash
npm install
```

3. Use <a href="https://github.com/twitter/bower">Bower</a> to get the dev dependencies.

```bash 
bower install
```

4. Use <a href="www.gruntjs.com">Grunt</a> to run tests, compress assets, etc. 

```bash 
grunt test // run jshint and jasmine tests
grunt watch // run jsHint and Jasmine tests whenever a file is changed
grunt compile // minify the js and css files
```

- Please make it easy on me by covering any new features or issues 
  with <a href="http://pivotal.github.com/jasmine">Jasmine</a> tests.
- If your changes need documentation, please take the time to update the docs.

Acknowledgements
================

Thanks to everyone who have given feedback and submitted pull requests. A 
list of all the contributors can be found <a href="https://github.com/jdewit/bootstrap-timepicker/graphs/contributors">here</a>.

Special thanks to @eternicode and his <a href="https://github.com/eternicode/bootstrap-datepicker">Twitter Datepicker</a> for inspiration.
