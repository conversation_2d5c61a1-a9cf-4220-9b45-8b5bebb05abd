body {
    font-size: 15px;
    line-height: 1.6em;
    position: relative;
    margin: 0;
}
.container {
    width: 95%;
    max-width: 1260px;
}
p, pre {
    margin-bottom: 2em;
}
.main h2 {
    font-weight: bold;
    margin: 60px 0 20px 0;
}
.main h3 {
    margin: 60px 0 20px 0;
}
.main h4 {
    margin: 0 0 10px 0;
    font-weight: bold;
}
ul.nobullets {
    margin: 0;
    padding: 0;
    list-style-position: inside;
}
li {
    padding-bottom: 1em;
}
#sidebar {
    top: 20px;
    width: 300px;
}
#sidebar ul {
    margin-bottom: 5px;
}
#sidebar li {
    margin-bottom: 0;
    padding-bottom: 0;
}
#sidebar li ul {
    display: none;
}
#sidebar li.active ul {
    display: block;
}
#sidebar li li {
    padding: 4px 0;
}
input[type="text"] {
    padding: 6px;
    width: 100%;
    border-radius: 4px;
}
.navbar {
    text-align: left;
    margin: 0;
    border: 0;
}
.navbar-inverse {
    background: #222;
}
.navbar .container {
    padding: 0 20px;
}
.navbar-nav li a:link, .navbar-nav li a:visited {
    font-weight: bold;
    color: #fff;
    font-size: 16px;
}
.navbar-nav li {
    background: #fff;
}
.navbar-nav li a:hover {
    opacity: 0.8;
}
.navbar-nav li {
    padding: 0;
}
.navbar-inverse .navbar-text {
        margin: 18px 0 0 0;
        color: #eee;
}
#footer {
    background: #222;
    margin-top: 80px;
    padding: 30px;
    color: #fff;
    text-align: center;
}
#footer a:link, #footer a:visited {
    color: #fff;
    border-bottom: 1px dotted #fff;
}
#jumbo {
    background: #f5f5f5 linear-gradient(to bottom,#eee 0,#f5f5f5 100%);
    color: #000;
    padding: 30px 0;
    margin-bottom: 30px;
}
#jumbo .btn {
    border-radius: 0;
}
#config .demo { position: relative; }
#config .demo i { position: absolute; bottom: 10px; right: 24px; top: auto; cursor: pointer; }

#rightcol {
    margin-left: 330px;
}

#nav-spy {
    float: left;
    width: 300px;
}

@media (max-width: 980px) {
    #rightcol {
        margin-left: 0;
    }
    #nav-spy {
        float: none;
        position: relative;
    }
}