<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once('includes/script.php');
require_once('session/Login.php');
require_once('includes/BiometricSDK.php');
require_once('includes/DeviceManager.php');

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

// Check if user is logged in
if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    header("location:index.php?utm_campaign=expired");
}

// Get session variables
$password = $_SESSION['official_password'];
$username = $_SESSION['official_username'];
$uid = $_SESSION['official_id'];

// Get admin information
$connection = $model->TemporaryConnection();
$query = $model->GetAdministrator($username, $password);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];

// Get device ID from URL
$device_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Check if device exists
$device_query = "SELECT * FROM biometric_devices WHERE id = $device_id";
$device_result = mysqli_query($connection, $device_query);

if(mysqli_num_rows($device_result) == 0) {
    // Device not found, redirect to devices list
    header("location:biometrics.php?error=device_not_found");
    exit;
}

$device = mysqli_fetch_assoc($device_result);
$status_message = "";

// Initialize SDK
$deviceManager = new DeviceManager($connection);

// Handle form submissions
if(isset($_POST['save_config'])) {
    $config_type = $_POST['config_type'];
    
    switch($config_type) {
        case 'general':
            // Update general settings
            $config = [
                'device_name' => $_POST['device_name'],
                'volume' => intval($_POST['volume']),
                'language' => $_POST['language'],
                'display_mode' => $_POST['display_mode']
            ];
            break;
            
        case 'recognition':
            // Update recognition settings
            $config = [
                'face_threshold' => floatval($_POST['face_threshold']),
                'liveness_check' => isset($_POST['liveness_check']) ? 1 : 0,
                'mask_detection' => isset($_POST['mask_detection']) ? 1 : 0,
                'temperature_check' => isset($_POST['temperature_check']) ? 1 : 0,
                'temperature_threshold' => floatval($_POST['temperature_threshold'])
            ];
            
            try {
                $result = $deviceManager->setRecognitionConfig($device, $config);
                if($result['success']) {
                    $status_message = '<div class="alert alert-success">Recognition settings updated successfully</div>';
                } else {
                    $status_message = '<div class="alert alert-danger">Failed to update recognition settings: ' . $result['message'] . '</div>';
                }
            } catch (Exception $e) {
                $status_message = '<div class="alert alert-danger">Error: ' . $e->getMessage() . '</div>';
            }
            break;
            
        case 'server':
            // Update server settings
            $config = [
                'server_url' => $_POST['server_url'],
                'server_port' => intval($_POST['server_port']),
                'heartbeat_interval' => intval($_POST['heartbeat_interval'])
            ];
            
            // Set attendance upload URL
            $callback_url = $_POST['callback_url'];
            try {
                $result = $deviceManager->setUploadUrl($device, $callback_url);
                if($result['success']) {
                    // Set personnel upload URL
                    $personnel_url = $_POST['personnel_url'];
                    $result = $deviceManager->setPersonnelUploadUrl($device, $personnel_url);
                    if($result['success']) {
                        $status_message = '<div class="alert alert-success">Server settings updated successfully</div>';
                    } else {
                        $status_message = '<div class="alert alert-danger">Failed to update personnel upload URL: ' . $result['message'] . '</div>';
                    }
                } else {
                    $status_message = '<div class="alert alert-danger">Failed to update attendance upload URL: ' . $result['message'] . '</div>';
                }
            } catch (Exception $e) {
                $status_message = '<div class="alert alert-danger">Error: ' . $e->getMessage() . '</div>';
            }
            break;
    }
    
    // Save configuration to database
    if(isset($config)) {
        $config_json = mysqli_real_escape_string($connection, json_encode($config));
        
        // Check if config already exists
        $check_query = "SELECT id FROM biometric_device_configs WHERE device_id = $device_id AND config_type = '$config_type'";
        $check_result = mysqli_query($connection, $check_query);
        
        if(mysqli_num_rows($check_result) > 0) {
            // Update existing config
            $config_id = mysqli_fetch_assoc($check_result)['id'];
            $update = "UPDATE biometric_device_configs SET config_data = '$config_json', updated_at = NOW() 
                      WHERE id = $config_id";
            mysqli_query($connection, $update);
        } else {
            // Insert new config
            $insert = "INSERT INTO biometric_device_configs (device_id, config_type, config_data, created_at, updated_at) 
                      VALUES ($device_id, '$config_type', '$config_json', NOW(), NOW())";
            mysqli_query($connection, $insert);
        }
    }
}

// Handle device actions
if(isset($_POST['device_action'])) {
    $action = $_POST['device_action'];
    
    try {
        switch($action) {
            case 'reboot':
                $result = $deviceManager->rebootDevice($device);
                if($result['success']) {
                    $status_message = '<div class="alert alert-success">Device is rebooting</div>';
                } else {
                    $status_message = '<div class="alert alert-danger">Failed to reboot device: ' . $result['message'] . '</div>';
                }
                break;
                
            case 'reset_all':
                $result = $deviceManager->resetDevice($device, 'all');
                if($result['success']) {
                    $status_message = '<div class="alert alert-success">Device has been reset</div>';
                } else {
                    $status_message = '<div class="alert alert-danger">Failed to reset device: ' . $result['message'] . '</div>';
                }
                break;
                
            case 'reset_users':
                $result = $deviceManager->resetDevice($device, 'users');
                if($result['success']) {
                    $status_message = '<div class="alert alert-success">User data has been reset</div>';
                } else {
                    $status_message = '<div class="alert alert-danger">Failed to reset user data: ' . $result['message'] . '</div>';
                }
                break;
                
            case 'reset_logs':
                $result = $deviceManager->resetDevice($device, 'logs');
                if($result['success']) {
                    $status_message = '<div class="alert alert-success">Attendance logs have been reset</div>';
                } else {
                    $status_message = '<div class="alert alert-danger">Failed to reset attendance logs: ' . $result['message'] . '</div>';
                }
                break;
                
            case 'sync_users':
                // Get all employees
                $employees_query = "SELECT employee_id, CONCAT(firstname, ' ', lastname) AS name FROM employees WHERE status = 'Active'";
                $employees_result = mysqli_query($connection, $employees_query);
                
                $users = [];
                while($employee = mysqli_fetch_assoc($employees_result)) {
                    $users[] = [
                        'personSn' => $employee['employee_id'],
                        'personName' => $employee['name'],
                        'cardNo' => $employee['employee_id'], // Using employee_id as card number
                        'personType' => 0, // Regular employee
                        'verifyMode' => 1, // Face recognition
                        'viceCard' => '', // No vice card
                        'personStatus' => 1 // Active
                    ];
                }
                
                $result = $deviceManager->syncUsers($device, $users);
                if($result['success']) {
                    $status_message = '<div class="alert alert-success">Users synchronized successfully</div>';
                } else {
                    $status_message = '<div class="alert alert-danger">Failed to synchronize users: ' . $result['message'] . '</div>';
                }
                break;
        }
    } catch (Exception $e) {
        $status_message = '<div class="alert alert-danger">Error: ' . $e->getMessage() . '</div>';
    }
}

// Get current configurations
$configs = [];
$config_query = "SELECT config_type, config_data FROM biometric_device_configs WHERE device_id = $device_id";
$config_result = mysqli_query($connection, $config_query);

while($config = mysqli_fetch_assoc($config_result)) {
    $configs[$config['config_type']] = json_decode($config['config_data'], true);
}

// Default configurations
$default_configs = [
    'general' => [
        'device_name' => $device['device_name'],
        'volume' => 70,
        'language' => 'en',
        'display_mode' => 'standard'
    ],
    'recognition' => [
        'face_threshold' => 0.7,
        'liveness_check' => 1,
        'mask_detection' => 0,
        'temperature_check' => 0,
        'temperature_threshold' => 37.3
    ],
    'server' => [
        'server_url' => '',
        'server_port' => 80,
        'heartbeat_interval' => 60
    ]
];

// Merge defaults with saved configs
foreach($default_configs as $type => $default) {
    if(!isset($configs[$type])) {
        $configs[$type] = $default;
    } else {
        $configs[$type] = array_merge($default, $configs[$type]);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <title>Biometric Device Configuration - Payroll Management System</title>
</head>
<body>
    <div class="page">
        <div class="page-main" id="app">
            <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
              <div class="container">
                <div class="row align-items-center">
                  <div class="d-flex">
                    <?php require_once('includes/header.php') ?>
                  </div>
                  <div class="col-lg order-lg-first">
                    <?php require_once('includes/subheader.php') ?> 
                  </div>
                </div>
              </div>
            </div>
            <div class="my-3 my-md-5">
                <div class="container">
                    <div class="page-header">
                        <h1 class="page-title">Biometric Device Configuration</h1>
                        <div class="page-subtitle">Device: <?php echo $device['device_name']; ?></div>
                        <div class="page-options d-flex">
                            <a href="biometrics.php" class="btn btn-secondary">
                                <i class="fe fe-arrow-left mr-2"></i> Back to Devices
                            </a>
                        </div>
                    </div>
                    
                    <?php echo $status_message; ?>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Device Information</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label class="form-label">Device Name</label>
                                        <div class="form-control-plaintext"><?php echo $device['device_name']; ?></div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Location</label>
                                        <div class="form-control-plaintext"><?php echo $device['location']; ?></div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">IP Address</label>
                                        <div class="form-control-plaintext"><?php echo $device['ip_address']; ?></div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Port</label>
                                        <div class="form-control-plaintext"><?php echo $device['port']; ?></div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Serial Number</label>
                                        <div class="form-control-plaintext"><?php echo $device['serial_number']; ?></div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Status</label>
                                        <div class="form-control-plaintext">
                                            <?php if($device['status'] == 'Active'): ?>
                                                <span class="badge badge-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge badge-danger">Inactive</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <form action="" method="post">
                                        <div class="d-flex">
                                            <div class="dropdown">
                                                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                                                    Device Actions
                                                </button>
                                                <div class="dropdown-menu">
                                                    <button type="submit" name="device_action" value="reboot" class="dropdown-item">
                                                        <i class="dropdown-icon fe fe-refresh-cw"></i> Reboot Device
                                                    </button>
                                                    <button type="submit" name="device_action" value="sync_users" class="dropdown-item">
                                                        <i class="dropdown-icon fe fe-users"></i> Sync Users
                                                    </button>
                                                    <div class="dropdown-divider"></div>
                                                    <button type="submit" name="device_action" value="reset_logs" class="dropdown-item text-warning">
                                                        <i class="dropdown-icon fe fe-trash-2"></i> Reset Attendance Logs
                                                    </button>
                                                    <button type="submit" name="device_action" value="reset_users" class="dropdown-item text-warning">
                                                        <i class="dropdown-icon fe fe-user-x"></i> Reset User Data
                                                    </button>
                                                    <button type="submit" name="device_action" value="reset_all" class="dropdown-item text-danger">
                                                        <i class="dropdown-icon fe fe-alert-triangle"></i> Factory Reset
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Device Configuration</h3>
                                </div>
                                <div class="card-body">
                                    <div class="dimmer active" id="loading-dimmer">
                                        <div class="loader"></div>
                                        <div class="dimmer-content">
                                            <ul class="nav nav-tabs" id="configTabs" role="tablist">
                                                <li class="nav-item">
                                                    <a class="nav-link active" id="general-tab" data-toggle="tab" href="#general" role="tab">
                                                        General
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" id="recognition-tab" data-toggle="tab" href="#recognition" role="tab">
                                                        Recognition
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" id="server-tab" data-toggle="tab" href="#server" role="tab">
                                                        Server
                                                    </a>
                                                </li>
                                            </ul>
                                            
                                            <div class="tab-content mt-3" id="configTabsContent">
                                                <!-- General Settings Tab -->
                                                <div class="tab-pane fade show active" id="general" role="tabpanel">
                                                    <form action="" method="post">
                                                        <input type="hidden" name="config_type" value="general">
                                                        
                                                        <div class="form-group">
                                                            <label class="form-label">Device Name</label>
                                                            <input type="text" class="form-control" name="device_name" 
                                                                value="<?php echo $configs['general']['device_name']; ?>">
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <label class="form-label">Volume</label>
                                                            <input type="range" class="form-control-range" name="volume" min="0" max="100" step="1"
                                                                value="<?php echo $configs['general']['volume']; ?>">
                                                            <small class="form-text text-muted">
                                                                Current: <span id="volume-value"><?php echo $configs['general']['volume']; ?></span>%
                                                            </small>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <label class="form-label">Language</label>
                                                            <select class="form-control" name="language">
                                                                <option value="en" <?php if($configs['general']['language'] == 'en') echo 'selected'; ?>>English</option>
                                                                <option value="es" <?php if($configs['general']['language'] == 'es') echo 'selected'; ?>>Spanish</option>
                                                                <option value="fr" <?php if($configs['general']['language'] == 'fr') echo 'selected'; ?>>French</option>
                                                                <option value="zh" <?php if($configs['general']['language'] == 'zh') echo 'selected'; ?>>Chinese</option>
                                                            </select>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <label class="form-label">Display Mode</label>
                                                            <select class="form-control" name="display_mode">
                                                                <option value="standard" <?php if($configs['general']['display_mode'] == 'standard') echo 'selected'; ?>>Standard</option>
                                                                <option value="kiosk" <?php if($configs['general']['display_mode'] == 'kiosk') echo 'selected'; ?>>Kiosk Mode</option>
                                                                <option value="simple" <?php if($configs['general']['display_mode'] == 'simple') echo 'selected'; ?>>Simple Mode</option>
                                                                <option value="advanced" <?php if($configs['general']['display_mode'] == 'advanced') echo 'selected'; ?>>Advanced Mode</option>
                                                            </select>
                                                        </div>
                                                        
                                                        <div class="form-footer">
                                                            <button type="submit" name="save_config" class="btn btn-primary">Save Settings</button>
                                                        </div>
                                                    </form>
                                                </div>
                                                
                                                <!-- Recognition Settings Tab -->
                                                <div class="tab-pane fade" id="recognition" role="tabpanel">
                                                    <form action="" method="post">
                                                        <input type="hidden" name="config_type" value="recognition">
                                                        
                                                        <div class="form-group">
                                                            <label class="form-label">Face Recognition Threshold</label>
                                                            <input type="range" class="form-control-range" name="face_threshold" min="0.1" max="0.9" step="0.1"
                                                                value="<?php echo $configs['recognition']['face_threshold']; ?>">
                                                            <small class="form-text text-muted">
                                                                Current: <span id="threshold-value"><?php echo $configs['recognition']['face_threshold']; ?></span>
                                                                (Higher values require more accurate matches)
                                                            </small>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" class="custom-control-input" id="liveness_check" name="liveness_check"
                                                                    <?php if($configs['recognition']['liveness_check']) echo 'checked'; ?>>
                                                                <label class="custom-control-label" for="liveness_check">Enable Liveness Check</label>
                                                                <small class="form-text text-muted">
                                                                    Detects if a real person is present (prevents photo spoofing)
                                                                </small>
                                                            </div>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" class="custom-control-input" id="mask_detection" name="mask_detection"
                                                                    <?php if($configs['recognition']['mask_detection']) echo 'checked'; ?>>
                                                                <label class="custom-control-label" for="mask_detection">Enable Mask Detection</label>
                                                                <small class="form-text text-muted">
                                                                    Detects if person is wearing a face mask
                                                                </small>
                                                            </div>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" class="custom-control-input" id="temperature_check" name="temperature_check"
                                                                    <?php if($configs['recognition']['temperature_check']) echo 'checked'; ?>>
                                                                <label class="custom-control-label" for="temperature_check">Enable Temperature Check</label>
                                                                <small class="form-text text-muted">
                                                                    Requires compatible hardware with temperature sensor
                                                                </small>
                                                            </div>
                                                        </div>
                                                        
                                                        <div class="form-group" id="temperature_threshold_group" 
                                                            <?php if(!$configs['recognition']['temperature_check']) echo 'style="display:none;"'; ?>>
                                                            <label class="form-label">Temperature Threshold (°C)</label>
                                                            <input type="number" class="form-control" name="temperature_threshold" step="0.1" min="35.0" max="42.0"
                                                                value="<?php echo $configs['recognition']['temperature_threshold']; ?>">
                                                            <small class="form-text text-muted">
                                                                Maximum allowed temperature for entry
                                                            </small>
                                                        </div>
                                                        
                                                        <div class="form-footer">
                                                            <button type="submit" name="save_config" class="btn btn-primary">Save Settings</button>
                                                        </div>
                                                    </form>
                                                </div>
                                                
                                                <!-- Server Settings Tab -->
                                                <div class="tab-pane fade" id="server" role="tabpanel">
                                                    <form action="" method="post">
                                                        <input type="hidden" name="config_type" value="server">
                                                        
                                                        <div class="form-group">
                                                            <label class="form-label">Server URL</label>
                                                            <input type="text" class="form-control" name="server_url" 
                                                                value="<?php echo $configs['server']['server_url']; ?>">
                                                            <small class="form-text text-muted">
                                                                Main server URL for device communication
                                                            </small>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <label class="form-label">Server Port</label>
                                                            <input type="number" class="form-control" name="server_port" min="1" max="65535"
                                                                value="<?php echo $configs['server']['server_port']; ?>">
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <label class="form-label">Heartbeat Interval (seconds)</label>
                                                            <input type="number" class="form-control" name="heartbeat_interval" min="10" max="3600"
                                                                value="<?php echo $configs['server']['heartbeat_interval']; ?>">
                                                            <small class="form-text text-muted">
                                                                How often the device checks in with the server
                                                            </small>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <label class="form-label">Attendance Callback URL</label>
                                                            <input type="text" class="form-control" name="callback_url" 
                                                                value="<?php echo isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://'; ?><?php echo $_SERVER['HTTP_HOST']; ?>/admin/api/attendance_callback.php">
                                                            <small class="form-text text-muted">
                                                                URL where attendance records will be sent
                                                            </small>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <label class="form-label">Personnel Upload URL</label>
                                                            <input type="text" class="form-control" name="personnel_url" 
                                                                value="<?php echo isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://'; ?><?php echo $_SERVER['HTTP_HOST']; ?>/admin/api/personnel-receive.php">
                                                            <small class="form-text text-muted">
                                                                URL where personnel data will be sent
                                                            </small>
                                                        </div>
                                                        
                                                        <div class="form-footer">
                                                            <button type="submit" name="save_config" class="btn btn-primary">Save Settings</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <?php require_once('includes/footer.php'); ?>
    </div>
    
    <script>
        $(document).ready(function() {
            // Hide loading dimmer
            $('#loading-dimmer').removeClass('active');
            
            // Update volume value display
            $('input[name="volume"]').on('input', function() {
                $('#volume-value').text($(this).val());
            });
            
            // Update threshold value display
            $('input[name="face_threshold"]').on('input', function() {
                $('#threshold-value').text($(this).val());
            });
            
            // Toggle temperature threshold field
            $('#temperature_check').change(function() {
                if($(this).is(':checked')) {
                    $('#temperature_threshold_group').show();
                } else {
                    $('#temperature_threshold_group').hide();
                }
            });
        });
    </script>
</body>
</html>
