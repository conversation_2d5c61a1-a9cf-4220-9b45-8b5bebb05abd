/* Latvian (UTF-8) initialisation for the jQuery UI date picker plugin. */
/* <AUTHOR> <<EMAIL>> */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['lv'] = {
	closeText: 'Aizvērt',
	prevText: 'Iepr.',
	nextText: 'Nāk.',
	currentText: '<PERSON><PERSON><PERSON>',
	monthNames: ['<PERSON>v<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>',
	'<PERSON><PERSON><PERSON><PERSON><PERSON>','Augusts','Septembris','Oktobris','Novembris','Decembris'],
	monthNamesShort: ['Jan','Feb','<PERSON>','Apr','<PERSON>','<PERSON><PERSON><PERSON>',
	'<PERSON><PERSON><PERSON>','Aug','Sep','Okt','Nov','Dec'],
	dayNames: ['svētdiena','pirmdiena','otrdiena','tre<PERSON>diena','ceturtdiena','piektdiena','sestdiena'],
	dayNamesShort: ['svt','prm','otr','tre','ctr','pkt','sst'],
	dayNamesMin: ['Sv','Pr','Ot','Tr','Ct','Pk','Ss'],
	weekHeader: 'Ned.',
	dateFormat: 'dd.mm.yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['lv']);

return datepicker.regional['lv'];

}));
