{"name": "morris.js", "version": "0.5.0", "homepage": "http://morrisjs.github.com/morris.js", "license": "BSD-2-<PERSON><PERSON>", "description": "Easy, pretty charts", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/morrisjs/morris.js.git"}, "bugs": {"url": "https://github.com/morrisjs/morris.js/issues"}, "devDependencies": {"matchdep": "~0.1.2", "grunt": "~0.4.1", "grunt-mocha": "~0.4.10", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-coffee": "~0.7.0", "grunt-contrib-uglify": "~0.2.4", "grunt-contrib-less": "~0.7.0", "grunt-contrib-watch": "~0.5.3", "grunt-shell": "~0.5.0", "bower": "~1.2.8"}, "scripts": {"test": "grunt concat coffee mocha"}, "engines": {"node": ">=0.8 <0.11"}}