# bootstrap-datepicker

[![Join the chat at https://gitter.im/uxsolutions/bootstrap-datepicker](https://badges.gitter.im/Join%20Chat.svg)](https://gitter.im/uxsolutions/bootstrap-datepicker?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge)
[![Build Status](https://travis-ci.org/uxsolutions/bootstrap-datepicker.svg?branch=master)](https://travis-ci.org/uxsolutions/bootstrap-datepicker)
[![GitHub license](https://img.shields.io/badge/license-Apache%202-blue.svg)](https://raw.githubusercontent.com/uxsolutions/bootstrap-datepicker/master/LICENSE)
[![npm](https://img.shields.io/npm/dt/bootstrap-datepicker.svg)](https://github.com/uxsolutions/bootstrap-datepicker)
[![Twitter Follow](https://img.shields.io/twitter/follow/bsdatepicker.svg?style=social&label=Follow)](https://twitter.com/bsdatepicker)

Versions are incremented according to [semver](http://semver.org/).

## CDN

You can use the [CloudFlare](https://www.cloudflare.com) powered [cdnjs.com](https://cdnjs.com) on your website.

[bootstrap-datepicker](https://cdnjs.com/libraries/bootstrap-datepicker) on cdnjs

Please note: It might take a few hours until a new version is available on cdnjs.

## Links

* [Online Demo](https://uxsolutions.github.io/bootstrap-datepicker/)
* [Online Docs](https://bootstrap-datepicker.readthedocs.org/en/stable/) (ReadTheDocs.com)
* [Google Group](https://groups.google.com/group/bootstrap-datepicker/)
* [Travis CI](https://travis-ci.org/uxsolutions/bootstrap-datepicker)

## Development

Once you cloned the repo, you'll need to install [grunt](https://gruntjs.com/) and the development dependencies using a package manager:

* [yarn](https://yarnpkg.com/) (recommended):

```
$ [sudo] yarn global add grunt-cli
$ yarn install
```

* [npm](https://www.npmjs.com/):

```
$ [sudo] npm install --global grunt-cli
$ npm install
```
