{"disallowEmptyBlocks": true, "disallowKeywords": ["with"], "disallowMixedSpacesAndTabs": true, "disallowMultipleLineStrings": true, "disallowMultipleVarDecl": true, "disallowQuotedKeysInObjects": "allButReserved", "disallowSpaceAfterPrefixUnaryOperators": ["++", "--", "+", "-", "~", "!"], "disallowSpaceBeforeBinaryOperators": [","], "disallowSpaceBeforePostfixUnaryOperators": ["++", "--"], "disallowSpacesInFunctionDeclaration": {"beforeOpeningRoundBrace": true}, "disallowSpacesInNamedFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInsideArrayBrackets": true, "disallowSpacesInsideParentheses": true, "disallowTrailingComma": true, "disallowTrailingWhitespace": true, "requireCamelCaseOrUpperCaseIdentifiers": true, "requireCapitalizedConstructors": true, "requireCommaBeforeLineBreak": true, "requireDollarBeforejQueryAssignment": true, "requireDotNotation": true, "requireLineFeedAtFileEnd": true, "requirePaddingNewLinesAfterUseStrict": true, "requirePaddingNewLinesBeforeExport": true, "requireSpaceAfterBinaryOperators": ["+", "-", "/", "*", "=", "==", "===", "!=", "!==", ">", "<", ">=", "<="], "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch"], "requireSpaceAfterLineComment": true, "requireSpaceBeforeBinaryOperators": ["+", "-", "/", "*", "=", "==", "===", "!=", "!==", ">", "<", ">=", "<="], "requireSpaceBetweenArguments": true, "requireSpacesInAnonymousFunctionExpression": {"beforeOpeningCurlyBrace": true, "beforeOpeningRoundBrace": true}, "requireSpacesInConditionalExpression": true, "requireSpacesInForStatement": true, "requireSpacesInFunctionDeclaration": {"beforeOpeningCurlyBrace": true}, "requireSpacesInFunctionExpression": {"beforeOpeningCurlyBrace": true}, "requireSpacesInNamedFunctionExpression": {"beforeOpeningCurlyBrace": true}, "requireSpacesInsideObjectBrackets": "allButNested", "validateAlignedFunctionParameters": true, "validateIndentation": 2, "validateLineBreaks": "LF", "validateNewlineAfterArrayElements": true, "validateQuoteMarks": "'"}