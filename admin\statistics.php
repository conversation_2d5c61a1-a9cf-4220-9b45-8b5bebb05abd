<?php
require_once('includes/script.php');
require_once('session/Login.php');

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

// Get the session variables
$password = $_SESSION['official_password'];
$username = $_SESSION['official_username'];
$uid = $_SESSION['official_id'];

// Get admin information
$connection = $model->TemporaryConnection();
$query = $model->GetAdministrator($username, $password);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];
$type = $admin['type'];

// Get statistics data
$today = date('Y-m-d');

// Include API cache for performance
require_once('includes/api_cache.php');

// Get total employees from ERP API with caching
$total_employees = getApiEmployeeCountCached();

// Attendance rate
$sql = "SELECT COUNT(*) AS total_present FROM attendance WHERE status_morning = 1";
$query = mysqli_query($connection, $sql);
$present = mysqli_fetch_assoc($query)['total_present'];

$sql = "SELECT COUNT(*) AS total FROM attendance";
$query = mysqli_query($connection, $sql);
$total = mysqli_fetch_assoc($query)['total'];

$attendance_rate = ($total > 0) ? round(($present / $total) * 100) : 0;

// On-time percentage
$sql = "SELECT COUNT(*) AS on_time FROM attendance WHERE time_in_morning <= '08:00:00' AND status_morning = 1";
$query = mysqli_query($connection, $sql);
$onTime = mysqli_fetch_assoc($query)['on_time'];

$onTimePercentage = ($present > 0) ? round(($onTime / $present) * 100) : 0;

// Absent today
$sql = "SELECT COUNT(*) AS absent_today FROM attendance WHERE date = '$today' AND status_morning = 0";
$query = mysqli_query($connection, $sql);
$absent_today = mysqli_fetch_assoc($query)['absent_today'];
?>
<!doctype html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta http-equiv="Content-Language" content="en" />
  <meta name="msapplication-TileColor" content="#2d89ef">
  <meta name="theme-color" content="#4188c9">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/>
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="HandheldFriendly" content="True">
  <meta name="MobileOptimized" content="320">
  <link rel="icon" href="./favicon.ico" type="image/x-icon"/>
  <link rel="shortcut icon" type="image/x-icon" href="./favicon.ico" />
  <title>Statistics Dashboard - Profiling and Payroll Management System</title>
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,300i,400,400i,500,500i,600,600i,700,700i&amp;subset=latin-ext">
  <script src="./assets/js/require.min.js"></script>
  <script>
    requirejs.config({
      baseUrl: '.'
    });
  </script>
  <!-- Dashboard Core -->
  <link href="./assets/css/dashboard.css" rel="stylesheet" />
  <script src="./assets/js/dashboard.js"></script>
  <!-- c3.js Charts Plugin -->
  <link href="./assets/plugins/charts-c3/plugin.css" rel="stylesheet" />
  <script src="./assets/plugins/charts-c3/plugin.js"></script>
  <!-- Google Maps Plugin -->
  <link href="./assets/plugins/maps-google/plugin.css" rel="stylesheet" />
  <script src="./assets/plugins/maps-google/plugin.js"></script>
  <!-- Input Mask Plugin -->
  <script src="./assets/plugins/input-mask/plugin.js"></script>
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@2.9.4/dist/Chart.min.js"></script>
</head>
<body class="">
  <div class="page">
    <div class="page-main">
        <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
          <div class="container">
            <div class="row align-items-center">
              <div class="d-flex">
                <?php require_once('includes/header.php') ?>
              </div>
              <div class="col-lg order-lg-first">
                <?php require_once('includes/subheader.php') ?>
              </div>
            </div>
          </div>
        </div>
      <div class="my-3 my-md-5">
        <div class="container">
          <div class="page-header mb-4">
            <div class="row align-items-center">
              <div class="col">
                <h1 class="page-title text-gradient">Analytics Dashboard</h1>
                <p class="page-subtitle">Comprehensive insights into employee attendance and performance metrics</p>
              </div>
              <div class="col-auto">
                <div class="btn-group">
                  <button class="btn btn-primary" onclick="window.print()">
                    <i class="fe fe-download mr-2"></i> Generate Report
                  </button>
                  <button class="btn btn-outline-primary" onclick="location.reload()">
                    <i class="fe fe-refresh-cw mr-2"></i> Refresh Data
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Statistics Cards -->
          <div class="row mb-4">
            <div class="col-sm-6 col-lg-3">
              <div class="stat-card fade-in-up">
                <div class="d-flex align-items-center">
                  <div class="stat-icon bg-gradient-primary text-white rounded-circle p-3 mr-3">
                    <i class="fe fe-users" style="font-size: 1.5rem;"></i>
                  </div>
                  <div>
                    <div class="stat-number"><?php echo number_format($total_employees); ?></div>
                    <div class="stat-label">Total Employees</div>
                    <small class="text-muted">From ERP System</small>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-sm-6 col-lg-3">
              <div class="stat-card fade-in-up" style="animation-delay: 0.1s;">
                <div class="d-flex align-items-center">
                  <div class="stat-icon bg-success text-white rounded-circle p-3 mr-3">
                    <i class="fe fe-trending-up" style="font-size: 1.5rem;"></i>
                  </div>
                  <div>
                    <div class="stat-number"><?php echo $attendance_rate; ?>%</div>
                    <div class="stat-label">Attendance Rate</div>
                    <small class="text-muted">Overall Performance</small>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-sm-6 col-lg-3">
              <div class="stat-card fade-in-up" style="animation-delay: 0.2s;">
                <div class="d-flex align-items-center">
                  <div class="stat-icon bg-warning text-white rounded-circle p-3 mr-3">
                    <i class="fe fe-clock" style="font-size: 1.5rem;"></i>
                  </div>
                  <div>
                    <div class="stat-number"><?php echo $onTimePercentage; ?>%</div>
                    <div class="stat-label">On-Time Rate</div>
                    <small class="text-muted">Punctuality Score</small>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-sm-6 col-lg-3">
              <div class="stat-card fade-in-up" style="animation-delay: 0.3s;">
                <div class="d-flex align-items-center">
                  <div class="stat-icon bg-danger text-white rounded-circle p-3 mr-3">
                    <i class="fe fe-user-x" style="font-size: 1.5rem;"></i>
                  </div>
                  <div>
                    <div class="stat-number"><?php echo number_format($absent_today); ?></div>
                    <div class="stat-label">Absent Today</div>
                    <small class="text-muted"><?php echo date('M d, Y'); ?></small>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Charts Row -->
          <div class="row row-cards row-deck">
            <div class="col-lg-6">
              <div class="card">
                <div class="card-header">
                  <h3 class="card-title">Monthly Attendance Overview</h3>
                </div>
                <div class="card-body">
                  <div style="height: 300px">
                    <canvas id="monthlyAttendanceChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-6">
              <div class="card">
                <div class="card-header">
                  <h3 class="card-title">Attendance Status</h3>
                </div>
                <div class="card-body">
                  <div style="height: 300px">
                    <canvas id="attendanceStatusChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Recent Attendance Table -->
          <div class="row row-cards row-deck">
            <div class="col-12">
              <div class="card">
                <div class="card-header">
                  <h3 class="card-title">Recent Attendance</h3>
                </div>
                <div class="table-responsive">
                  <table class="table card-table table-vcenter text-nowrap">
                    <thead>
                      <tr>
                        <th>Employee</th>
                        <th>Date</th>
                        <th>Time In</th>
                        <th>Time Out</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <?php
                      $sql = "SELECT a.*, e.fullname, p.description as position 
                              FROM attendance a
                              JOIN employees e ON a.employee_id = e.id
                              JOIN position p ON e.position_id = p.id
                              ORDER BY a.date DESC, a.time_in_morning DESC
                              LIMIT 10";
                      $query = mysqli_query($connection, $sql);
                      while ($row = mysqli_fetch_assoc($query)) {
                          $status = $row['status_morning'] == 1 ? 
                              ($row['time_in_morning'] <= '08:00:00' ? 'On Time' : 'Late') : 'Absent';
                          $statusClass = $status == 'On Time' ? 'text-success' : 
                              ($status == 'Late' ? 'text-warning' : 'text-danger');
                          
                          echo "<tr>";
                          echo "<td>" . $row['fullname'] . "</td>";
                          echo "<td>" . date('M d, Y', strtotime($row['date'])) . "</td>";
                          echo "<td>" . ($row['time_in_morning'] ? date('h:i A', strtotime($row['time_in_morning'])) : 'N/A') . "</td>";
                          echo "<td>" . ($row['time_out_morning'] ? date('h:i A', strtotime($row['time_out_morning'])) : 'N/A') . "</td>";
                          echo "<td class='" . $statusClass . "'>" . $status . "</td>";
                          echo "</tr>";
                      }
                      ?>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <?php require_once('includes/footer.php') ?>
  </div>
  <?php require_once('includes/datatables.php') ?>
  <?php require_once('includes/bower.php') ?>
  <script>
  document.addEventListener('DOMContentLoaded', function() {
    // Monthly Attendance Chart
    var monthlyCtx = document.getElementById('monthlyAttendanceChart').getContext('2d');
    var monthlyChart = new Chart(monthlyCtx, {
      type: 'line',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [{
          label: 'Present',
          data: [65, 59, 80, 81, 56, 55, 40, 60, 70, 75, 80, 90],
          backgroundColor: 'rgba(70, 127, 208, 0.1)',
          borderColor: 'rgba(70, 127, 208, 1)',
          borderWidth: 2,
          pointBackgroundColor: '#ffffff',
          pointBorderColor: 'rgba(70, 127, 208, 1)',
          pointBorderWidth: 2,
          pointRadius: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        legend: {
          display: false
        },
        scales: {
          yAxes: [{
            ticks: {
              beginAtZero: true
            }
          }]
        }
      }
    });
    
    // Attendance Status Chart
    var statusCtx = document.getElementById('attendanceStatusChart').getContext('2d');
    var statusChart = new Chart(statusCtx, {
      type: 'doughnut',
      data: {
        labels: ['Present', 'On Time', 'Late'],
        datasets: [{
          data: [<?php echo $present; ?>, <?php echo $onTime; ?>, <?php echo ($present - $onTime); ?>],
          backgroundColor: ['#467fd0', '#5eba00', '#f1c40f'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        cutoutPercentage: 60,
        legend: {
          position: 'bottom'
        }
      }
    });
  });
  </script>
</body>
</html>


