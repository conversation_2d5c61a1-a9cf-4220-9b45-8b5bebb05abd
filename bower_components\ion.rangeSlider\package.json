{"name": "ion-rangeslider", "version": "2.2.0", "description": "Cool, comfortable and easily customizable range slider with many options and skin support", "homepage": "http://ionden.com/a/plugins/ion.rangeSlider/en.html", "author": {"name": "<PERSON> (IonDen)", "email": "<EMAIL>", "url": "https://github.com/IonDen"}, "keywords": ["jquery-plugin", "ecosystem:jquery", "j<PERSON>y", "form", "input", "range", "slider", "rangeslider", "interface", "diapason", "ui", "noui", "skins"], "main": "./js/ion.rangeSlider.js", "directories": {"lib": "js"}, "repository": {"type": "git", "url": "git://github.com/IonDen/ion.rangeSlider.git"}, "bugs": {"url": "https://github.com/IonDen/ion.rangeSlider/issues"}, "license": "MIT", "dependencies": {"jquery": ">=1.8"}, "ignore": [".idea", "PSD", "bower.json", "ion-rangeSlider.jquery.json", "readme.ru.md"], "spm": {"main": "js/ion.rangeSlider.js", "dependencies": {"jquery": "1.11.2"}, "ignore": [".idea", "PSD", "bower.json", "ion-rangeSlider.jquery.json", "readme.ru.md"]}}