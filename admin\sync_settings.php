<?php
require_once('includes/script.php');
require_once('session/ModelController.php');

$model = new Dashboard();
$connection = $model->TemporaryConnection();

// Check if user is logged in
if(!isset($_SESSION['id'])){
    header('location: index.php');
    exit;
}

$stat = '';

// Handle settings update
if(isset($_POST['update_settings'])) {
    $autoSyncEnabled = isset($_POST['auto_sync_enabled']) ? 1 : 0;
    $syncInterval = (int)$_POST['sync_interval'];
    
    // Validate sync interval (minimum 1 minute, maximum 60 minutes)
    if($syncInterval < 1) $syncInterval = 1;
    if($syncInterval > 60) $syncInterval = 60;
    
    // Save settings to database or file
    $settings = array(
        'auto_sync_enabled' => $autoSyncEnabled,
        'sync_interval' => $syncInterval,
        'last_updated' => date('Y-m-d H:i:s')
    );
    
    file_put_contents('sync/settings.json', json_encode($settings));
    
    $stat = '<div class="alert alert-success alert-dismissible">
            <button type="button" class="close" data-dismiss="alert"></button>
            Sync settings updated successfully!
            </div>';
}

// Load current settings
$defaultSettings = array(
    'auto_sync_enabled' => 1,
    'sync_interval' => 5,
    'last_updated' => null
);

$settingsFile = 'sync/settings.json';
if(file_exists($settingsFile)) {
    $currentSettings = json_decode(file_get_contents($settingsFile), true);
    if(!$currentSettings) $currentSettings = $defaultSettings;
} else {
    $currentSettings = $defaultSettings;
}

// Get sync log
$logFile = 'sync/sync_log.txt';
$syncLog = '';
if(file_exists($logFile)) {
    $logLines = file($logFile);
    $syncLog = implode('', array_slice($logLines, -20)); // Last 20 lines
}

// Get last sync time
$lastSyncFile = 'sync/last_sync.txt';
$lastSyncTime = null;
if(file_exists($lastSyncFile)) {
    $lastSyncTime = file_get_contents($lastSyncFile);
}
?>

<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="Content-Language" content="en" />
    <meta name="msapplication-TileColor" content="#2d89ef">
    <meta name="theme-color" content="#4188c9">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="HandheldFriendly" content="True">
    <meta name="MobileOptimized" content="320">
    <link rel="icon" href="./favicon.ico" type="image/x-icon"/>
    <link rel="shortcut icon" type="image/x-icon" href="./favicon.ico" />
    <!-- Generated: 2018-04-16 09:29:05 +0200 -->
    <title>ERP Sync Settings</title>
    <?php require_once('includes/script.php') ?>
  </head>
  <body class="">
    <div class="page">
      <div class="page-main">
        <?php require_once('includes/header.php') ?>
        <div class="my-3 my-md-5">
          <div class="container">
            <div class="page-header mb-4">
              <div class="row align-items-center">
                <div class="col">
                  <h1 class="page-title text-gradient">ERP Sync Settings</h1>
                  <p class="page-subtitle">Configure automatic synchronization with the ERP system</p>
                </div>
                <div class="col-auto">
                  <button class="btn btn-primary" onclick="window.autoSyncManager?.manualSync()">
                    <i class="fe fe-refresh-cw mr-2"></i>Manual Sync
                  </button>
                </div>
              </div>
            </div>

            <?php echo $stat; ?>

            <div class="row">
              <!-- Settings Card -->
              <div class="col-md-6">
                <div class="card shadow-custom">
                  <div class="card-header">
                    <h3 class="card-title">
                      <i class="fe fe-settings mr-2"></i>Sync Configuration
                    </h3>
                  </div>
                  <form method="POST">
                    <div class="card-body">
                      <div class="form-group">
                        <label class="custom-switch">
                          <input type="checkbox" name="auto_sync_enabled" class="custom-switch-input" 
                                 <?php echo $currentSettings['auto_sync_enabled'] ? 'checked' : ''; ?>>
                          <span class="custom-switch-indicator"></span>
                          <span class="custom-switch-description">Enable Automatic Sync</span>
                        </label>
                        <small class="form-text text-muted">
                          When enabled, employee data will be automatically synchronized from the ERP system
                        </small>
                      </div>

                      <div class="form-group">
                        <label class="form-label">Sync Interval (minutes)</label>
                        <select name="sync_interval" class="form-control">
                          <option value="1" <?php echo $currentSettings['sync_interval'] == 1 ? 'selected' : ''; ?>>Every 1 minute</option>
                          <option value="5" <?php echo $currentSettings['sync_interval'] == 5 ? 'selected' : ''; ?>>Every 5 minutes</option>
                          <option value="10" <?php echo $currentSettings['sync_interval'] == 10 ? 'selected' : ''; ?>>Every 10 minutes</option>
                          <option value="15" <?php echo $currentSettings['sync_interval'] == 15 ? 'selected' : ''; ?>>Every 15 minutes</option>
                          <option value="30" <?php echo $currentSettings['sync_interval'] == 30 ? 'selected' : ''; ?>>Every 30 minutes</option>
                          <option value="60" <?php echo $currentSettings['sync_interval'] == 60 ? 'selected' : ''; ?>>Every 1 hour</option>
                        </select>
                        <small class="form-text text-muted">
                          How often the system should check for updates from the ERP
                        </small>
                      </div>
                    </div>
                    <div class="card-footer">
                      <button type="submit" name="update_settings" class="btn btn-primary">
                        <i class="fe fe-save mr-2"></i>Save Settings
                      </button>
                    </div>
                  </form>
                </div>
              </div>

              <!-- Status Card -->
              <div class="col-md-6">
                <div class="card shadow-custom">
                  <div class="card-header">
                    <h3 class="card-title">
                      <i class="fe fe-activity mr-2"></i>Sync Status
                    </h3>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-6">
                        <div class="text-center">
                          <div class="h1 m-0 text-primary">
                            <?php echo $currentSettings['auto_sync_enabled'] ? 'ON' : 'OFF'; ?>
                          </div>
                          <div class="text-muted">Auto Sync</div>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="text-center">
                          <div class="h1 m-0 text-info">
                            <?php echo $currentSettings['sync_interval']; ?>m
                          </div>
                          <div class="text-muted">Interval</div>
                        </div>
                      </div>
                    </div>

                    <hr>

                    <div class="form-group">
                      <label class="form-label">Last Sync</label>
                      <div class="text-muted">
                        <?php if($lastSyncTime): ?>
                          <?php echo date('M d, Y H:i:s', strtotime($lastSyncTime)); ?>
                        <?php else: ?>
                          Never
                        <?php endif; ?>
                      </div>
                    </div>

                    <div class="form-group">
                      <label class="form-label">Settings Last Updated</label>
                      <div class="text-muted">
                        <?php if($currentSettings['last_updated']): ?>
                          <?php echo date('M d, Y H:i:s', strtotime($currentSettings['last_updated'])); ?>
                        <?php else: ?>
                          Default settings
                        <?php endif; ?>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Sync Log -->
            <div class="row mt-4">
              <div class="col-12">
                <div class="card shadow-custom">
                  <div class="card-header">
                    <h3 class="card-title">
                      <i class="fe fe-file-text mr-2"></i>Recent Sync Activity
                    </h3>
                  </div>
                  <div class="card-body">
                    <?php if($syncLog): ?>
                      <pre class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto; font-size: 12px;"><?php echo htmlspecialchars($syncLog); ?></pre>
                    <?php else: ?>
                      <div class="text-center text-muted py-4">
                        <i class="fe fe-inbox" style="font-size: 3rem;"></i>
                        <p class="mt-2">No sync activity yet</p>
                      </div>
                    <?php endif; ?>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
    // Update auto-sync settings when form is submitted
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const autoSyncEnabled = document.querySelector('input[name="auto_sync_enabled"]').checked;
            const syncInterval = document.querySelector('select[name="sync_interval"]').value;
            
            // Update localStorage settings for JavaScript
            localStorage.setItem('autoSyncEnabled', autoSyncEnabled);
            localStorage.setItem('syncInterval', syncInterval * 60 * 1000); // Convert to milliseconds
            
            // Update auto-sync manager if available
            if (window.autoSyncManager) {
                if (autoSyncEnabled) {
                    window.autoSyncManager.enableAutoSync();
                } else {
                    window.autoSyncManager.disableAutoSync();
                }
            }
        });
    });
    </script>

    <?php require_once('includes/datatables.php') ?>
  </body>
</html>
