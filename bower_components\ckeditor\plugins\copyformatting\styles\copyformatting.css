/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/

html.cke_copyformatting_active {
	min-height: 100%;
}

/* There is no cursor in CUR format for IE/Edge as that browser
 does not support custom cursor in [contenteditable] area.
 Ticket for this issue:
 https://connect.microsoft.com/IE/feedback/details/1070215/cant-change-cursor-in-contenteditable-using-css */
.cke_copyformatting_disabled,
.cke_copyformatting_disabled a,
.cke_copyformatting_disabled .cke_editable {
	cursor: url(../cursors/cursor-disabled.svg) 12 1, auto;
}

.cke_copyformatting_disabled .cke_top a,
.cke_copyformatting_disabled .cke_bottom a {
	cursor: default;
}

/* Added `!important` rule as a fix for overriding the cursor by the Table Resize plugin.
 The `!important` rule is used because the Table Resize plugin creates a `<div>` which changes the cursor using inlined styles. */
.cke_copyformatting_active,
.cke_copyformatting_active.cke_editable,
.cke_copyformatting_active .cke_editable,
.cke_copyformatting_active a,
.cke_copyformatting_active table,
.cke_copyformatting_active div[data-cke-temp],
.cke_copyformatting_tableresize_cursor div[data-cke-temp] {
	cursor: url(../cursors/cursor.svg) 12 1, auto !important;
}

.cke_screen_reader_only {
	position: absolute;
	clip: rect(1px, 1px, 1px, 1px);
	padding: 0;
	border: 0;
	height: 1px;
	width: 1px;
	overflow: hidden;
}
