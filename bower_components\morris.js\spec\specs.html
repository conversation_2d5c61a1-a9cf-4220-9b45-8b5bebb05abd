<!doctype html>
<head>
  <meta charset="utf-8">
  <title>morris.js tests</title>
  <link rel="stylesheet" href="../bower_components/mocha/mocha.css" type="text/css" media="screen" />
  <link rel="stylesheet" href="../morris.css" type="text/css" media="screen" />
  <!-- jQuery packaging changed for 2.1.0, so try to load both paths, one will work. -->
  <script src="../bower_components/jquery/dist/jquery.js"></script>
  <script src="../bower_components/jquery/jquery.js"></script>
  <script type="text/javascript" src="../bower_components/raphael/raphael-min.js"></script>
</head>
<body>
  <div id="mocha"></div>

  <script type="text/javascript" src="../bower_components/mocha/mocha.js"></script>
  <script type="text/javascript" src="../bower_components/chai/chai.js"></script>
  <script type="text/javascript" src="../bower_components/chai-jquery/chai-jquery.js"></script>
  <script type="text/javascript" src="../bower_components/sinon/index.js"></script>
  <script type="text/javascript" src="../bower_components/sinon-chai/lib/sinon-chai.js"></script>
  <script>
    mocha.setup('bdd');
    should = chai.should();
  </script>

  <script type="text/javascript" src="../morris.js"></script>
  <script type="text/javascript" src="../build/spec.js"></script>
  <div id="test" style="width: 400px; height: 200px;"></div>
  <script>
    if (navigator.userAgent.indexOf('PhantomJS') < 0) {
      mocha.run();
    }
  </script>
</body>
</html>
