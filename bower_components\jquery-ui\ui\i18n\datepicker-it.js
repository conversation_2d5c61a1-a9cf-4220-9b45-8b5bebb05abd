/* Italian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['it'] = {
	closeText: '<PERSON>udi',
	prevText: '&#x3C;Prec',
	nextText: 'Succ&#x3E;',
	currentText: 'Oggi',
	monthNames: ['<PERSON>nai<PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','Aprile','Ma<PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON>o','<PERSON><PERSON><PERSON>','<PERSON>tem<PERSON>','<PERSON><PERSON>','Novembre','Dicembre'],
	monthNamesShort: ['Gen','Feb','Mar','Apr','Mag','<PERSON><PERSON>',
		'Lug','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','Nov','Dic'],
	dayNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON>bato'],
	dayNamesShort: ['Dom','Lun','Mar','Mer','Gio','Ven','Sab'],
	dayNamesMin: ['Do','Lu','Ma','Me','Gi','Ve','Sa'],
	weekHeader: 'Sm',
	dateFormat: 'dd/mm/yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['it']);

return datepicker.regional['it'];

}));
