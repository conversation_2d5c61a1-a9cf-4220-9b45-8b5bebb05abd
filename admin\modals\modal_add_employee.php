<?php

if (!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])) {
  header("location:index.php?utm_campaign=expired");
}

if (isset($_POST['add_new'])) {
  $numbers = '';
  for ($i = 0; $i < 10; $i++) {
    $numbers .= $i;
  }

  $employee_id = substr(str_shuffle($numbers), 0, 9);
  $position_id = $_POST['desired_position'];
  $schedule_id = $_POST['desired_schedule'];
  $department_id = $_POST['department_id'];

  $photo = $_FILES['img_name']['name'];
  $target = "../image/" . basename($photo);
  move_uploaded_file($_FILES['img_name']['tmp_name'], $target);

  $first = ucwords($_POST['first']);
  $middle = ucwords($_POST['middle']);
  $last = ucwords($_POST['last']);

  $fullname = $first . ' ' . $middle . ' ' . $last;
  $address = ucwords($_POST['address']);
  $email = $_POST['email'];
  $phonenumber = $_POST['phonenumber'];
  $sex = $_POST['sex'];
  $position = $_POST['desired_position'];
  $emergency_name = ucwords($_POST['emergency_name']);
  $emergency_contact = $_POST['emergency_contact'];
  $project_name = ucwords($_POST['project_name']);
  $site_location = ucwords($_POST['site_location']);
  $date = date("Y-m-d");

  $insert = "INSERT INTO `employees` (
    `employee_id`, `position_id`, `schedule_id`, `department_id`, 
    `created_on`, `photo`, `fullname`, `address`, `email`, 
    `phonenumber`, `sex`, `position`, `emergency_name`, 
    `emergency_contact`, `project_name`, `site_location`
  ) VALUES (
    '$employee_id', '$position_id', '$schedule_id', '$department_id',
    '$date', '$photo', '$fullname', '$address', '$email',
    '$phonenumber', '$sex', '$position', '$emergency_name',
    '$emergency_contact', '$project_name', '$site_location'
  )";

  $query = mysqli_query($connection, $insert) or die("Could not insert: $insert");

  echo "<script>window.location.href='profile.php'</script>";
}

?>
<script type="text/javascript">
  function limitKeypress(event, value, maxLength) {
    if (value != undefined && value.toString().length >= maxLength) {
      event.preventDefault();
    }
  }
</script>

<div id="modal-add-employee" class="modal" data-backdrop="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">New Employee Personal Data</h5>
      </div>
      <div class="modal-body p-lg">
        <div class="col-md-12">
          <form class="" method="post" enctype="multipart/form-data">
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label class="form-label">First Name</label>
                  <input name="first" type="text" class="form-control" required placeholder="Enter first name...">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label class="form-label">Middle Name</label>
                  <input name="middle" type="text" class="form-control" required placeholder="Enter middle name...">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label class="form-label">Last Name</label>
                  <input name="last" type="text" class="form-control" required placeholder="Enter last name...">
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label class="form-label">Address</label>
                  <input name="address" type="text" class="form-control" required placeholder="Enter address...">
                </div>
              </div>
              <div class="col-sm-6 col-md-6">
                <div class="form-group">
                  <label class="form-label">Email Address</label>
                  <input name="email" type="email" class="form-control" required placeholder="Enter email address...">
                </div>
              </div>
              <div class="col-sm-6 col-md-6">
                <div class="form-group">
                  <label class="form-label">Phone Number</label>
                  <input name="phonenumber" type="number" maxlength="11" min="0" onkeypress="limitKeypress(event,this.value,11)" class="form-control" required placeholder="Enter phone number...">
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label class="form-label">Department</label>
                  <select required="" name="department_id" class="form-control custom-select">
                    <option class="text-muted">Select Department</option>
                    <?php
                    $dept = "SELECT * FROM `departments`;";
                    $res = mysqli_query($connection, $dept);
                    while ($row = mysqli_fetch_assoc($res)) {
                      ?>
                      <option value="<?php echo $row['id'] ?>"><?php echo $row['name'] ?></option>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label class="form-label">Desired Position</label>
                  <select required="" name="desired_position" class="form-control custom-select">
                    <option class="text-muted">Select Position</option>
                    <?php
                    $pos = "SELECT * FROM `position`;";
                    $res = mysqli_query($connection, $pos);
                    while ($row = mysqli_fetch_assoc($res)) {
                      ?>
                      <option value="<?php echo $row['id'] ?>"><?php echo $row['description'] ?></option>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label class="form-label">Desired Schedule</label>
                  <select required="" name="desired_schedule" class="form-control custom-select">
                    <option class="text-muted">Select Schedule</option>
                    <?php
                    $pos = "SELECT * FROM `schedules`;";
                    $res = mysqli_query($connection, $pos);
                    while ($row = mysqli_fetch_assoc($res)) {
                      ?>
                      <option value="<?php echo $row['id'] ?>"><?php echo $row['time_in_morning'] ?>-<?php echo $row['time_out_morning'] ?>/<?php echo $row['time_in_afternoon'] ?>-<?php echo $row['time_out_afternoon'] ?></option>
                    <?php } ?>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <div class="form-label">Upload Image</div>
                  <div class="custom-file">
                    <input type="file" required class="custom-file-input" name="img_name">
                    <label class="custom-file-label">Choose image file</label>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label class="form-label">Sex</label>
                  <select required="" name="sex" class="form-control custom-select">
                    <option class="text-muted" value="">Select Sex</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label class="form-label">Project Name</label>
                  <input name="project_name" type="text" class="form-control" required placeholder="Enter project name...">
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label class="form-label">Site Location</label>
                  <input name="site_location" type="text" class="form-control" required placeholder="Enter site location...">
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label class="form-label">Person to be contacted in case of emergency</label>
                  <input name="emergency_name" type="text" class="form-control" required placeholder="Enter name details...">
                </div>
              </div>
              <div class="col-md-12">
                <div class="form-group">
                  <label class="form-label">His/her contact details</label>
                  <input name="emergency_contact" type="number" maxlength="11" min="0" onkeypress="limitKeypress(event,this.value,11)" class="form-control" required placeholder="Enter contact details...">
                </div>
              </div>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <div style="padding-right: 12px;">
          <button type="button" class="btn dark-white p-x-md" data-dismiss="modal">No</button>
          <button type="submit" name="add_new" class="btn danger p-x-md">Save</button>
        </div>
      </div>
      </form>
    </div><!-- /.modal-content -->
  </div>
</div>