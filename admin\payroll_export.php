<?php
require_once('includes/script.php');
require_once('session/Login.php');

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

// Check if user is logged in
if(!isset($_SESSION['official_username'])){
    header('location: index.php');
    exit;
}

$connection = $model->TemporaryConnection();

// Get parameters
$export_format = $_GET['format'] ?? 'html';
$month = $_GET['month'] ?? date('n');
$year = $_GET['year'] ?? date('Y');

// Get payroll data
$payroll_query = "SELECT 
    pr.*,
    bu.fullname,
    e.employee_id as emp_id,
    d.name as department_name,
    p.description as position_name
FROM payroll_records pr
LEFT JOIN biometric_users bu ON pr.employee_id = bu.employee_id
LEFT JOIN employees e ON pr.employee_id = e.employee_id
LEFT JOIN departments d ON e.department_id = d.id
LEFT JOIN position p ON e.position_id = p.id
WHERE pr.month = ? AND pr.year = ?
ORDER BY bu.fullname";

$stmt = mysqli_prepare($connection, $payroll_query);
mysqli_stmt_bind_param($stmt, 'ii', $month, $year);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

$payroll_data = [];
$total_gross_pay = 0;
$total_net_pay = 0;
$total_hours = 0;

while ($row = mysqli_fetch_assoc($result)) {
    $payroll_data[] = $row;
    $total_gross_pay += $row['gross_pay'] ?: $row['basic_pay'];
    $total_net_pay += $row['net_pay'] ?: $row['basic_pay'];
    $total_hours += $row['total_hours'];
}

$month_name = date('F', mktime(0, 0, 0, $month, 1));

// Export as CSV
if ($export_format === 'csv') {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="payroll_' . $month . '_' . $year . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // CSV Headers
    fputcsv($output, [
        'Employee ID',
        'Full Name',
        'Department',
        'Position',
        'Days Worked',
        'Total Hours',
        'Basic Pay',
        'Overtime Pay',
        'Gross Pay',
        'Deductions',
        'Net Pay',
        'Generated Date'
    ]);
    
    // CSV Data
    foreach ($payroll_data as $row) {
        fputcsv($output, [
            $row['employee_id'],
            $row['fullname'],
            $row['department_name'] ?? 'N/A',
            $row['position_name'] ?? 'N/A',
            $row['total_days_worked'],
            number_format($row['total_hours'], 2),
            number_format($row['basic_pay'], 2),
            number_format($row['overtime_pay'], 2),
            number_format($row['gross_pay'] ?: $row['basic_pay'], 2),
            number_format($row['deductions'], 2),
            number_format($row['net_pay'] ?: $row['basic_pay'], 2),
            date('Y-m-d', strtotime($row['generated_at']))
        ]);
    }
    
    fclose($output);
    exit;
}

// Export as Excel (HTML table that Excel can open)
if ($export_format === 'excel') {
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="payroll_' . $month . '_' . $year . '.xls"');
    
    echo '<html><head><meta charset="UTF-8"></head><body>';
    echo '<table border="1">';
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    echo '<td>Employee ID</td><td>Full Name</td><td>Department</td><td>Position</td>';
    echo '<td>Days Worked</td><td>Total Hours</td><td>Basic Pay</td><td>Overtime Pay</td>';
    echo '<td>Gross Pay</td><td>Deductions</td><td>Net Pay</td><td>Generated Date</td>';
    echo '</tr>';
    
    foreach ($payroll_data as $row) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($row['employee_id']) . '</td>';
        echo '<td>' . htmlspecialchars($row['fullname']) . '</td>';
        echo '<td>' . htmlspecialchars($row['department_name'] ?? 'N/A') . '</td>';
        echo '<td>' . htmlspecialchars($row['position_name'] ?? 'N/A') . '</td>';
        echo '<td>' . $row['total_days_worked'] . '</td>';
        echo '<td>' . number_format($row['total_hours'], 2) . '</td>';
        echo '<td>' . number_format($row['basic_pay'], 2) . '</td>';
        echo '<td>' . number_format($row['overtime_pay'], 2) . '</td>';
        echo '<td>' . number_format($row['gross_pay'] ?: $row['basic_pay'], 2) . '</td>';
        echo '<td>' . number_format($row['deductions'], 2) . '</td>';
        echo '<td>' . number_format($row['net_pay'] ?: $row['basic_pay'], 2) . '</td>';
        echo '<td>' . date('Y-m-d', strtotime($row['generated_at'])) . '</td>';
        echo '</tr>';
    }
    
    echo '</table></body></html>';
    exit;
}

// Default HTML view
?>
<!doctype html>
<html lang="en" dir="ltr">
<head>
    <title>Payroll Export - <?php echo $month_name . ' ' . $year; ?></title>
    <?php require_once('includes/script.php') ?>
    <style>
        @media print {
            .no-print { display: none; }
            .page-header { margin-bottom: 20px; }
        }
    </style>
</head>
<body class="">
    <div class="page">
        <div class="page-main">
            <?php require_once('includes/header.php') ?>
            <div class="my-3 my-md-5">
                <div class="container">
                    <div class="page-header mb-4 no-print">
                        <div class="row align-items-center">
                            <div class="col">
                                <h1 class="page-title">Payroll Report</h1>
                                <p class="page-subtitle"><?php echo $month_name . ' ' . $year; ?> - Biometric Based</p>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group">
                                    <a href="?format=csv&month=<?php echo $month; ?>&year=<?php echo $year; ?>" class="btn btn-success">
                                        <i class="fe fe-download mr-2"></i>Export CSV
                                    </a>
                                    <a href="?format=excel&month=<?php echo $month; ?>&year=<?php echo $year; ?>" class="btn btn-primary">
                                        <i class="fe fe-file-text mr-2"></i>Export Excel
                                    </a>
                                    <button onclick="window.print()" class="btn btn-outline-primary">
                                        <i class="fe fe-printer mr-2"></i>Print
                                    </button>
                                    <a href="biometric_integration.php#payroll" class="btn btn-outline-secondary">
                                        <i class="fe fe-arrow-left mr-2"></i>Back
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payroll Summary -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h3 class="text-primary"><?php echo count($payroll_data); ?></h3>
                                    <p class="text-muted mb-0">Employees</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h3 class="text-info"><?php echo number_format($total_hours, 1); ?></h3>
                                    <p class="text-muted mb-0">Total Hours</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h3 class="text-success">₱<?php echo number_format($total_gross_pay, 2); ?></h3>
                                    <p class="text-muted mb-0">Gross Pay</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h3 class="text-warning">₱<?php echo number_format($total_net_pay, 2); ?></h3>
                                    <p class="text-muted mb-0">Net Pay</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payroll Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Payroll Details - <?php echo $month_name . ' ' . $year; ?></h3>
                        </div>
                        <div class="card-body">
                            <?php if (empty($payroll_data)): ?>
                                <div class="alert alert-info">
                                    <i class="fe fe-info mr-2"></i>
                                    No payroll data found for <?php echo $month_name . ' ' . $year; ?>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Employee ID</th>
                                                <th>Full Name</th>
                                                <th>Department</th>
                                                <th>Position</th>
                                                <th>Days</th>
                                                <th>Hours</th>
                                                <th>Basic Pay</th>
                                                <th>Overtime</th>
                                                <th>Gross Pay</th>
                                                <th>Deductions</th>
                                                <th>Net Pay</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($payroll_data as $row): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($row['employee_id']); ?></td>
                                                <td><?php echo htmlspecialchars($row['fullname']); ?></td>
                                                <td><?php echo htmlspecialchars($row['department_name'] ?? 'N/A'); ?></td>
                                                <td><?php echo htmlspecialchars($row['position_name'] ?? 'N/A'); ?></td>
                                                <td><?php echo $row['total_days_worked']; ?></td>
                                                <td><?php echo number_format($row['total_hours'], 1); ?></td>
                                                <td>₱<?php echo number_format($row['basic_pay'], 2); ?></td>
                                                <td>₱<?php echo number_format($row['overtime_pay'], 2); ?></td>
                                                <td>₱<?php echo number_format($row['gross_pay'] ?: $row['basic_pay'], 2); ?></td>
                                                <td>₱<?php echo number_format($row['deductions'], 2); ?></td>
                                                <td><strong>₱<?php echo number_format($row['net_pay'] ?: $row['basic_pay'], 2); ?></strong></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-active">
                                                <th colspan="8">TOTALS</th>
                                                <th>₱<?php echo number_format($total_gross_pay, 2); ?></th>
                                                <th>-</th>
                                                <th><strong>₱<?php echo number_format($total_net_pay, 2); ?></strong></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="card-footer text-muted">
                            Generated on <?php echo date('F d, Y H:i:s'); ?> | 
                            Based on biometric attendance data
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
