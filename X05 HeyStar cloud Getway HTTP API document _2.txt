
1. Document Description

 This document is mainly used for the middleware HTTP interface docking of the face recognition machine and is suitable for system developers and maintenance personnel.
1.1 Server Preparation

1. Ports 10010 , 10011 , and 8190 need to be open

2. Support Windows and Linux systems

1.2 Service Startup







1.

Enter Password: 123456




1. <PERSON><PERSON> to enter the communication settings and configure the server information



1. Communication Settings

* Turn on the server switch

* Select Cloud as the connection method

* Select TCP as the connection protocol

* The password you set should be consistent with the following interface parameter secret
* Server IP: fill in the server IP where the middleware is deployed

* The server port is set to 10010




2. Interface Description

2.1 Interface Specification


* Interface syntax: http://{middleware server IP}:8190/api/{module}/{action}

* {middleware server IP} : The IP address of the requested middleware server

* {module} : module name

* {action} : interface name

* Interface form : Providing external services by converting TCP to HTTP

* Character encoding: UTF-8 format is used uniformly

* Request header: Content-Type = application/x-www-form-urlencoded

* Request method: Use POST method uniformly

2.2 Interface Return
 The data returned by all interfaces and the interface return examples involved in the document may be slightly adjusted for the return data of individual interfaces. The actual return results shall prevail.


Fields
describe
type
Must
be trans
illustrate





mitted

code
Return Code
String
Y
Normal operations use a unified return
code, and each abnormal operation has a separate return code
msg
Return
informatio n
String
Y
Information returned by the interface, usually error information
data
Return data
Int / String / Object / List
N
The data returned by the interface may be a value, string, object or collection
ts
Return
time
Long
Y
Unix millisecond timestamp ( 13
bits)
success
Execution
Results
boolean
Y
Whether the operation is successful,
true for success and false for failure




2.3 Notes

* When calling a device interface, do not call the interface of the same device on other client servers at the same time.
* If the call to the interface returns a message "Parameter abnormality", please check according to the following steps:
* Check whether the parameter name is misspelled, contains spaces or carriage returns.
* Check whether the parameter value meets the specification, such as Int value is undefined, id contains illegal characters other than numbers and letters, etc.


* Check whether there are spaces in the Json string (if you use the Json string returned by the interface directly, you need to remove the spaces), whether there are too many or too few " symbols, please check the postman examples of each interface carefully.
* If the call interface returns empty, it means the URL is wrong (wrong IP, spelling error, field missing, etc.)
* If the call to the interface returns Could not get any response, possible reasons include: incorrect IP address, incorrect or omitted port number, incorrect parameter transmission format, etc.




3. Device Management Interface

3.1 Get device information

deviceKey is here:






Description : Get the device information of the face recognition machine.

Method
URL
GET / POST
http://middleware server IP:8190/api/device/get
Header Field

Parameter name
describe
type
Must be trans mitte
d
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be trans mitte
d
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
Postman Example




Return to example


3.2 Device Restart

Description : Restart the face recognition device.

Method
URL
POST
http://middleware server
IP:8190/api/device/reboot
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be trans mitte
d
Additional Notes
deviceKey
Device
serial number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the face
device
Postman Example




Return to example

code code description

code
msg
100201
Device reboot failed, not rooted

3.3 Device Reset

Description : Reset the face recognition device.

Method
URL
POST
http://middleware server IP:8190/api/device/reset
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field


Parameter name
describe
type
Must be trans mitte
d
Additional Notes
secret
password
String
Y
* The server password set in the communication settings on the face
device
deviceKey
Device serial
number
String
Y
* Device serial number, 18 digits
type
Device reset type
Int
Y
Device reset type, reset the corresponding type of information on the device, and cannot be restored after reset
* 1 : All data
* 2: Personnel registration information
* 3 : Photo information
* 4: Personnel characteristic information
* 5: Identify records
Postman Example



Return to example







3.4 Device Configuration

Description : Configure the recognition parameters, recognition mode parameters, peripheral parameters, UI display parameters, server parameters, etc. of the face recognition machine.
When calling the interface, if the corresponding parameters are not filled in, the

system default parameters will be used for setting.

Method
URL
POST
http://middleware server IP:8190/api/device/setConfig
Header Field

Parameter name
describe
type
Must be trans mitte
d
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must
be
Additional Notes




trans mitte
d

secret
password
String
Y
The server password set in the communication settings on the face
device
deviceKey
Device serial number
String
Y
Device serial number, 18 digits
recThreshold1vN
1vN face recognition threshold
Int
N
The default is 65.
Please enter an integer between 50 and 100. The higher the score, the higher the recognition accuracy, but the recognition speed will be slower.
recThreshold1v1
1v1 face recognition
threshold
Int
N
Same as above
recInterval
Identification interval time
Int
N
The default is 3 seconds. Recognition interval (the interval between callback notifications after consecutive successful recognitions
of the same person).
recDistance
Identification distance
Int
N
Identification distance:
* 0: No limit (default)
* 1: within 0.5 meters
* 2: Within 1 meter
* 3: within 1.5 meters
* 4: Within 2 meters
* 5: Within 3 meters
* 6: Within 4 meters
recRank
Identify live level
Int
N
Identify the level of living body:
* 1. No liveness detection (default)
* 2. Monocular liveness





* 3. Binocular liveness
recSucTtsMode
Voice Mode
Int
N
Voice Mode:
* 1. No voice broadcast
* 2. Announce name (default)
* 100. Customized voice broadcast
recSucTtsCustom
Customized voice content
String
N
Customized voice content
* Default{name}
* IfrecSucTtsModeis   100,   this field must be passed and cannot be empty
* {name}is allowed . The field format is fixed, and other contents only allow numbers, English and Chinese characters.
* The length limit is 255 characters. For example:
{name}Welcome
recSucDisplayMode
Display Mode
Int
N
Display Mode
* 1: Name (default)
* 2: Do not display content
* 100: Custom display
recSucDisplayCusto m
Display custom content
Int
N
Display custom content
* Default{name}
* IfrecSucDisplayModeis        100, this field must be passed and cannot be empty
* {name}is allowed . The field
format is fixed. Other content only allows numbers, Chinese





and English, and Chinese and English symbols. The length is limited to 255 characters. For example:{name}, check-in successful!
recStrangerEnable
Identify
strangers switch
Int
N
Identify stranger switch:
* 0. Do not identify strangers
* 1. Identify strangers (default)
recIsStrangerTimes
Number of
recognition failures
Int
N
How many times of identification
failure is considered a stranger. Default is 2 times
recStrangerTtsMod e
Stranger Voice Mode
Int
N
Stranger Voice Mode
* 1: Do not play voice (default)
* 2: Voice broadcast "The user is not registered, please contact the administrator"
* 100: Customize stranger voice
recStrangerTtsCust om
Voice broadcast of customized content
String
N
Voice broadcast of customized content,
* IfrecStrangerTtsModeis        100, this field must be passed and cannot be empty.
* The content only allows numbers, English and Chinese characters, and the length is limited to 255 characters. For
example: Beware of strangers

recStrangerDisplay Mode
Stranger screen display text type
Int
N
Stranger screen display text type
* 1. The person is not registered (default)
* 100. Customization
recStrangerDisplay Custom
Text content displayed on stranger screen
String
N
Customized text displayed on stranger screen
* IfrecStrangerDisplayModeis           100, this field must be passed and cannot be empty.
* The content only allows numbers, Chinese and English, and Chinese and English symbols, and the length is limited to 255 characters. For example: The person is not
registered!
recNotBioTtsMode
Non-live voice mode
Int
N
Non-live voice mode
* 1: Do not play voice (default)
* 2: Voice announcement "Do not try to use photos or videos for recognition"
* 100: Custom non-living voice
recNotBioTtsCusto m
Voice broadcast of customized content
String
N
Voice broadcast of customized content,
* IfrecNotBioTtsModeis    100,    this field must be passed and cannot be empty.
* The content only allows numbers, English and Chinese characters, and the length is limited to 255 characters. For example: Please do not try to
use photos or videos for





recognition
recNotBioDisplayM ode
Non-living screen display text type
Int
N
Non-living screen display text type
* 1 : Do not try to use photos or videos for recognition (default)
* 100 : Custom
recNotBioDisplayCu stom
Non-living screen displays text content
String
N
Non-living screen displays text custom content
* IfrecNotBioDisplayModeis          100, this field must be passed and cannot be empty.
* The content only allows numbers, Chinese and English, and Chinese and English symbols, and the length is limited to 255 characters. For example: Please do not try to use photos or videos for
recognition!
recNoPerTtsMode
Insufficient permissions Voice mode
Int
N
Insufficient permissions Voice mode
* 1: Do not play voice
* 2: Voice broadcast "Insufficient permissions, please contact the administrator" (default)
* 100: Insufficient custom
permissions for voice

recNoPerTtsCusto m
Voice broadcast of customized content
String
N
Voice broadcast of customized content
* IfrecNoPerTtsModeis    100,    this field must be passed and cannot be empty.
* The content only allows numbers, English and Chinese characters, and the length is limited to 255 characters. If you do not have enough permissions, please contact the
administrator
recNoPerDisplayMo de
Insufficient permissions screen display text type
Int
N
Insufficient permissions screen display text type
* 1 Insufficient permissions, please contact the administrator (default)
* 100 Custom
recNoPerDisplayCu stom
Insufficient permissions to display text content on the screen
String
N
Insufficient permissions to display custom text on the screen
* IfrecNoPerDisplayModeis         100, this field must be passed and cannot be empty.
* The content only allows numbers, Chinese and English, and Chinese and English symbols, and the length is limited to 255 characters. If: insufficient permissions, please
contact the administrator!
recStrangerOpenDo or
Identify whether a stranger
Int
N
Identify whether a stranger opens the door
* 0: Do not open the door


opens the door


(default)
* 1 : Open the door
recMultiplayer
Identify multiple people
Int
N
Identify multiple people
* 0: Single person recognition (default)
* 1: Multi-person recognition
recSnapshotSave
Identify and
save snapshots
Int
N
Identify and save snapshots
* 0 : Do not save
* 1 : Save (default)
recRecordSave
Identify and save records
Int
N
Identify and save records
* 0 : Do not save
* 1 : Save (default)
recRecordSaveDay s
Identification Record Retention
Days
Int
N
Identification record retention days, default retention is 7 days
recRecordUploadM ode
Identify record upload mode
Int
N
Identify record upload mode
* 1: Real-time upload (default)
* 2: Resume download
recWhitelistIdcard
Whitelist switch for identity verification
Int
N
Whitelist switch for identity verification
* 0: Off (default), directly perform the ID verification process after reading the ID card
* 1: On, read the ID number and compare it with the ID numbers of all people in the database. If it exists, perform a person- document comparison; if it does not exist, prompt insufficient
authority

recModeFaceEnabl e
Face recognition
mode switch
Int
N
Face recognition mode switch
* 0 : Off
* 1 : On (default)
recModeCardEnabl e
Card swipe mode switch
Int
N
Card swipe mode switch
* 0 : Off
* 1 : On (default)
recModeCardIntf
Card number transmission interface
Int
N
Card number transmission interface
* 1 : Wiegand (default)
* 2 : TTL serial port
* 3 : USB
recModeCardIntfPor t
Card number transmission serial port number
Int
N
Card number transmission serial port number
 This field is effective when the card number transmission interface recModeCardIntfis configured as a
rseialport.
* 1 : ttyS1
* 2 : ttyS2
* 3 : ttyS3 (default)
* 4 : ttyS4
* 100 : Custom
recModeCardIntfPor tCustom
Customize the card number transmission interface port number
String
N
Customize the port number of the card number transmission interface
* This field takes effect when the card number transmission interface port
daInfPoercMdC	trtis
configured as 100

recModeCardIntfBa udrate
Card number transmission serial port baud rate
Int
N
Card number transmission serial port baud rate
 This field is effective when the card number transmission interface recModeCardIntfis configured as a
rseialport.
* 1 : 9600
* 2 : 19200
* 3 : 38400
* 4 : 115200 (default)
recModeIdcardEnab le
Person-ID verification
switch
Int
N
Person-ID verification switch
* 0 : Disable (default)
* 1 : Open
recModeIdcardIntf
ID card module transmission interface
Int
N
ID card module transmission interface
* 1: TTL serial port
* 2: 232 serial port
* 3 : USB (default)
recModeIdcardIntfP ort
ID card transmission serial port number
Int
N
ID card transmission serial port number
This field is effective when the ID card transmission interface is
rsepilconfigured as	rt.
* 1 : ttyS1
* 2 : ttyS2 (default)
* 3 : ttyS3
* 4 : ttyS4
* 100 : Custom
recModeIdcardIntfP ortCustom
Customize the ID card transmission interface port
number
String
N
Customize the port number of the ID card transmission interface
This field takes effect when the ID card transmission interface port
tfroeaifIdnMocgurcPoe	d	tsired





as100
recModeIdcardIntfB audrate
ID card transmission serial port baud rate
Int
N
ID card transmission serial port baud rate
This field is effective when the ID card transmission interface is configured as a serial port.
* 1 : 9600
* 2 : 19200
* 3 : 38400
* 4 : 115200 (default)
recModeQrcodeEna ble
QR code scanning
mode switch
Int
N
QR code scanning mode switch
* 0 : Disable (default)
* 1 : Open
recModeQrcodeIntf
QR code transmission interface
Int
N
QR code transmission interface
* 1: TTL serial port (default)
* 2: 232 serial port
* 3 : USB
recModeQrcodeIntf Port
QR code transmission serial port number
Int
N
The port number of the QR code transmission serial port
This field is effective when the QR code transmission interface
tIneofdinrigQcsodrMuce	redas
orseaialpt.
* 1 : ttyS1
* 2 : ttyS2 (default)
* 3 : ttyS3
* 4 : ttyS4
* 100 : Custom

recModeQrcodeIntf PortCustom
Customize the QR code transmission interface port number
String
N
Customize the port number of the QR code transmission interface This field takes effect when the QR code transmission interface port
trIfidrcQrecMo	nPots
focniguredas100
recModeQrcodeIntf Baudrate
QR code transmission serial port baud rate
Int
N
Baud rate of the serial port for QR code transmission
This field is effective when the QR code transmission interface
                                                                                                                                                                                                                                                                          trIfidrcQrecMo	nPots        frsenociguirsleadpt.
* 1 : 9600
* 2 : 19200
* 3 : 38400
* 4 : 115200 (default)
recModePalmEnabl e
Palmprint
recognition switch
Int
N
Palmprint recognition switch
* 0 : Disable (default)
* 1 : Open
recModeFingerEna ble
Fingerprint
recognition switch
Int
N
Fingerprint recognition switch
* 0 : Disable (default)
* 1 : Open
pciRelayOut
Relay output
Int
N
Relay output
* 0 : Do not open the door
* 1: Open the door (default)
pciRelayMode
Relay Mode
Int
N
Relay Mode
* 1 : Normally open
* 2 : Normally closed

pciRelayDelay
Relay control time
Int
N
Relay control time
* 1000ms (default)
* The time interval between the door opening and door closing controlled by the relay is 1000ms by default. Please enter an integer between 100-25500, rounded down to the nearest hundred. For example, if you enter an integer between 101- 199, the actual effective time is
100ms.
pciWgType
Wiegand Type
Int
N
Wiegand Type
* 1: Wiegand 26 (default)
* 2 : Wiegand 34
* 3 : Wiegand 37
pciWgOut
Wiegand output type
Int
N
Wiegand output type
* 0: No output (default)
* 1: Output personnel id
* 2: Output card number
* 100 : Custom
pciWgOutCustom
Wiegand output custom content
String
N
Wiegand output custom content This field is effective when the Wiegand output type pciWgOutis
focniguredas100
pciComOut
Serial port output type
Int
N
Serial port output type
* 0: No output (default)
* 1: Output personnel id
* 2: Output card number
* 3: Output ID card
* 100 : Custom

pciComOutCustom
Serial port output custom content
String
N
Serial port output custom content This field takes effect when the serial port output type pciComOutis
focniguredas100
pciComOutPort
Serial output port
Int
N
Serial output port
* 1 : ttyS1
* 2 : ttyS2
* 3 : ttyS3
* 4 : ttyS4
* 100 : Custom
pciComOutPortCust om
Serial output port customization
String
N
Serial output port customization This field takes effect when the serial port output port
frcuotncpgiOsPtoumC	redas100
pciComOutBaudrat e
Serial port output baud rate
Int
N
Serial port output baud rate
* 1 : 9600
* 2 : 19200
* 3 : 38400
* 4 : 115200 (default)
pciLedAlwaysEnabl e
Fill light is always on
Int
N
Fill light is always on
* 0: Not always on (default)
* 1: Always on
pciLedColorStrange r
Fill light color (stranger)
Int
N
Fill light color (stranger)
* 1: Red light (default)
* 2 : Green light
uiCompanyName
The interface displays the company
name
String
N
Whether the apk main interface displays the middleware server IP
* 0 : Hide
* 1 : Show (default)
uiShowSn
The main interface displays the
device serial
Int
N
Whether the device serial number is displayed on the apk main interface
* 0 : Hide
* 1 : Show (default)


number



uiShowIp
Whether the main interface displays the middleware
server IP
Int
N
Whether the apk main interface displays the middleware server IP
* 0 : Hide
* 1 : Show (default)
uiShowPersonCoun t
Whether the main interface displays the number of
people
Int
N
Whether the number of people is displayed on the main interface of apk
* 0 : Hide
* 1 : Show (default)
uiScreensaverWait
Screen saver waiting time
Int
N
apk enters screen saver waiting time, unit is seconds
sevUploadRecReco rdUrl
The URL address to upload the identification
record
String
N

sevUploadDevHeart beatUrl
Upload the URL address of the device
heartbeat
String
N

sevUploadDevHeart beatInterval
Device heartbeat
interval
Int
N
Device heartbeat interval, in seconds, default 60, 10 - 300
sevUploadRegPers onUrl
Upload the url address of the registered personnel
data
String
N



sevUploadRecStran gerDataEnable
Upload stranger identification data switch
Int
N
Whether to upload stranger identification data switch
* 0 : Do not upload
* 1: Upload (default)
sevUploadRecSnap shotEnable
Upload recognition capture photo
switch
Int
N
Whether to upload the switch for identifying stolen photos
* 0 : Do not upload
* 1: Upload (default)
sevUploadRecCard DataEnable
Upload card swipe data switch
Int
N
Whether to upload data to identify card swipe
* 0 : Do not upload
* 1: Upload (default)
sevUploadRecIdcar dDataEnable
Upload ID card identification data switch
Int
N
Whether to upload ID card identification data switch
* 0 : Do not upload
* 1: Upload (default)
sevUploadRecQrco deDataEnable
Upload and identify QR code data switch
Int
N
Whether to upload the QR code data switch
* 0: Do not upload (default)
* 1 : Upload
Postman Example


Return to example



























































Branch: Shenzhen Bio Technology Co., Ltd Headquarter: Chongqing Huifan Technology Co., Ltd


- 28 -





3.5 Identification Configuration
Description : Configure the recognition parameters of the face recognition machine. When calling the interface, if the corresponding parameters are not filled in, the system default parameters will be used for setting.

Method
URL
POST
http://middleware server
IP:8190/api/device/setRecConfig
Header Field

Parameter name
describe
type
Must be trans mitte
d
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Mu st be tra ns mitt
ed
Additional Notes
secret
password
String
Y
The server password set in the communication settings on the face
device

deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
recThreshold1vN
1vN face recognition threshold
Int
N
The default is 65.
Please enter an integer between 50 and 100. The higher the score, the higher the recognition accuracy, but
the recognition speed will be slower.
recThreshold1v1
1v1 face recognition
threshold
Int
N
Same as above
recInterval
Identification interval time
Int
N
The default is 3 seconds. Recognition interval (the interval between callback notifications after
consecutive successful recognitions of
the same person).
recDistance
Identification distance
Int
N
Identification distance:
* 0: No limit (default)
* 1: within 0.5 meters
* 2: Within 1 meter
* 3: within 1.5 meters
* 4: Within 2 meters
* 5: Within 3 meters
* 6: Within 4 meters
recRank
Identify live level
Int
N
Identify the level of living body:
* 1. No liveness detection (default)
* 2. Monocular liveness
* 3. Binocular liveness
recSucTtsMode
Voice Mode
Int
N
Voice Mode:
* 1. No voice broadcast
* 2. Announce name (default)
* 100. Customized voice broadcast

recSucTtsCustom
Customized voice content
String
N
Customized voice content
* Default{name}
* IfrecSucTtsModeis  100,  this  field must be passed and cannot be empty
* {name}is allowed . The field format is fixed, and other contents only allow numbers, English and Chinese characters.
* The length limit is 255 characters.
For example:{name}Welcome
recSucDisplayMode
Display Mode
Int
N
Display Mode
* 1: Name (default)
* 2: Do not display content
* 100: Custom display
recSucDisplayCustom
Display custom content
Int
N
Display custom content
* Default{name}
* IfrecSucDisplayModeis    100,    this field must be passed and cannot be empty
* {name}is allowed . The field format is fixed. Other content only allows numbers, Chinese and English, and Chinese and English symbols. The length is limited to 255 characters. For example:
{name}, check-in successful!
recStrangerEnable
Identify strangers
switch
Int
N
Identify stranger switch:
* 0. Do not identify strangers
* 1. Identify strangers (default)
recIsStrangerTimes
Number of
recognition failures
Int
N
How many times of identification
failure is considered a stranger. Default is 2 times

recStrangerTtsMode
Stranger Voice Mode
Int
N
Stranger Voice Mode
* 1: Do not play voice (default)
* 2: Voice broadcast "The user is not registered, please contact the administrator"
* 100: Customize stranger voice
recStrangerTtsCusto m
Voice broadcast of customized content
String
N
Voice broadcast of customized content,
* IfrecStrangerTtsModeis     100,    this field must be passed and cannot be empty.
* The content only allows numbers, English and Chinese characters, and the length is limited to 255 characters. For example: Beware
of strangers
recStrangerDisplayMo de
Stranger screen display text
type
Int
N
Stranger screen display text type
* 1. The person is not registered (default)
* 100. Customization
recStrangerDisplayCu stom
Text content displayed on stranger screen
String
N
Customized text displayed on stranger screen
* IfrecStrangerDisplayModeis           100, this field must be passed and cannot be empty.
* The content only allows numbers, Chinese and English, and Chinese and English symbols, and the length is limited to 255 characters. For example: The
person is not registered!

recNotBioTtsMode
Non-live voice mode
Int
N
Non-live voice mode
* 1: Do not play voice (default)
* 2: Voice announcement "Do not try to use photos or videos for recognition"
* 100: Custom non-living voice
recNotBioTtsCustom
Voice broadcast of customized content
String
N
Voice broadcast of customized content,
* IfrecNotBioTtsModeis    100,    this field must be passed and cannot be empty.
* The content only allows numbers, English and Chinese characters, and the length is limited to 255 characters. For example: Please do not try to use photos or videos
for recognition
recNotBioDisplayMod e
Non-living screen display text
type
Int
N
Non-living screen display text type
* 1 : Do not try to use photos or videos for recognition (default)
* 100 : Custom
recNotBioDisplayCust om
Non-living screen displays text content
String
N
Non-living screen displays text custom content
* IfrecNotBioDisplayModeis          100, this field must be passed and cannot be empty.
* The content only allows numbers, Chinese and English, and Chinese and English symbols, and the length is limited to 255 characters. For example: Please do not try to use photos or videos
for recognition!

recNoPerTtsMode
Insufficient permissions Voice mode
Int
N
Insufficient permissions Voice mode
* 1: Do not play voice
* 2: Voice broadcast "Insufficient permissions, please contact the administrator" (default)
* 100: Insufficient custom
permissions for voice
recNoPerTtsCustom
Voice broadcast of customized content
String
N
Voice broadcast of customized content
* IfrecNoPerTtsModeis    100,    this field must be passed and cannot be empty.
* The content only allows numbers, English and Chinese characters, and the length is limited to 255 characters. If you do not have enough permissions, please
contact the administrator
recNoPerDisplayMod e
Insufficient permissions screen display text
type
Int
N
Insufficient permissions screen display text type
* 1 Insufficient permissions, please contact the administrator (default)
* 100 Custom
recNoPerDisplayCust om
Insufficient permissions to display text content on the screen
String
N
Insufficient permissions to display custom text on the screen
* IfrecNoPerDisplayModeis         100, this field must be passed and cannot be empty.
* The content only allows numbers, Chinese and English, and Chinese and English symbols, and the length is limited to 255
characters. If: insufficient





permissions, please contact the administrator!
recStrangerOpenDoor
Identify whether a stranger opens the
door
Int
N
Identify whether a stranger opens the door
* 0: Do not open the door (default)
* 1 : Open the door
recMultiplayer
Identify multiple people
Int
N
Identify multiple people
* 0: Single person recognition (default)
* 1: Multi-person recognition
recSnapshotSave
Identify and
save snapshots
Int
N
Identify and save snapshots
* 0 : Do not save
* 1 : Save (default)
recRecordSave
Identify and save records
Int
N
Identify and save records
* 0 : Do not save
* 1 : Save (default)
recRecordSaveDays
Identification Record Retention
Days
Int
N
Identification record retention days, default retention is 7 days
recRecordUploadMod e
Identify
record upload mode
Int
N
Identify record upload mode
* 1: Real-time upload (default)
* 2: Resume download
recWhitelistIdcard
Whitelist switch for identity verification
Int
N
Whitelist switch for identity verification
* 0: Off (default), directly perform the ID verification process after reading the ID card
* 1: On, read the ID number and
compare it with the ID numbers of






all people in the database. If it exists, perform a person- document comparison; if it does not exist, prompt insufficient authority
Postman Example

Return to example





3.6 Identification Mode Configuration
Description : Configure the parameters related to the face recognition mode. When calling the interface, if the corresponding parameters are not filled in, the system default parameters will be used for setting.

Method
URL
POST
http://middleware server IP:8190/api/device/setRecModeConfig


Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be transmi
tted
Additional Notes
secret
password
String
Y
* The server password set in the communication settings
on the face device
deviceKey
Device serial number
String
Y
* Device serial number, 18 digits
recModeFaceEn able
Face recognition mode switch
Int
N
Face recognition mode switch
* 0 : Off
* 1 : On (default)
recModeCardEn able
Card swipe mode switch
Int
N
Card swipe mode switch
* 0 : Off
* 1 : On (default)
recModeCardIntf
Card number transmission interface
Int
N
Card number transmission interface
* 1 : Wiegand (default)
* 2 : TTL serial port
* 3 : USB

recModeCardIntf Port
Card number transmission serial port number
Int
N
Card number transmission serial port number
This field is effective when the card number transmission interfacerecModeCardIntfis
rsepilconfigured as	rt.
* 1 : ttyS1
* 2 : ttyS2
* 3 : ttyS3 (default)
* 4 : ttyS4
* 100 : Custom
recModeCardIntf PortCustom
Customize the card number transmission interface port number
String
N
Customize the port number of the card number transmission interface
This field takes effect when the card number transmission interface port
daInfPoercMdC	trtis
configured as 100
recModeCardIntf Baudrate
Card number transmission serial port baud rate
Int
N
Card number transmission serial port baud rate
This field is effective when the card number transmission interfacerecModeCardIntfis
rsepilconfigured as	rt.
* 1 : 9600
* 2 : 19200
* 3 : 38400
* 4 : 115200 (default)
recModeIdcardE nable
Person-ID
verification switch
Int
N
Person-ID verification switch
* 0 : Disable (default)
* 1 : Open

recModeIdcardIn tf
ID card module transmission interface
Int
N
ID card module transmission interface
* 1: TTL serial port
* 2: 232 serial port
* 3 : USB (default)
recModeIdcardIn tfPort
ID card transmission serial port number
Int
N
ID card transmission serial port number
This field is effective when the ID card transmission interface is
rsepilconfigured as	rt.
* 1 : ttyS1
* 2 : ttyS2 (default)
* 3 : ttyS3
* 4 : ttyS4
* 100 : Custom
recModeIdcardIn tfPortCustom
Customize the ID card transmission interface port number
String
N
Customize the port number of the ID card transmission interface
This field takes effect when the ID card transmission interface
tearfiIdcdoMcport e	nPots
focniguredas100
recModeIdcardIn tfBaudrate
ID card transmission serial port baud rate
Int
N
ID card transmission serial port baud rate
This field is effective when the ID card transmission interface is configured as a serial port.
* 1 : 9600
* 2 : 19200
* 3 : 38400
* 4 : 115200 (default)

recModeQrcode Enable
QR code scanning mode
switch
Int
N
QR code scanning mode switch
* 0 : Disable (default)
* 1 : Open
recModeQrcodeI ntf
QR code transmission interface
Int
N
QR code transmission interface
* 1: TTL serial port (default)
* 2: 232 serial port
* 3 : USB
recModeQrcodeI ntfPort
QR code transmission serial port number
Int
N
The port number of the QR code transmission serial port
This field is effective when the QR code transmission interface
tIneofdinrigQcsodrMuce	red
orseasialpt.
* 1 : ttyS1
* 2 : ttyS2 (default)
* 3 : ttyS3
* 4 : ttyS4
* 100 : Custom
recModeQrcodeI ntfPortCustom
Customize the QR code transmission interface port number
String
N
Customize the port number of the QR code transmission interface
This field takes effect when the QR                                                                              code                                                                                transmission                                                                                interface trIfidrcQport ecMo	nPots
focniguredas100
recModeQrcodeI ntfBaudrate
QR code transmission serial port baud rate
Int
N
Baud rate of the serial port for QR code transmission
This field is effective when the QR code transmission interface
trIfidrcQrecMo	nPots        frsenociguarsileadpt.
* 1 : 9600
* 2 : 19200






* 3 : 38400
* 4 : 115200 (default)
recModePalmEn able
Palmprint
recognition switch
Int
N
Palmprint recognition switch
* 0 : Disable (default)
* 1 : Open
recModeFingerE nable
Fingerprint
recognition switch
Int
N
Fingerprint recognition switch
* 0 : Disable (default)
* 1 : Open
Postman Example

Return to example




code code description

code
msg
100201
Device reboot failed, not rooted



3.7 Peripheral Configuration


Description : Configure the parameters related to the peripheral mode of the face recognition machine.
When calling the interface, if the corresponding parameters are not filled in, the

system default parameters will be used for setting.

Method
URL
POST
http://middleware server IP:8190/api/device/setPciConfig
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
deviceKey
Device serial
number
String
Y
* Device serial number, 18 digits
secret
password
String
Y
* The server password set in the
communication settings on the face device
pciRelayOut
Relay output
Int
N
Relay output
* 0 : Do not open the door
* 1: Open the door (default)
pciRelayMode
Relay Mode
Int
N
Relay Mode
* 1 : Normally open
* 2 : Normally closed

pciRelayDelay
Relay control time
Int
N
Relay control time
* 1000ms (default)
* The time interval between the door opening and door closing controlled by the relay is 1000ms by default. Please enter an integer between 100-25500, rounded down to the nearest hundred. For example, if you enter an integer between 101- 199, the actual effective time is
100ms.
pciWgType
Wiegand Type
Int
N
Wiegand Type
* 1: Wiegand 26 (default)
* 2 : Wiegand 34
* 3 : Wiegand 37
pciWgOut
Wiegand output type
Int
N
Wiegand output type
* 0: No output (default)
* 1: Output personnel id
* 2: Output card number
pciComOut
Serial port output type
Int
N
Serial port output type
* 0: No output (default)
* 1: Output personnel id
* 2: Output card number
* 3: Output ID card
* 100 : Custom
pciComOutCust om
Serial port output custom
content
String
N
Serial port output custom content This field takes effect when the serial port output type pciComOutis
focniguredas100


pciComOutPort
Serial output port
Int
N
Serial output port
* 1 : ttyS1
* 2 : ttyS2
* 3 : ttyS3
* 4 : ttyS4
* 100 : Custom
pciComOutPortC ustom
Serial output port customizatio
n
String
N
Serial output port customization
This field takes effect when the serial port output port pciComOutPortis
focniguredas100
pciComOutBaud rate
Serial port output baud rate
Int
N
Serial port output baud rate
* 1 : 9600
* 2 : 19200
* 3 : 38400
* 4 : 115200 (default)
pciLedAlwaysEn able
Fill light is always on
Int
N
Fill light is always on
* 0: Not always on (default)
* 1: Always on
pciLedColorStra nger
Fill light color
(stranger)
Int
N
Fill light color (stranger)
* 1: Red light (default)
* 2 : Green light
Postman Example

Return to example





3.8 Interface Display Configuration

Description : Configure the interface display parameters of the face recognition machine.
When calling the interface, if the corresponding parameters are not filled in, the

system default parameters will be used for setting.

Method
URL
POST
http://middleware server
IP:8190/api/device/setUiConfig
Header Field

Parameter name
describe
type
Must be trans
mitted
Additional Notes
Content-Type
Request
String
Y
application/x-www-form-urlencoded



body type



Body Field

Parameter name
describe
type
Must be transmitt
ed
Additional Notes
deviceKey
Device serial
number
String
Y
* Device serial number, 18
digits
secret
password
String
Y
* The server password set in
the communication settings on the face device
uiCompanyNa me
The interface
displays the company name
String
N
The company name displayed on the apk interface
uiShowSn
The main interface displays the device serial
number
Int
N
Whether the device serial number is displayed on the apk main interface
* 0 : Hide
* 1 : Show (default)
uiShowIp
Whether the main interface displays the middleware
server IP
Int
N
Whether the apk main interface displays the middleware server IP
* 0 : Hide
* 1 : Show (default)
uiShowPerson Count
Whether the main interface displays the number of
people
Int
N
Whether the number of people is displayed on the main interface of apk
* 0 : Hide
* 1 : Show (default)
uiScreensaver
Wait
Screen saver
waiting time
Int
N
* apk enters screen saver
waiting time, unit is seconds
Postman Example




Return to example




3.9 Server Configuration

Description : Configure the server-related parameters of the face recognition machine.
When calling the interface, if the corresponding parameters are not filled in, the system default parameters will be used for setting.


Method
URL
POST
http://middleware server IP:8190/api/device/
setSevConfig
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
sevUploadRecReco rdUrl
The URL address to upload the identification
record
String
N
The callback address must conform to the regular
expression: ((http|https)://)((a- zA-Z0-9\._-{2,6})|(0-9{1,3}\.0- 9{1,3}))(:0-9*)?
sevUploadDevHeart beatUrl
Upload the URL address of the device
heartbeat
String
N
Same as above
sevUploadRegPers onUrl
Upload the url address of the
registered
String
N
Same as above


personnel data



sevUploadRegFing erUrl
URL address for uploading registered
fingerprint data
String
N
Same as above
sevUploadAlarmUrl
URL address for uploading
alarm data
String
N
Same as above
sevUploadRegPalm Url
The URL address to upload the
palm data
String
N
Same as above
sevUploadDevHeart beatInterval
Device heartbeat
interval
Int
N
* Device heartbeat interval, in seconds, default 60, 10 - 300
sevUploadRecStran gerDataEnable
Upload stranger identification
data switch
Int
N
Whether to upload stranger identification data switch
* 0 : Do not upload
* 1: Upload (default)
sevUploadRecSnap shotEnable
Upload recognition capture photo
switch
Int
N
Whether to upload the recognition snapshot switch
* 0 : Do not upload
* 1: Upload (default)
sevUploadRecCard DataEnable
Upload card swipe data switch
Int
N
Whether to upload data to identify card swipe
* 0 : Do not upload
* 1: Upload (default)
sevUploadRecIdcar dDataEnable
Upload ID card identification data switch
Int
N
Whether to upload ID card identification data switch
* 0 : Do not upload
* 1: Upload (default)


sevUploadRecQrco deDataEnable
Upload and identify QR code data
switch
Int
N
Whether to upload the QR code data switch
* 0: Do not upload (default)
* 1 : Upload
Postman Example


Return to example




3.10 Remote control output (including door opening)


Description : Remote control face recognition device to open the door, serial port

output, Wiegand output, custom pop-up window, voice broadcast

Method
URL
POST
http://middleware server
IP:8190/api/device/output
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
deviceKey
Device serial number
String
Y
* Device serial number, 18 digits
secret
password
String
Y
* The server password set in the communication settings on the
face device
type
Remote output interaction type
Int
Y
Device interaction type
* 1: Open the door (default)
* 2: Serial port
* 3 : Wiegand
* 4: Custom text pop-up window, custom voice broadcast


content
Remote output content
String
N
* Interaction type is set to 1: when openingthedoor,  no  setting  is required
* the interaction type is set to 2:
rseialport,    only     numbers, English and English characters are allowed in the content, and the length is limited to 255 characters.
* the Interaction Type is set to 3: Wiegand, only numbers are allowed.
* the interaction type is set to 4: custompop-upwindowand            voicebroadcast, the content is
set to JSON string
When the output type is set to 4, the remote output content JSONstring example:


Postman Example

Return to example




code code description

code
msg
100501
The value of content cannot be empty
100502
The length of content is limited to 255 characters
100503
The value of content is only allowed to be numbers
100504
The value of content is not defined by the interface. Please
refer to the interface documentation.



3.11 Setting up a wired network
Note : Set the DHCPmode of the face recognition device and other wired network configurations. The fixed IP set on this interface is suitable for network cable connection.

Method
URL
POST
http://middleware server
IP:8190/api/device/setNetwork
Header Field

Parameter name
describe
type
Must
be transm
Additional Notes





itted

Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be trans
mitted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the face
device
dhcpEnable
DHCP Mode Selection
Int
Y
DHCP Mode Selection
* 0: Manually set IP mode. Except subnetMask, all other parameters must be passed in and cannot be empty.
* 1: Dynamically obtain IP mode,
no need to pass in other parameters
ip
IP address
String
N
Set a static IP address for the face
recognition device
gateway
Gateway
String
N
Setting up the gateway
subnetMask
Subnet Mask
String
N
Setting the Subnet Mask
DNS
DNS Server
String
N
Setting up DNS servers
Postman Example




Return to example





3.12 Set the upload address

Description : Set the reporting address configuration of the face recognition device

Method
URL
POST
http://middleware server
IP:8190/api/device/setUploadUrl
Header Field

Parameter name
describe
type
Must be
trans
Additional Notes





mitte
d

Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be transmitt
ed
Additional Notes
deviceKey
Device serial number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the face
device
data
List<Object>
List<Object>
Y
Reporting address parameters
Object.type
Configure the type of the reported data URL
Int
Y
Configure the type of the reported data URL
* 0: Clear all reported addresses
* 1: Heartbeat report
* 2: Identify and report records
* 3: Registered personnel data reporting
* 4: Fingerprint registration data reporting
* 5: Anti-tampering alarm data reporting
* 6: Palmprint registration data
reporting


Object.url
Configured URL
String
N
The upload address must conform to the regular expression: ((http|https)://)((a-zA-Z0-9\._-
{2,6})|(0-9{1,3}\.0-9{1,3}))(:0-9*)?
data request parameter description:

Postman Example


Return to example


Identification record reporting request parameter description:

Parameter name
type
Additional Notes
deviceKey
String
Device serial number, 18 digits
recordId
String
Identify the unique ID of the record
personSn
String
Personnel Number
personType
Int
Person Type0: Unknown1: Employee2: Visitor3:
Blacklist
verifyStyle
Int
Verification
* 0 : Any
* 1: Face only
* 2: Card only
* 20: Card + Face
* 21: Card + Fingerprint
* 3: Fingerprint only
* 30: Fingerprint + Face
checkImgUrl
String
Snap photo LAN path
checkImgBase64
String
Snapshot photo imgBase64 string
cardNo
String
card number
idCard
String
ID number
voucherCode
String
Voucher Number
temperature
String
body temperature

attach
String
Additional Information
maskFlag
Int
Mask identification 0: No mask 1: With mask
capFlag
Int
Hat mark 0: No hat 1: With hat
faceFlag
Int
Face ID0: No1: Yes
cardFlag
Int
Card swipe identification0: No1: Yes
fingerFlag
Int
Fingerprint identification0: No1: Yes
idCardFlag
Int
ID card identification0: No1: Yes
pwdFlag
Int
Password ID0: No1: Yes
palmFlag
Int
Palmprint ID0: No1: Yes
resultFlag
Int
Identification result mark 1: Success 2: Failure 3:
Insufficient authority
strangerFlag
Int
Stranger ID0: No1: Yes
openDoorFlag
Int
Door open flag0: No1: Yes
recordTime
Long
Passage time, Unix millisecond timestamp ( 13
bits)
Identify the report request response string :



Note: If you do not respond with the correct string, the record will be uploaded repeatedly






Heartbeat reporting request parameter description:

* The device will report heartbeat information to the heartbeat address every minute.
* No information is required to be returned, and the device will not process it.

Parameter name
type
Additional Notes
deviceKey
String
Device serial number, 18 digits
time
Long
Request time, Unix millisecond timestamp ( 13
bits)
ip
String
Device IP
personCount
Int
Number of staff
faceCount
Int
Number of photos
version
String
Device version number
freeDiskSpace
String
Remaining disk space unit: M
Personnel registration report request parameter description:

* When the device creates a person, it will register the person and report the address POST person information
* No information is required to be returned, and the device will not process it.

Parameter name
type
Additional Notes
deviceKey
String
Device serial number, 18 digits
personType
Int
Personnel Type
* 1 Internal staff
* 2 Visitors
* 3 Blacklist
personSn
String
Personnel number/employment number
name
String
Personnel Name
mobile
String
Phone number
cardNo
String
card number
idCard
String
ID number
verifyPwd
String
password
voucherCode
String
Voucher Number
acGroupNumber
Int
Access control group number
verifyStyle
Int
Verification
* 0 : Any
* 1: Face only
* 2: Card only
* 20: Card + Face
* 21: Card + Fingerprint
* 3: Fingerprint only
* 30: Fingerprint + Face

expiredType
Int
Expiration Type
* 0 Never expires
* 1 Time
* 2 times
* 3 Time + Repetitions
validCount
Int
Valid times
validTimeBegin
Long
Valid time start , Unix millisecond timestamp ( 13 bits)
validTimeEnd
Long
Valid time end, Unix millisecond timestamp ( 13 bits)
acTzNumber1
Int
Passage time 1 number
acTzNumber2
Int
Passage time 2 number
acTzNumber3
Int
Passage time 3 number
updateTime
Long
Update time, Unix millisecond timestamp ( 13
bits)
createTime
Long
Creation time, Unix millisecond timestamp ( 13
bits)
Fingerprint registration report request parameter description:

* When the device creates a person, if the person has registered a fingerprint, the fingerprint information will be POSTed to the person registration reporting address
* No information is required to be returned, and the device will not process it.

Parameter name
type
Additional Notes


deviceKey
String
Device serial number, 18 digits
personSn
String
Personnel number/employment number
fingerId
String
Unique identifier of fingerprint feature value
fingerNum
Int
Finger number, which finger, 2 digits
* The first digit indicates the left and right hands, 1 for left hand and 2 for right hand
* The second finger is marked, 1 thumb 2
index finger 3 middle finger 4 ring finger 5 little finger
feature
String
Fingerprint feature value
time
Long
Registration time, Unix millisecond timestamp ( 13 bits)

3.13 APK Upgrade

Description : Set the reporting address configuration of the face recognition device

Method
URL
POST
http://middleware server
IP:8190/api/device/upgradeApk
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field


Parameter name
describe
type
Must be trans
mitted
Additional Notes
deviceKey
Device serial number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the face
device
versionCode
apk version number
Int
Y
Upgraded APK version number
channel
Channel Number
String
Y
Channel number of the device
apkUpgradeUrl
apk upgrade address
String
Y
The apk upgrade address needs to conform to the regular expression: ((http|https)://)((a-zA-Z0-9\._-
{2,6})|(0-9{1,3}\.0-9{1,3}))(:0-9*)?

3.14 Setting the time

Description : Set the time of the face recognition device

Method
URL
POST
http://middleware server IP:8190/api/device/setTime
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
String
Y
application/x-www-form-



type


urlencoded
Body Field

deviceKey
Device serial number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the face
device
timestamp
Timestamp
String
Y
13-bit timestamp
Postman Example


3.15 Setting custom configuration

Description : Set custom configuration settings for face recognition

Method
URL
POST
http://middleware server IP:8190/api/device/setCstConfig
Header Field

Parameter name
describe
type
Must be
transm
Additional Notes





itted

Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field

deviceKey
Device serial number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
json
Custom
configuration JSON
String
Y
Custom configuration JSON
json parameter example

{

// Custom Key: Custom value "delay_time_for_alarm_activation" : 5
}

Postman Example




3.18 Get the door sensor status

Description : Set the door magnetic closed state of the device connected

Method
URL
POST
http://middleware server IP:8190/api/device/setCstConfig
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field

deviceKey
Device serial number
String
Y
Device serial number, 18 digits


secret
password
String
Y
The server password set in the communication settings on the
face device
Return parameter description
{

"code": "000",

"msg": "Successful", "ts": 1705478552329,
//Door sensor status 0: Closed 1: Open -1: No status obtained "data": 1,
"success": true

}



Postman Example





4. Personnel Management Interface

4.1 Personnel Creation

Description : Create different types of users on the face recognition device

Method
URL
POST
http://middleware server
IP:8190/api/person/create
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded


Body Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
deviceKey
Device serial number
String
Y
* Device serial number, 18 digits
secret
password
String
Y
* The server password set in the
communication settings on the face device
type
Personnel Type
Int
Y
Personnel Type
* 1: Internal staff
* 2 : Visitors
* 3 : Blacklist
s
Work No.
String
Y
Personnel unique identification When	creating		a	person,	the employee		number		cannot		be repeated	with	other		employee numbers.
Only numbers and English letters
are allowed, case sensitive
name
Name
String
Y
Personnel Name
cardNo
card number
String
N
Card number, which can be left blank when creating a card. There is no length limit.
If you fill in the card number when registering a person, you can directly swipe the card with the
corresponding card number for





identification, and the screen will also display the name of the person corresponding to the card number
idCard
ID number
String
N

mobile
Phone number
String
N

verifyPwd
password
String
N
TODO
voucherCode
Voucher Number
String
N
TODO
acGroupNumber
Access control group number
Int
N
Access	control	group	number corresponding to the personnel When set to 0, it means that the person does not set the access
control permission group (default)
verifyStyle
Verification
Int
N
When the access control permission group is set to 0, this field takes effect
* 0 : Any
* 1: Face only
* 2: Card only
* 20: Card + Face
* 21: Card + Fingerprint
* 3: Fingerprint only
* 30: Fingerprint + Face

expiredType
Expiration Type
Int
N
Expiration type of personnel
* 0: Never expire (default)
* 1: Time period
* 2: Number of TODOs
* 3: Time + times TODO
validTimeBegin
Effective time starts
Long
N
The start time of the personnel's
validity	period,	Unix	millisecond timestamp ( 13 bits)
If a person is identified outside the validity period, the device will announce "Insufficient name permissions" and the device screen will display "Name +
Insufficient permissions "
validTimeEnd
Validity period ends
Long
N
The expiration date of the personnel, Unix millisecond timestamp ( 13
bits)
If a person is identified outside the validity period, the device will announce "Insufficient name permissions" and the device screen will display "Name +
Insufficient permissions "
Postman Example


Return to example







4.2 Personnel Update

Description : Update user information on the face recognition device

Method
URL
POST
http://middleware server
IP:8190/api/person/update
Header Field

Parameter name
describe
type
Must be trans
mitted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter
describe
type
Must
Additional Notes
name


be




transm




itted


deviceKey
Device serial number
String
Y
* Device serial number, 18 digits
secret
password
String
Y
* The server password set in the
communication settings on the face device
type
Personnel Type
Int
Y
Personnel Type
* 1: Internal staff
* 2 : Visitors
* 3 : Blacklist
s
Work No.
String
Y
Personnel unique identification, When updating, modify the database information in the device according to	the	work	number.	If	the corresponding work number does not exist, the modification will fail.
Only numbers and English letters
are allowed, case sensitive
name
Name
String
Y
Personnel Name
cardNo
card number
String
N
Card number, which can be left blank when creating a card. There is no length limit.
If you fill in the card number when registering a person, you can directly swipe the card with the corresponding card number for identification, and the screen will also display the name of the person
corresponding to the card number
idCard
ID number
String
N


mobile
Phone number
String
N

verifyPwd
password
String
N
TODO
voucherCode
Voucher Number
String
N
TODO
acGroupNum ber
Access control group number
Int
N
Access	control	group	number corresponding to the personnel When set to 0, it means that the person does not set the access
control permission group (default)
verifyStyle
Verification
Int
N
When the access control permission group is set to 0, this field takes effect
* 0 : Any
* 1: Face only
* 2: Card only
* 20: Card + Face
* 21: Card + Fingerprint
* 3: Fingerprint only
* 30: Fingerprint + Face
expiredType
Expiration Type
Int
N
Expiration type of personnel
* 0: Never expire (default)
* 1: Time period
* 2: Number of TODOs
* 3: Time + times TODO


validTimeBegi n
Effective time starts
Long
N
The start time of the personnel's validity	period,	Unix	millisecond
timestamp ( 13 bits)
If a person is identified outside the validity period, the device will announce "Insufficient name permissions" and the device screen will display "Name +
Insufficient permissions "
validTimeEnd
Validity period ends
Long
N
The expiration date of the personnel, Unix millisecond timestamp ( 13
bits)
If a person is identified outside the validity period, the device will announce "Insufficient name permissions" and the device screen will display "Name +
Insufficient permissions "
Postman Example


Return to example







4.3 Personnel Deletion
Description : Delete the personnel information corresponding to the employee number on the face recognition device

Method
URL
POST
http://middleware server
IP:8190/api/person/delete
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must
be transmi
Additional Notes





tted

deviceKey
Device
serial number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the face
device
s
Work No.
List<String>
Y
To delete a set of employee IDs,
separate multiple numbers with commas.



Postman Example


Return to example


4.4 Clearing out personnel

Description : Clear all user information on the device

Method
URL
POST
http://middleware server
IP:8190/api/person/empty
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be trans
mitted
Additional Notes
deviceKey
Device serial number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the face
device


Postman Example




Return to example




4.5 Person Search
Description : Find the corresponding user information according to the user's work number, ID number, and card number

Method
URL
POST
http://middleware server IP:8190/api/person/find
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field


Parameter name
describe
type
Must be trans mitte
d
Additional Notes
deviceKey
Device serial number
String
Y
* Device serial number, 18 digits
secret
password
String
Y
* The server password set in the communication settings on the
face device
type
Find Type
Int
Y
Find Type
* 1 : Work number
* 2 : ID card
* 3 : Card number
key
Correspondin g query data
String
Y

Postman Example

Return to example







4.6 Personnel List Search

Description : Query all user information by page

Method
URL
POST
http://middleware server
IP:8190/api/person/findList
Header Field

Parameter name
describe
type
Must be trans mitte
d
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be transmitte
d
Additional Notes


deviceKey
Device serial number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
index
page number
Int
Y
Page number, starting from 1
length
Query quantity
Int
N
The number of users queried per page, the default is 20
Postman Example


Return to example








4.7 Creation or update of personnel passage time period
Note : Set the access time for the corresponding user from Monday to Sunday. Users who pass outside the access time will be displayed as insufficient authority and verification failed.

Method
URL
POST
http://middleware server
IP:8190/api/person/passtime/merge
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded


Body Field

Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
s
Work No.
List<String>
Y
Employee ID collection, multiple employee IDs are separated by
commas
passtimeList
Traffic time
String
N
Passing time Json string
A maximum of 3 sets are allowed. If only 1 segment is set, the last two segments do not need to be transmitted.
Passing time string, the format is start time - end time , the time format is HH:mm, for example: 00:00-23:59,
separated by -
passtimeList Request parameter example:




Postman Example


Return to example








4.8 Deletion of personnel passage time

Description : Remove the time limit for the user's access

Method
URL
POST
http://middleware server
IP:8190/api/person/passtime/delete
Header Field

Parameter name
describe
type
Must be trans
mitted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded


Body Field

Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
s
page number
List<String>
Y
Employee ID collection, multiple IDs are separated by commas


Postman Example


Return to example


4.9 Personnel creation or update
Description : Create different types of users on the face recognition device. If the person already exists, update the person information.

Method
URL
POST
http://deviceIP:8190/api/js/person/merge
Header Field

Parameter name
describe
type
Must be trans mitte
d
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be trans
mitted
Additional Notes
deviceKey
Device serial number
String
Y
* Device serial number, 18 digits
secret
password
String
Y
* The server password set in the communication settings on the face device

type
Personnel Type
Int
Y
Personnel Type
* 1: Internal staff
* 2 : Visitors
* 3 : Blacklist
s
Work No.
String
Y
Personnel unique identification
When creating a person, the employee number cannot be repeated with other employee numbers.
Only numbers and English letters are allowed, case sensitive
If it already exists, update the personnel
information
name
Name
String
Y
Personnel Name
cardNo
card number
String
N
Card number, which can be left blank when creating a card. There is no length limit.
If you fill in the card number when registering a person, you can directly swipe the card with the corresponding card number for identification, and the screen will also display the name of the person corresponding to the card
number
idCard
ID number
String
N

mobile
Phone number
String
N


verifyPwd
password
String
N

voucherCod e
Voucher Number
String
N

acGroupNu mber
Access control group number
Int
N
Access	control	group	number corresponding to the personnel
When set to 0, it means that the person does not set the access control
permission group (default)
verifyStyle
Verification
Int
N
When the access control permission group is set to 0, this field takes effect
* 0 : Any
* 1: Face only
* 2: Card only
* 20: Card + Face
* 21: Card + Fingerprint
* 3: Fingerprint only
* 30: Fingerprint + Face
expiredType
Expiration Type
Int
N
Expiration type of personnel
* 0: Never expire (default)
* 1: Time period
* 2: Number of TODOs
* 3: Time + times TODO
validTimeBe gin
Effective time starts
Long
N
The start time of the personnel's validity period,  Unix  millisecond  timestamp
( 13 bits)
If a person is identified outside the validity period, the device will announce "Insufficient name
permissions" and the device screen






will display "Name + Insufficient permissions "
validTimeEn d
Validity period ends
Long
N
The expiration date of the personnel,
Unix millisecond timestamp ( 13 bits) If a person is identified outside the validity period, the device will announce "Insufficient name permissions" and the device screen will display "Name + Insufficient
permissions "
Postman Example


Return to example


5. Face Management Interface

5.1 Face creation or update
Description : Register the facial recognition photo of the corresponding person by work number

Method
URL
POST
http://middleware server IP:8190/api/face/merge
Header Field

Parameter name
describe
type
Must be trans
mitted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the face
device
personSn
Staff number
String
Y
Personnel number, must first register personnel
imgUrl
Work No.
String
Y
The LAN URL address of the photo Photos only support images stored in






http protocol locations
imgBase64
Name
String

* Base64 encoded string of the photo
* The photo formats supported are PNG, JPG, JPEF;
* imgBase64 needs to remove the header such as:
data:image/jpeg;base64
easy
card number
Int
N
Detect photo quality level
* 0: Strictly check the photo quality
* 1: Relaxed detection of photo quality
Request Instructions

* You must add people before adding pictures

* imgUrl and imgBase64 parameters are required
* The photo formats supported are PNG, JPG, JPEF; Postman Example


Return to example





5.2 Face Removal
Description : Delete the facial recognition photo of the corresponding person by work number

Method
URL
POST
http://middleware server IP:8190/api/face/delete
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits


secret
password
String
Y
The server password set in the communication settings on the face
device
s
Staff ID Collection
List<Stri ng>
Y
Personnel number set, multiple personnel are separated by commas
Postman Example


Return to example


5.3 Face Search
Description : Query the imgBase64 encoding of the photo of the corresponding person on the face recognition device

Method
URL
POST
http://middleware server IP:8190/api /face/find


Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be
transmitt ed
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18
digits
secret
password
String
Y
The server password set in
the communication settings on the face device
personSn
Personnel
Number
String
Y
Personnel Number
Postman Example


Return to example




Return parameter data Description:

Parameter name
type
Additional Notes
personSn
String
Personnel's work number
imgBase64
String
imgbase64 encoding of the photo
featureData
String
Face feature value



6. Record Management Interface

6.1 Search the Pass Record List
Description : Query the access records of the corresponding person on the face recognition device

Method
URL
POST
http://middleware server
IP:8190/api/record/findList
Header Field


Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be
transmit ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
startTime
The start timestamp of
the query
Long
Y
Unix millisecond timestamp (13 bits)
endTime
The end
timestamp of the query
Long
Y
Same as above
personSn
Work No.
String
N
Query the specified user ID. If
this parameter is not passed, query all users.
personType
Personnel Type
Int
N
Query the personnel type of the record
* -1 : all (default)
* 0 : Unknown
* 1 : Employee
* 2 : Visitors
* 3 : Blacklist


recordType
Find Type
Int
N
The type of record to be queried
* 0 : All (default)
* 1: Face recognition
* 2 : Swipe card
* 3: Card + Face
* 4 : Fingerprint
index
page number
Int
N
Page number, starting from 1
length
Query the
number of records
Int
N
The number of records to query each time, the default is 20
order
sort by
Int
N
sort by
* 0 : Descending (default)
* 1 : Ascending



Postman Example

Return to example




Return parameter data array description:

Parameter name
type
Additional Notes
id
Long
Record unique identifier
personSn
String
The employee number of the person taking
the photo
personName
String
Name of the person taking the photo

personType
Int
Query the personnel type of the record
* 0 : Unknown
* 1 : Employee
* 2 : Visitor
* 3 : Blacklist
checkImgUrl
String
Snap photo LAN address
cardNo
String
card number
idCard
String
ID number
temperature
String
body temperature
attach
String
Additional Information
maskFlag
Int
Wearing a mask1: Yes0: No
capFlag
Int
Wearing a helmet? 1: Yes 0: No
faceFlag
Int
Is it a face scan pass? 1: Yes 0: No
cardFlag
Int
Is it a card swipe pass? 1: Yes 0: No
fingerFlag
Int
Is fingerprint access required? 1: Yes 0:
No
idCardFlag
Int
Is it to scan the ID card to pass 1: Yes 0:
No
pwdFlag
Int
Is it to verify the password? 1: Yes 0: No
resultFlag
Int
Identification result mark 1: Success 2:
Failure 3: Insufficient authority
strangerFlag
Int
Is it a stranger? 1: Yes 0: No


openDoorFlag
Int
Open the door 1: Yes 0: No
createTime
Long
Passing time,





6.2 Deletion of Pass Records
Description : Delete the pass records of the corresponding period on the personnel identification device

Method
URL
POST
http://middleware server
IP:8190/api/record/delete
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be
transmit ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the
face device

startTime
Start
timestamp
Long
Y
Unix millisecond timestamp (13
bits)
endTime
End
timestamp
Long
Y
Same as above
personSn
Work No.
String
N
Delete the records of the specified user ID. If this parameter is not passed, all
records will be deleted.
personType
Personnel Type
Int
N
Type of person who deleted the record
* -1 : all (default)
* 0 : Unknown
* 1 : Employee
* 2 : Visitors
* 3 : Blacklist
recordType
Find Type
Int
N
Type of deleted records
* 0 : All (default)
* 1: Face recognition
* 2 : Swipe card
* 3: Card + Face
* 4 : Fingerprint
recordId
Record unique identifier
Int
N
Record unique identifier
The id in the data parameter in the response information of the corresponding access record
search interface



Postman Example




Return to example


6.3 Pass record search
Description : Query the access record of the corresponding person on the face recognition device, including the imgBase64 encoding of the captured photo

Method
URL
POST
http://middleware server IP:8190/api/record/find
Header Field

Parameter name
describe
type
Must be trans
mitted
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field


Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
recordId
Pass record id
Long
Y
The unique identifier of the record in the information returned by the access record
list search interface
Interface Description

* Compared with the return information of the access record list search interface, there is one more checkImgBase64 snapshot photo imgBase64 encoding
Postman Example


Return to example




Return parameter data array description:

Parameter name
type
Additional Notes
id
Long
Record unique identifier
personSn
String
The employee number of the person taking
the photo
personName
String
Name of the person taking the photo
personType
Int
Query the personnel type of the record
* 0 : Unknown
* 1 : Employee
* 2 : Visitors
* 3 : Blacklist
checkImgUrl
String
Snap photo LAN address
checkImgBase64
String
Imgbase64 encoding of captured photos
cardNo
String
card number

idCard
String
ID number
voucherCode
String
Voucher Number
temperature
String
body temperature
attach
String
Additional Information
maskFlag
Int
Wearing a mask1: Yes0: No
capFlag
Int
Wearing a helmet? 1: Yes 0: No
faceFlag
Int
Is it a face scan pass? 1: Yes 0: No
cardFlag
Int
Is it a card swipe pass? 1: Yes 0: No
fingerFlag
Int
Is fingerprint access required? 1: Yes 0:
No
idCardFlag
Int
Is it to scan the ID card to pass 1: Yes 0:
No
pwdFlag
Int
Is it to verify the password? 1: Yes 0: No
resultFlag
Int
Identification result mark 1: Success 2:
Failure 3: Insufficient authority
strangerFlag
Int
Is it a stranger? 1: Yes 0: No
openDoorFlag
Int
Open the door 1: Yes 0: No
createTime
Long
Passing time,




7. Time period management interface

7.1 Creating or updating a time period

Description : Create a time period on the face recognition machine

Method
URL
POST
http://middleware server
IP:8190/api/ac_timezone/merge
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field

Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
acTzNumber
Time period number
Int
Y
Time period number
* Please enter an integer between 1 and 100. If a time period with this number exists on the device, the information of the corresponding time
period will be updated.

acTzName
Time period
name
String
Y
Time period name
sunStart
Sunday start
time
String
N
* The format is HH:mm.
Default: 00:00
sunEnd
Sunday End
Time
String
N
* The format is HH:mm.
Default: 23:59
monStart
Monday start
time
String
N
The format is HH:mm. Default:
00:00
monEnd
Monday End
Time
String
N
* The format is HH:mm.
Default: 23:59
tueStart
Tuesday start
time
String
N
* The format is HH:mm.
Default: 00:00
tueEnd
Tuesday End
Time
String
N
* The format is HH:mm.
Default: 23:59
wedStart
Wednesday
start time
String
N
The format is HH:mm. Default:
00:00
wedEnd
Wednesday
End Time
String
N
The format is HH:mm. Default:
23:59
thursStart
Thursday
start time
String
N
The format is HH:mm. Default:
00:00
thursEnd
Thursday
End Time
String
N
The format is HH:mm. Default:
23:59
friStart
Friday start time
String
N
The format is HH:mm. Default: 00:00
friEnd
Friday End
Time
String
N
The format is HH:mm. Default:
23:59
satStart
Saturday
start time
String
N
The format is HH:mm. Default:
00:00
satEnd
Saturday
End Time
String
N
The format is HH:mm. Default:
23:59


Postman Example


Return to example


7.2 Time period deletion


Description : Delete the corresponding time period on the personnel identification

device

Method
URL
POST
http://middleware server
IP:8190/api/ac_timezone/delete
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field

Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
acTzNumber
Time period number
String
Y
Time period number , multiple
numbers are separated by commas


Postman Example




Return to example


7.3 Time period list query

Description : Query the access control group list information on the device

Method
URL
POST
http://middleware server IP:8190/api/ac_timezone/findList
Header Field

Parameter name
describe
type
Must be trans mitte
d
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field


Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
acTzNumber
Time period number
String
N
Time period number, multiple time period numbers are separated by commas
If this parameter is not filled, all time period information on the
device will be returned.



Postman Example


Return to example




8. Access control group management interface

8.1 Create or update access control group

Description : Create a door access control group on the facial recognition machine

Method
URL
POST
http://middleware server
IP:8190/api/ac_group/merge
Header Field

Parameter name
describe
type
Must be
transm
Additional Notes





itted

Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field

Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the
face device
acGroupNumb er
Access control group number
Int
Y
Access control group number
* Please enter an integer between 2 and 50. If there is an access control group with this number on the device, the corresponding access control group
information will be updated.
name
Access control
group name
String
Y
Access control group name
verifyStyle
Verification
Int
N
Verification
* 0 : Any
* 1: Face only
* 2: Card only
* 20: Card + Face
* 21: Card + Fingerprint
* 3: Fingerprint only
* 30: Fingerprint + Face

expiredType
Expiration Type
Int
N
Expiration Type
* 0 Never expires
* 1 Time
* 2 times
* 3 Time + Repetitions
validTimeBegin
Effective time starts
Long
N
The	start	time	of	the personnel's validity period, Unix
millisecond timestamp ( 13
bits)
If a person is identified outside the validity period, the device will announce "Insufficient name permissions" and the device screen will display "Name +
Insufficient permissions "
validTimeEnd
Validity period ends
Long
N
The	expiration	date	of	the
personnel,	Unix	millisecond timestamp ( 13 bits)
* If a person is identified outside the validity period, the device will announce "Insufficient name permissions" and the device screen will display "Name +
Insufficient permissions "
validCount
Valid times
Int
N
* TODO
acTzNumber1
Passage time 1 number
Int
N
Passage time 1 number
* Please enter an integer between 0 and 100, corresponding to the time
period number


acTzNumber2
Passage time 2
number
Int
N
Same as above
acTzNumber3
Passage time 3
number
Int
N
Same as above



Postman Example


Return to example


8.2 Access Control Group Deletion
Description : Delete the corresponding access control group on the personnel identification device.

Method
URL


POST
http://middleware server
IP:8190/api/ac_group/delete
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field

Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the
communication settings on the face device
acGroupNu mber
Access control group number
String
Y
Access control group number, multiple access control group numbers are separated by
commas


Postman Example




Return to example



8.3 Access Control Group List Query

Description : Query the access control group list information on the device

Method
URL
POST
http://middleware server IP:8190/api/ac_group/findList
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded


Body Field

Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the
face device
acGroupNumber
Access control group number
String
N
Access control group number, multiple access control group numbers are separated by commas
If this parameter is not filled, all access control group information on the device will
be returned.


Postman Example


Return to example












code code description

code
msg
101101
The value of personType is an integer between -1 and 3
101102
The value of findType is an integer greater than 0
101103
The value of startTime is in the wrong format
101104
The value of endTime is in the wrong format
101105
The value of endTime should be greater than startTime
101106
The value of recordId should be greater than 0

9. Fingerprint Management Interface

9.1 Fingerprint creation or update
Description : Register the facial recognition photo of the corresponding person by work number

Method
URL
POST
http://middleware server
IP:8190/api/finger/merge
Header Field

Parameter name
describe
type
Must be transm
itted
Additional Notes
Content-Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field

Parameter name
describe
type
Must be
transmit ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the
face device
fingerMerge
List
Fingerprint
parameters
List<Object>
Y
Fingerprint parameter array
Object.perso
nSn
Staff number
String
Y
Personnel number, must first
register personnel


Object.finger Id
Unique identifier of
feature value
String
Y
Unique identifier of feature value
Object.finger Num
Which finger? 2
Int
Y
The number of fingers, 2 digits, for example: 12 means the left thumb
* The first position indicates the left and right hands
* 1 : left hand 2 : right hand
* Second finger marker 1: thumb 2: index finger 3:
middle finger 4: ring finger 5:
pinky finger
Object.featur
e
Eigenvalue
String
Y

fingerMergeList Parameter Description:

Postman Example




Return to example


9.2 Fingerprint Deletion
Description : Delete the corresponding access control group on the personnel identification device.

Method
URL
POST
http://middleware server IP:8190/api
/finger/delete
Header Field

Parameter name
describe
type
Must
be
Additional Notes





transm
itted

Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be transmit
ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the
face device
personSn
Staff number
String
Y
Personnel unique identification
fingerId
Fingerprint ID
String
Y
Fingerprint unique identification


Postman Example


Return to example




9.3 Fingerprint Search

Description : Query the access control group list information on the device

Method
URL
POST
http://middleware server IP:8190/api/finger/find
Header Field

Parameter name
describe
type
Must be trans mitte
d
Additional Notes
Content-Type
Request
body type
String
Y
application/x-www-form-urlencoded
Body Field

Parameter name
describe
type
Must be
transmit ted
Additional Notes
deviceKey
Device serial
number
String
Y
Device serial number, 18 digits
secret
password
String
Y
The server password set in the communication settings on the
face device


personSn
Staff number
String
Y
Personnel unique identification



Postman Example


Return to example




10. Palm print/palm vein management interface

10.1 Palm print/palm vein creation or update


Description : Register the palm print/palm vein data of the corresponding person through the work number


Header Field


Parameter name


describe


type
Must be trans
mitted


Additional Notes
Content-
Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field


Parameter name


describe


type
Must be transm
itted


Additional Notes

deviceKey
Device
serial number

String

Y

Device serial number, 18 digits

secret

password

String

Y
The server password set in
the communication settings on the face device
palmMerge
List
Palm print
parameters
List<Object>
Y
Palmprint parameter array
Object.pers
onSn
Staff
number

String

Y
Personnel number, must first
register personnel
Object.fing
erId
Unique
identifier of
String
Y
Unique identifier of feature
value



feature
value



Object.fing
erNum

Which palm

Int

Y

1 left hand 2 right hand
Object.feat
ure
Eigenvalue
String
Y

palmMergeList Parameters:


// The parameter is of type List<Object> [
{

"personSn": "004", // Personnel number, must be registered first "palmId": "001", // unique identifier of the characteristic value "palmNum": 21, // 1 left hand 2 right hand
"feature": "Rk1SACAyMAAAAAF6AAAA/AFEAMUAxQEAAABm..." // feature

},

{

"personSn": "004",

"palmId": "001",

"palmNum": 22,

"feature": "Rk1SACAyMAAAAAF6AAAA/AFEAMUAxQEAAABm..."

}

]




Postman Example

Return to example


{

"code": "000", "data": [
{

"feature": "Rk1SACAyMAAAAAF6AAAA/AFEAMUAxQEAA...",

"palmId": "001",

"palmNum": 21,

"personSn": "004"

}

],

"msg": "Request successful"

}

10.2 Palm print/palm vein deletion

Description : Delete the palm print/palm vein data of the corresponding person by work number


Header Field


Parameter name


describe


type
Must be trans
mitted


Additional Notes
Content-
Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field


Parameter name


describe


type
Must be transm
itted


Additional Notes

deviceKey
Device serial
number

String

Y

Device serial number, 18 digits

secret

password

String

Y
The server password set in
the communication settings on the face device
personSn
Staff
number
String
Y
Personnel unique
identification

palmId
Palmprint
ID

String

Y
Palm print unique
identification




Postman Example

Return to example


{

"code": "000", "data": { "palmId": "001",
"personSn": "003"

},

"msg": "Request successful"

}



10.3 Palm print/palm vein search

Description : Search for the palm print/palm vein data of the corresponding person by work number


Method
URL
POST
http://middleware server IP:8190/api /palm/find
Header Field


Parameter name


describe


type
Must be trans
mitted


Additional Notes
Content-
Type
Request body
type
String
Y
application/x-www-form-
urlencoded
Body Field


Parameter name


describe


type
Must be transm
itted


Additional Notes

deviceKey
Device
serial number

String

Y

Device serial number, 18 digits

secret

password

String

Y
The server password set in
the communication settings on the face device
personSn
Staff
number
String
Y
Personnel unique
identification


Postman Example




Return to example


{

"code": "000", "data": [
{

"createTime": 1879731214951, // creation time "feature":
"Rk1SACAyMAAAAAF6AAAA/AFEAMUAxQEAAABmC0CGALX+XUARAHQWXUDd

AF...", // feature value

"palmId": "a194ef00d89643eb9500e31b7f2aade6", // Palm print/palm vein unique identifier
"palmNum": 1, // 1 left hand 2 right hand "personSn": "004" // Personnel number
}

],

"msg": "Request successful"


}



Schedule

1.	code code description

code
msg
-1
The device is busy, please try again later
000
Request Success
401
Illegal request, authentication failed
1000
Request parameter error
1001
The value of length is an integer between 0 and 1000.


1.	Supported languages/time zones








































































































































































































































































































































































































































































































































































