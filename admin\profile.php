<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once('includes/script.php');  
require_once('session/Login.php'); 

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    header("location:index.php?utm_campaign=expired");
}

$model = new Dashboard();
$password = $_SESSION['official_password'];
$username = $_SESSION['official_username'];
$uid = $_SESSION['official_id'];

$connection = $model->TemporaryConnection();

$query = $model->GetAdministrator($username, $password);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];
$generate = '';
$stat = '';

// Function to fetch employees from API
function fetchApiEmployees($page = 1, $per_page = 50) {
    // Ensure per_page is at least 1 to avoid division by zero
    $per_page = max(1, (int)$per_page);
    $page = max(1, (int)$page);

    $apiUrl = "http://196.189.151.125:8080/api/HRMAPI/get_employee?page=$page&pageSize=$per_page";
    
    $response = false;
    // Check if cURL is available
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); // Add timeout to cURL as well
        $response = curl_exec($ch);
        
        if(curl_errno($ch)) {
            error_log("cURL Error for $apiUrl: " . curl_error($ch));
            $response = false; // Indicate failure
        }
        curl_close($ch);
    } else {
        // Fallback to file_get_contents if cURL is not available
        error_log("cURL not available, attempting file_get_contents for $apiUrl");
        $context = stream_context_create([
            'http' => [
                'ignore_errors' => true,
                'timeout' => 30
            ]
        ]);
        
        $response = @file_get_contents($apiUrl, false, $context);
        if ($response === false) {
            error_log("Error fetching API data using file_get_contents for $apiUrl");
            $response = false; // Indicate failure
        }
    }

    error_log("Raw API response from $apiUrl: " . ($response === false ? 'Failed to get response' : $response));

    $data = [];
    $total = 0;
    $current_page = $page;
    $total_pages = 1; // Default to 1 page for empty data

    if ($response !== false) {
        $decoded_data = json_decode($response, true);
        error_log("Decoded API data from $apiUrl: " . print_r($decoded_data, true));
        if ($decoded_data && isset($decoded_data['data']) && is_array($decoded_data['data'])) {
            $data = $decoded_data['data'];
            $total = $decoded_data['total'] ?? 0;
            $current_page = $decoded_data['page'] ?? $page;
            $per_page_from_api = $decoded_data['pageSize'] ?? $per_page;
            
            // Ensure per_page_from_api is at least 1 for calculation
            $per_page_from_api = max(1, (int)$per_page_from_api);

            $total_pages = ($total > 0) ? ceil($total / $per_page_from_api) : 1;
            
        } else {
            error_log("Invalid API response format or missing 'data' key from $apiUrl: " . ($response ?: 'Empty Response'));
        }
    } else {
        error_log("Failed to get response from API for URL: " . $apiUrl);
    }
    
    return [
        'data' => $data,
        'total' => $total,
        'current_page' => $current_page,
        'per_page' => $per_page, // Use the requested per_page, or the default
        'total_pages' => $total_pages
    ];
}

// Handle preview request
$preview_data = null;
if(isset($_GET['preview'])) {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 50;
    $preview_data = fetchApiEmployees($page, $per_page);
}

// Get employee data from API with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 50;
$employee_data = fetchApiEmployees($page, $per_page);

if(isset($_GET['edit'])){
    $generate = $_GET['edit'];
}

if($generate == '1' ){
    $stat = '<div class="alert alert-success alert-dismissible">
    <button type="button" class="close" data-dismiss="alert"></button>
    Employee profile successfully edited.
    </div>';
} else { }        
?>
<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <title>Profiling and Payroll Management System</title>
  </head>
  <body class="" v-on:click="Reload">
    <div class="page" id="app">
      <div class="page-main">
        <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
          <div class="container">
            <div class="row align-items-center">
              <div class="d-flex">
                <?php require_once('includes/header.php') ?>
              </div>
              <div class="col-lg order-lg-first">
                <?php require_once('includes/subheader.php') ?>
              </div>
            </div>
          </div>
        </div>
        <div class="my-3 my-md-5">
          <div class="container">
            <?php echo $stat ?>
            <div class="page-header">
              <h1 class="page-title">
                Profiling
              </h1>
            </div>
            <div class="row row-cards">           
              <div style="padding-left: 12px; padding-bottom: 25px;">
                <button type="button" class="btn btn-secondary" data-toggle="modal" data-target="#modal-add-employee">
                   <i class="fe fe-plus mr-2"></i> Add Employee
                </button>
                <a href="?preview=1" class="btn btn-info">
                    <i class="fe fe-eye mr-2"></i> Preview API Data
                </a>
                <form method="POST" style="display: inline;">
                    <button type="submit" name="sync_employees" class="btn btn-primary">
                        <i class="fe fe-refresh-cw mr-2"></i> Sync with API
                    </button>
                </form>
                <form method="POST" style="display: inline;" onsubmit="return confirm('This will delete all employees not found in the API. Are you sure?')">
                    <button type="submit" name="delete_non_api" class="btn btn-danger">
                        <i class="fe fe-trash-2 mr-2"></i> Delete Non-API Employees
                    </button>
                </form>
              </div>    

              <?php if($preview_data): ?>
              <div class="col-12">
                <div class="card">
                  <div class="card-header py-3">
                    <h3 class="card-title">API Data Preview (Total: <?php echo $preview_data['total']; ?> records)</h3>
                    <div class="card-options">
                      <form method="GET" class="d-inline-block">
                        <input type="hidden" name="preview" value="1">
                        <input type="hidden" name="page" value="1">
                        <select name="per_page" class="form-control" onchange="this.form.submit()">
                          <option value="10" <?php echo ($preview_data['per_page'] ?? 0) == 10 ? 'selected' : ''; ?>>10 per page</option>
                          <option value="25" <?php echo ($preview_data['per_page'] ?? 0) == 25 ? 'selected' : ''; ?>>25 per page</option>
                          <option value="50" <?php echo ($preview_data['per_page'] ?? 0) == 50 ? 'selected' : ''; ?>>50 per page</option>
                          <option value="100" <?php echo ($preview_data['per_page'] ?? 0) == 100 ? 'selected' : ''; ?>>100 per page</option>
                          <option value="250" <?php echo ($preview_data['per_page'] ?? 0) == 250 ? 'selected' : ''; ?>>250 per page</option>
                          <option value="500" <?php echo ($preview_data['per_page'] ?? 0) == 500 ? 'selected' : ''; ?>>500 per page</option>
                        </select>
                      </form>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-hovered" width="100%" cellspacing="0">
                        <thead>
                          <tr>
                            <th>Employee ID</th>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Department</th>
                            <th>Business Unit</th>
                          </tr>
                        </thead>
                        <tbody>
                          <?php foreach($preview_data['data'] as $employee): ?>
                          <tr>
                            <td><?php echo htmlspecialchars($employee['employee_id'] ?? ''); ?></td>
                            <td>
                              <?php 
                                echo htmlspecialchars(($employee['first_name'] ?? '') . ' ' . 
                                    ($employee['middle_name'] ?? '') . ' ' . 
                                    ($employee['last_name'] ?? ''));
                              ?>
                            </td>
                            <td><?php echo htmlspecialchars($employee['position_id'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($employee['department_name'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($employee['businessUnit_name'] ?? ''); ?></td>
                          </tr>
                          <?php endforeach; ?>
                        </tbody>
                      </table>
                    </div>
                    <?php if(($preview_data['total_pages'] ?? 0) > 1): ?>
                    <div class="mt-3">
                      <nav>
                        <ul class="pagination justify-content-center">
                          <?php if(($preview_data['current_page'] ?? 0) > 1): ?>
                          <li class="page-item">
                            <a class="page-link" href="?preview=1&page=1&per_page=<?php echo ($preview_data['per_page'] ?? 50); ?>">First</a>
                          </li>
                          <li class="page-item">
                            <a class="page-link" href="?preview=1&page=<?php echo ($preview_data['current_page'] ?? 1) - 1; ?>&per_page=<?php echo ($preview_data['per_page'] ?? 50); ?>">Previous</a>
                          </li>
                          <?php endif; ?>
                          
                          <?php
                          $start_page = max(1, ($preview_data['current_page'] ?? 1) - 2);
                          $end_page = min(($preview_data['total_pages'] ?? 1), $start_page + 4);
                          if(($end_page - $start_page) < 4) {
                              $start_page = max(1, ($end_page ?? 1) - 4);
                          }
                          ?>
                          
                          <?php for($i = $start_page; $i <= $end_page; $i++): ?>
                          <li class="page-item <?php echo $i == ($preview_data['current_page'] ?? 0) ? 'active' : ''; ?>">
                            <a class="page-link" href="?preview=1&page=<?php echo $i; ?>&per_page=<?php echo ($preview_data['per_page'] ?? 50); ?>"><?php echo $i; ?></a>
                          </li>
                          <?php endfor; ?>
                          
                          <?php if(($preview_data['current_page'] ?? 0) < ($preview_data['total_pages'] ?? 0)): ?>
                          <li class="page-item">
                            <a class="page-link" href="?preview=1&page=<?php echo ($preview_data['current_page'] ?? 1) + 1; ?>&per_page=<?php echo ($preview_data['per_page'] ?? 50); ?>">Next</a>
                          </li>
                          <li class="page-item">
                            <a class="page-link" href="?preview=1&page=<?php echo ($preview_data['total_pages'] ?? 1); ?>&per_page=<?php echo ($preview_data['per_page'] ?? 50); ?>">Last</a>
                          </li>
                          <?php endif; ?>
                        </ul>
                      </nav>
                      <div class="text-center mt-2">
                        Showing <?php echo ((($preview_data['current_page'] ?? 1) - 1) * ($preview_data['per_page'] ?? 50)) + 1; ?> to 
                        <?php echo min((($preview_data['current_page'] ?? 1) * ($preview_data['per_page'] ?? 50)), ($preview_data['total'] ?? 0)); ?> 
                        of <?php echo ($preview_data['total'] ?? 0); ?> entries
                      </div>
                    </div>
                    <?php endif; ?>
                  </div>
                </div>
              </div>
              <?php endif; ?>
                                    
              <div class="col-12">
                <div class="card">
                  <div class="card-header py-3">
                    <h3 class="card-title">Employee Profiling (API Data)</h3>
                  </div>
                  <?php require_once('modals/modal_add_employee.php') ?>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-hovered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                          <tr>
                            <th class="w-1" >ID</th>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Address</th>
                            <th>Schedule</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <?php
                                // Fetch API data directly for display
                                $apiData = fetchApiEmployees(1, PHP_INT_MAX)['data'];

                                if(!empty($apiData)) {
                                    foreach($apiData as $employee) {
                                        $employee_id = htmlspecialchars($employee['employee_id'] ?? '');
                                        $fullname = htmlspecialchars(trim(($employee['first_name'] ?? '') . ' ' .
                                                    ($employee['middle_name'] ?? '') . ' ' .
                                                    ($employee['last_name'] ?? '')));
                                        $position = htmlspecialchars($employee['position_name'] ?? $employee['position_id'] ?? '');
                                        $department = htmlspecialchars($employee['department_name'] ?? '');

                                        // Check if employee exists in local database for view/edit links
                                        $localCheckQuery = "SELECT id FROM employees WHERE employee_id = '" . mysqli_real_escape_string($connection, $employee_id) . "'";
                                        $localCheckResult = mysqli_query($connection, $localCheckQuery);
                                        $isInLocal = mysqli_num_rows($localCheckResult) > 0;
                           ?>
                          <tr>
                            <td><a ><?php echo $employee_id ?></a></td>
                            <td><a class="text-inherit"><?php echo $fullname ?></a></td>
                            <td>
                              <?php echo $position ?>
                            </td>
                            <td>
                              <?php echo $department ?>
                            </td>
                            <td>
                              <em>API Data - No Schedule</em>
                            </td>
                            <td >
                              <?php if($isInLocal): ?>
                                <a href="view.php?id=<?php echo $employee_id ?>"><button class="btn btn-success btn-sm">View</button></a>
                                <a href="edit.php?id=<?php echo $employee_id ?>"><button class="btn btn-primary btn-sm">Edit</button></a>
                              <?php else: ?>
                                <button class="btn btn-secondary btn-sm" disabled>Not Synced</button>
                              <?php endif; ?>
                            </td>
                          </tr>
                          <?php
                                    }
                                } else {
                                    echo '<tr><td colspan="6" class="text-center">No API data available</td></tr>';
                                }
                          ?>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div> 
            </div>           
          </div>
        </div>
      </div>  
    <?php require_once('includes/footer.php') ?>
    </div>   

    <?php require_once('includes/datatables.php') ?>
  </body>
</html>