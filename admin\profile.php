<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once('includes/script.php');  
require_once('session/Login.php'); 

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    header("location:index.php?utm_campaign=expired");
}

$model = new Dashboard();
$password = $_SESSION['official_password'];
$username = $_SESSION['official_username'];
$uid = $_SESSION['official_id'];

$connection = $model->TemporaryConnection();

$query = $model->GetAdministrator($username, $password);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];
$generate = '';
$stat = '';

// Function to fetch employees from API
function fetchApiEmployees($page = 1, $per_page = 50) {
    // Ensure per_page is at least 1 to avoid division by zero
    $per_page = max(1, (int)$per_page);
    $page = max(1, (int)$page);

    $apiUrl = "http://196.189.151.125:8080/api/HRMAPI/get_employee?page=$page&pageSize=$per_page";
    
    $response = false;
    // Check if cURL is available
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); // Add timeout to cURL as well
        $response = curl_exec($ch);
        
        if(curl_errno($ch)) {
            error_log("cURL Error for $apiUrl: " . curl_error($ch));
            $response = false; // Indicate failure
        }
        curl_close($ch);
    } else {
        // Fallback to file_get_contents if cURL is not available
        error_log("cURL not available, attempting file_get_contents for $apiUrl");
        $context = stream_context_create([
            'http' => [
                'ignore_errors' => true,
                'timeout' => 30
            ]
        ]);
        
        $response = @file_get_contents($apiUrl, false, $context);
        if ($response === false) {
            error_log("Error fetching API data using file_get_contents for $apiUrl");
            $response = false; // Indicate failure
        }
    }

    error_log("Raw API response from $apiUrl: " . ($response === false ? 'Failed to get response' : $response));

    $data = [];
    $total = 0;
    $current_page = $page;
    $total_pages = 1; // Default to 1 page for empty data

    if ($response !== false) {
        $decoded_data = json_decode($response, true);
        error_log("Decoded API data from $apiUrl: " . print_r($decoded_data, true));
        if ($decoded_data && isset($decoded_data['data']) && is_array($decoded_data['data'])) {
            $data = $decoded_data['data'];
            $total = $decoded_data['total'] ?? 0;
            $current_page = $decoded_data['page'] ?? $page;
            $per_page_from_api = $decoded_data['pageSize'] ?? $per_page;
            
            // Ensure per_page_from_api is at least 1 for calculation
            $per_page_from_api = max(1, (int)$per_page_from_api);

            $total_pages = ($total > 0) ? ceil($total / $per_page_from_api) : 1;
            
        } else {
            error_log("Invalid API response format or missing 'data' key from $apiUrl: " . ($response ?: 'Empty Response'));
        }
    } else {
        error_log("Failed to get response from API for URL: " . $apiUrl);
    }
    
    return [
        'data' => $data,
        'total' => $total,
        'current_page' => $current_page,
        'per_page' => $per_page, // Use the requested per_page, or the default
        'total_pages' => $total_pages
    ];
}

// Handle sync operation to populate local database for viewing
if(isset($_POST['sync_employees'])) {
    $apiEmployees = fetchApiEmployees(1, PHP_INT_MAX)['data'];

    if(!empty($apiEmployees)) {
        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;

        // First pass: Create departments
        foreach($apiEmployees as $employee) {
            if(isset($employee['department_id']) && isset($employee['department_name'])) {
                $dept_id = (int)$employee['department_id'];
                $dept_name = mysqli_real_escape_string($connection, $employee['department_name']);

                // Check if department already exists
                $checkDeptQuery = "SELECT id FROM departments WHERE id = $dept_id";
                $deptResult = mysqli_query($connection, $checkDeptQuery);

                if(mysqli_num_rows($deptResult) == 0) {
                    // Create department
                    $insertDeptQuery = "INSERT INTO departments (id, name) VALUES ($dept_id, '$dept_name')";
                    if(!mysqli_query($connection, $insertDeptQuery)) {
                        error_log("Error creating department: " . mysqli_error($connection));
                    }
                }
            }
        }

        // Second pass: Sync employees
        foreach($apiEmployees as $employee) {
            if(!isset($employee['employee_id']) || !isset($employee['first_name'])) {
                $skippedCount++;
                continue;
            }

            $employee_id = mysqli_real_escape_string($connection, $employee['employee_id']);
            $checkQuery = "SELECT id FROM employees WHERE employee_id = '$employee_id'";
            $result = mysqli_query($connection, $checkQuery);

            $first_name = mysqli_real_escape_string($connection, $employee['first_name']);
            $middle_name = isset($employee['middle_name']) ? mysqli_real_escape_string($connection, $employee['middle_name']) : '';
            $last_name = isset($employee['last_name']) ? mysqli_real_escape_string($connection, $employee['last_name']) : '';
            $position_id = isset($employee['position_id']) ? (int)$employee['position_id'] : 0;
            $department_id = isset($employee['department_id']) ? (int)$employee['department_id'] : 'NULL';
            $fullname = trim($first_name . ' ' . $middle_name . ' ' . $last_name);

            if(mysqli_num_rows($result) == 0) {
                // Insert new employee
                $insertQuery = "INSERT INTO employees (employee_id, fullname, position_id, department_id, created_on)
                               VALUES ('$employee_id', '$fullname', $position_id, $department_id, NOW())";

                if(!mysqli_query($connection, $insertQuery)) {
                    $errorCount++;
                } else {
                    $successCount++;
                }
            } else {
                // Update existing employee
                $updateQuery = "UPDATE employees SET fullname = '$fullname', position_id = $position_id, department_id = $department_id WHERE employee_id = '$employee_id'";
                if(mysqli_query($connection, $updateQuery)) {
                    $skippedCount++;
                } else {
                    $errorCount++;
                }
            }
        }

        if($errorCount == 0) {
            $stat = '<div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert"></button>
                    Sync completed! Added: ' . $successCount . ', Updated: ' . $skippedCount . ', Errors: ' . $errorCount . '
                    </div>';
        } else {
            $stat = '<div class="alert alert-warning alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert"></button>
                    Sync completed with errors. Added: ' . $successCount . ', Updated: ' . $skippedCount . ', Errors: ' . $errorCount . '
                    </div>';
        }
    } else {
        $stat = '<div class="alert alert-warning alert-dismissible">
                <button type="button" class="close" data-dismiss="alert"></button>
                No employees found in ERP API response.
                </div>';
    }
}

// Get search parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 50;

// Function to get all employees and filter them
function getFilteredEmployees($search = '', $page = 1, $per_page = 50) {
    // Get all employees from API
    $all_employees_data = fetchApiEmployees(1, PHP_INT_MAX);
    $all_employees = $all_employees_data['data'];

    // Filter employees based on search
    $filtered_employees = array();
    if (!empty($search)) {
        foreach ($all_employees as $employee) {
            $employee_id = $employee['employee_id'] ?? '';
            $fullname = trim(($employee['first_name'] ?? '') . ' ' .
                        ($employee['middle_name'] ?? '') . ' ' .
                        ($employee['last_name'] ?? ''));

            // Search in employee ID and full name (case insensitive)
            if (stripos($employee_id, $search) !== false || stripos($fullname, $search) !== false) {
                $filtered_employees[] = $employee;
            }
        }
    } else {
        $filtered_employees = $all_employees;
    }

    // Calculate pagination
    $total = count($filtered_employees);
    $total_pages = ceil($total / $per_page);
    $offset = ($page - 1) * $per_page;
    $current_page_data = array_slice($filtered_employees, $offset, $per_page);

    return array(
        'data' => $current_page_data,
        'total' => $total,
        'current_page' => $page,
        'per_page' => $per_page,
        'total_pages' => $total_pages
    );
}

// Get employee data with search and pagination
$employee_data = getFilteredEmployees($search, $page, $per_page);

if(isset($_GET['edit'])){
    $generate = $_GET['edit'];
}

if($generate == '1' ){
    $stat = '<div class="alert alert-success alert-dismissible">
    <button type="button" class="close" data-dismiss="alert"></button>
    Employee profile successfully edited.
    </div>';
} else { }        
?>
<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <title>Profiling and Payroll Management System</title>
  </head>
  <body class="" v-on:click="Reload">
    <div class="page" id="app">
      <div class="page-main">
        <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
          <div class="container">
            <div class="row align-items-center">
              <div class="d-flex">
                <?php require_once('includes/header.php') ?>
              </div>
              <div class="col-lg order-lg-first">
                <?php require_once('includes/subheader.php') ?>
              </div>
            </div>
          </div>
        </div>
        <div class="my-3 my-md-5">
          <div class="container">
            <?php echo $stat ?>
            <div class="page-header">
              <h1 class="page-title">
                Profiling
              </h1>
            </div>
            <div class="row row-cards">           
              <div class="row mb-4">
                <div class="col-md-8">
                  <div class="alert alert-primary border-0 bg-primary-light">
                    <div class="d-flex align-items-center">
                      <i class="fe fe-database text-primary mr-3" style="font-size: 1.2em;"></i>
                      <div>
                        <strong class="text-primary">ERP Integration Active</strong>
                        <p class="mb-0 text-muted small">Real-time employee data from Enterprise Resource Planning system</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4 text-right">
                  <form method="POST" style="display: inline;">
                      <button type="submit" name="sync_employees" class="btn btn-outline-primary">
                          <i class="fe fe-refresh-cw mr-2"></i> Sync ERP Data
                      </button>
                  </form>
                </div>
              </div>


                                    
              <div class="col-12">
                <div class="card">
                  <div class="card-header border-bottom-0">
                    <div class="row align-items-center">
                      <div class="col">
                        <h3 class="card-title mb-1">Employee Directory</h3>
                        <p class="text-muted mb-0 small">
                          <i class="fe fe-users mr-1"></i>
                          <?php echo number_format($employee_data['total']); ?> employees from ERP system
                          <?php if (!empty($search)): ?>
                            <span class="mx-2">•</span>
                            <i class="fe fe-search mr-1"></i>
                            Filtered by "<?php echo htmlspecialchars($search); ?>"
                          <?php endif; ?>
                        </p>
                      </div>
                      <div class="col-auto">
                        <form method="GET" class="d-inline-block">
                          <?php if (!empty($search)): ?>
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                          <?php endif; ?>
                          <select name="per_page" class="form-control form-control-sm" onchange="this.form.submit()">
                            <option value="10" <?php echo ($employee_data['per_page'] ?? 0) == 10 ? 'selected' : ''; ?>>10 per page</option>
                            <option value="25" <?php echo ($employee_data['per_page'] ?? 0) == 25 ? 'selected' : ''; ?>>25 per page</option>
                            <option value="50" <?php echo ($employee_data['per_page'] ?? 0) == 50 ? 'selected' : ''; ?>>50 per page</option>
                            <option value="100" <?php echo ($employee_data['per_page'] ?? 0) == 100 ? 'selected' : ''; ?>>100 per page</option>
                            <option value="250" <?php echo ($employee_data['per_page'] ?? 0) == 250 ? 'selected' : ''; ?>>250 per page</option>
                          </select>
                        </form>
                      </div>
                    </div>
                  </div>

                  <!-- Search Section -->
                  <div class="card-body border-bottom py-3 bg-light">
                    <div class="row align-items-center">
                      <div class="col-md-8">
                        <form method="GET">
                          <div class="input-group">
                            <div class="input-group-prepend">
                              <span class="input-group-text bg-white border-right-0">
                                <i class="fe fe-search text-muted"></i>
                              </span>
                            </div>
                            <input type="text" name="search" class="form-control border-left-0 pl-0"
                                   placeholder="Search by Employee ID or Name..."
                                   value="<?php echo htmlspecialchars($search); ?>">
                            <input type="hidden" name="per_page" value="<?php echo $per_page; ?>">
                            <div class="input-group-append">
                              <button type="submit" class="btn btn-primary">Search</button>
                            </div>
                          </div>
                        </form>
                      </div>
                      <div class="col-md-4 text-right">
                        <?php if (!empty($search)): ?>
                          <a href="?" class="btn btn-outline-secondary btn-sm">
                            <i class="fe fe-x mr-1"></i> Clear Filter
                          </a>
                        <?php endif; ?>
                      </div>
                    </div>
                  </div>

                  <div class="card-body p-0">
                    <div class="table-responsive">
                      <table class="table table-vcenter table-striped">
                        <thead class="bg-light">
                          <tr>
                            <th class="text-muted font-weight-medium">Employee ID</th>
                            <th class="text-muted font-weight-medium">Full Name</th>
                            <th class="text-muted font-weight-medium">Position</th>
                            <th class="text-muted font-weight-medium">Department</th>
                            <th class="text-muted font-weight-medium">Business Unit</th>
                          </tr>
                        </thead>
                        <tbody>
                          <?php
                                if(!empty($employee_data['data'])) {
                                    foreach($employee_data['data'] as $employee) {
                                        $employee_id = htmlspecialchars($employee['employee_id'] ?? '');
                                        $fullname = htmlspecialchars(trim(($employee['first_name'] ?? '') . ' ' .
                                                    ($employee['middle_name'] ?? '') . ' ' .
                                                    ($employee['last_name'] ?? '')));
                                        $position = htmlspecialchars($employee['position_name'] ?? $employee['position_id'] ?? '');
                                        $department = htmlspecialchars($employee['department_name'] ?? '');
                                        $business_unit = htmlspecialchars($employee['businessUnit_name'] ?? '');
                           ?>
                          <tr>
                            <td class="font-weight-medium text-primary"><?php echo $employee_id ?></td>
                            <td class="font-weight-medium"><?php echo $fullname ?></td>
                            <td class="text-muted"><?php echo $position ?: '<em>Not specified</em>' ?></td>
                            <td class="text-muted"><?php echo $department ?: '<em>Not specified</em>' ?></td>
                            <td class="text-muted"><?php echo $business_unit ?: '<em>Not specified</em>' ?></td>
                          </tr>
                          <?php
                                    }
                                } else {
                                    echo '<tr><td colspan="5" class="text-center py-5 text-muted">
                                            <i class="fe fe-users mb-2" style="font-size: 2rem;"></i><br>
                                            No employee data available from ERP system
                                          </td></tr>';
                                }
                          ?>
                        </tbody>
                      </table>
                    </div>
                    <?php if(($employee_data['total_pages'] ?? 0) > 1): ?>
                    <div class="card-footer bg-light border-top">
                      <div class="row align-items-center">
                        <div class="col">
                          <p class="text-muted mb-0">
                            <?php if (!empty($search)): ?>
                              Showing <?php echo count($employee_data['data']); ?> search results
                            <?php else: ?>
                              Showing <?php echo ((($employee_data['current_page'] ?? 1) - 1) * ($employee_data['per_page'] ?? 50)) + 1; ?> to
                              <?php echo min((($employee_data['current_page'] ?? 1) * ($employee_data['per_page'] ?? 50)), ($employee_data['total'] ?? 0)); ?>
                              of <?php echo number_format($employee_data['total'] ?? 0); ?> entries
                            <?php endif; ?>
                          </p>
                        </div>
                        <div class="col-auto">
                          <nav>
                            <ul class="pagination pagination-sm mb-0">
                          <?php
                          // Build query string for pagination links
                          $query_params = array();
                          if (!empty($search)) $query_params['search'] = $search;
                          $query_params['per_page'] = $per_page;

                          function buildPaginationUrl($page, $params) {
                              $params['page'] = $page;
                              return '?' . http_build_query($params);
                          }
                          ?>

                          <?php if(($employee_data['current_page'] ?? 0) > 1): ?>
                          <li class="page-item">
                            <a class="page-link" href="<?php echo buildPaginationUrl(1, $query_params); ?>">First</a>
                          </li>
                          <li class="page-item">
                            <a class="page-link" href="<?php echo buildPaginationUrl(($employee_data['current_page'] ?? 1) - 1, $query_params); ?>">Previous</a>
                          </li>
                          <?php endif; ?>

                          <?php
                          $start_page = max(1, ($employee_data['current_page'] ?? 1) - 2);
                          $end_page = min(($employee_data['total_pages'] ?? 1), $start_page + 4);
                          if(($end_page - $start_page) < 4) {
                              $start_page = max(1, ($end_page ?? 1) - 4);
                          }
                          ?>

                          <?php for($i = $start_page; $i <= $end_page; $i++): ?>
                          <li class="page-item <?php echo $i == ($employee_data['current_page'] ?? 0) ? 'active' : ''; ?>">
                            <a class="page-link" href="<?php echo buildPaginationUrl($i, $query_params); ?>"><?php echo $i; ?></a>
                          </li>
                          <?php endfor; ?>

                          <?php if(($employee_data['current_page'] ?? 0) < ($employee_data['total_pages'] ?? 0)): ?>
                          <li class="page-item">
                            <a class="page-link" href="<?php echo buildPaginationUrl(($employee_data['current_page'] ?? 1) + 1, $query_params); ?>">Next</a>
                          </li>
                          <li class="page-item">
                            <a class="page-link" href="<?php echo buildPaginationUrl(($employee_data['total_pages'] ?? 1), $query_params); ?>">Last</a>
                          </li>
                          <?php endif; ?>
                            </ul>
                          </nav>
                        </div>
                      </div>
                    </div>
                    <?php else: ?>
                    <div class="card-footer bg-light border-top">
                      <p class="text-muted mb-0 text-center">
                        <?php if (!empty($search)): ?>
                          Showing <?php echo count($employee_data['data']); ?> search results
                        <?php else: ?>
                          Showing all <?php echo number_format($employee_data['total'] ?? 0); ?> employees
                        <?php endif; ?>
                      </p>
                    </div>
                    <?php endif; ?>
                  </div>
                </div>
              </div> 
            </div>           
          </div>
        </div>
      </div>  
    <?php require_once('includes/footer.php') ?>
    </div>   

    <?php require_once('includes/datatables.php') ?>
  </body>
</html>