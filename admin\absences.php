<?php 
require_once('includes/script.php');  
require_once('session/Login.php'); 

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
   header("location:index.php?utm_campaign=expired");
}

$model = new Dashboard();
$password = $_SESSION['official_password'];
$username = $_SESSION['official_username'];
$uid = $_SESSION['official_id'];

$connection = $model->TemporaryConnection();

$query = $model->GetAdministrator($username, $password);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];

// Status messages
$stat = '';
if(isset($_GET['status'])){
  $status = $_GET['status'];
  if($status == '1'){
    $stat = '<div class="alert alert-success alert-dismissible">
    <button type="button" class="close" data-dismiss="alert"></button>
    Absence record updated successfully.
    </div>';
  } else if($status == '2'){
    $stat = '<div class="alert alert-success alert-dismissible">
    <button type="button" class="close" data-dismiss="alert"></button>
    Absence record deleted successfully.
    </div>';
  }
}

// Handle manual absence recording
if(isset($_POST['record_absence'])){
  $employee_id = $_POST['employee_id'];
  $date = $_POST['absence_year'].'-'.$_POST['absence_month'].'-'.$_POST['absence_day'];
  $reason = $_POST['reason'];
  $type = $_POST['type'];
  $status = $_POST['status'];
  
  // Check if record already exists
  $checkQuery = "SELECT id FROM absences WHERE employee_id = '$employee_id' AND date = '$date'";
  $checkResult = mysqli_query($connection, $checkQuery);
  
  if(mysqli_num_rows($checkResult) > 0){
    // Update existing record
    $absenceId = mysqli_fetch_assoc($checkResult)['id'];
    $updateQuery = "UPDATE absences SET reason = '$reason', type = '$type', status = '$status' WHERE id = '$absenceId'";
    mysqli_query($connection, $updateQuery);
  } else {
    // Insert new record
    $insertQuery = "INSERT INTO absences (employee_id, date, reason, type, status) 
                   VALUES ('$employee_id', '$date', '$reason', '$type', '$status')";
    mysqli_query($connection, $insertQuery);
  }
  
  echo "<script>window.location.href='absences.php?status=1'</script>";
}

// Get filter parameters
$from = isset($_GET['from']) ? $_GET['from'] : date('Y-m-01');
$to = isset($_GET['to']) ? $_GET['to'] : date('Y-m-t');
$employee_filter = isset($_GET['employee']) ? $_GET['employee'] : '';
$type_filter = isset($_GET['type']) ? $_GET['type'] : '';

// Build query with filters
$absenceQuery = "SELECT a.*, e.employee_id as emp_id, e.fullname 
                FROM absences a 
                LEFT JOIN employees e ON e.id = a.employee_id 
                WHERE a.date BETWEEN '$from' AND '$to'";

if(!empty($employee_filter)){
  $absenceQuery .= " AND a.employee_id = '$employee_filter'";
}

if(!empty($type_filter)){
  $absenceQuery .= " AND a.type = '$type_filter'";
}

$absenceQuery .= " ORDER BY a.date DESC";
$absenceResult = mysqli_query($connection, $absenceQuery);

// Get all employees for dropdown
$employeeQuery = "SELECT id, employee_id, fullname FROM employees ORDER BY fullname";
$employeeResult = mysqli_query($connection, $employeeQuery);
?>

<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <title>Absences - Profiling and Payroll Management System</title>
  </head>
  <body>
    <div class="page" id="app">
      <div class="page-main">
      <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
          <div class="container">
            <div class="row align-items-center">
              <div class="d-flex position-right">
                <?php require_once('includes/header.php') ?>
              </div>
              <div class="col-lg order-lg-first">
                <?php require_once('includes/subheader.php') ?> 
              </div>
            </div>
          </div>
        </div>
        <div class="my-3 my-md-5">
          <div class="container">
            <?php echo $stat ?>
            <div class="page-header">
              <h1 class="page-title">
                Employee Absences
              </h1>
            </div>
            <div class="row row-cards">
              <div style="padding-left: 12px; padding-bottom: 25px;">
                <button type="button" class="btn btn-secondary" data-toggle="modal" data-target="#modal-record-absence">
                   <i class="fe fe-plus mr-2"></i> Record Absence
                </button>
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-filter-absences">
                   <i class="fe fe-filter mr-2"></i> Filter Records
                </button>
                <button type="button" class="btn btn-info" onclick="runAbsenceCheck()">
                   <i class="fe fe-refresh-cw mr-2"></i> Check Missing Attendance
                </button>
              </div>
              <div class="col-12">
                <div class="card">
                  <div class="card-header py-3">
                    <h3 class="card-title">Absences (<?php echo date('M d, Y', strtotime($from)) ?> - <?php echo date('M d, Y', strtotime($to)) ?>)</h3>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-hover" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                          <tr>
                            <th>Employee ID</th>
                            <th>Name</th>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Reason</th>
                            <th>Status</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <?php while($row = mysqli_fetch_assoc($absenceResult)) { ?>
                          <tr>
                            <td><?php echo $row['emp_id'] ?></td>
                            <td><?php echo $row['fullname'] ?></td>
                            <td><?php echo date('M d, Y', strtotime($row['date'])) ?></td>
                            <td>
                              <?php 
                                $typeClass = '';
                                switch($row['type']) {
                                  case 'Unexcused': $typeClass = 'danger'; break;
                                  case 'Sick Leave': $typeClass = 'warning'; break;
                                  case 'Vacation': $typeClass = 'info'; break;
                                  case 'Personal Leave': $typeClass = 'primary'; break;
                                  default: $typeClass = 'secondary';
                                }
                              ?>
                              <span class="badge badge-<?php echo $typeClass ?>"><?php echo $row['type'] ?></span>
                            </td>
                            <td><?php echo $row['reason'] ? $row['reason'] : 'Not provided' ?></td>
                            <td>
                              <?php 
                                $statusClass = '';
                                switch($row['status']) {
                                  case 'Approved': $statusClass = 'success'; break;
                                  case 'Rejected': $statusClass = 'danger'; break;
                                  default: $statusClass = 'warning';
                                }
                              ?>
                              <span class="badge badge-<?php echo $statusClass ?>"><?php echo $row['status'] ?></span>
                            </td>
                            <td>
                              <button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#edit-<?php echo $row['id'] ?>">Edit</button>
                              <button class="btn btn-danger btn-sm" data-toggle="modal" data-target="#delete-<?php echo $row['id'] ?>">Delete</button>
                            </td>
                          </tr>

                          <!-- Edit Modal -->
                          <div id="edit-<?php echo $row['id'] ?>" class="modal fade animate" data-backdrop="true">
                            <div class="modal-dialog" id="animate">
                              <div class="modal-content">
                                <div class="modal-header">
                                  <h5 class="modal-title">Edit Absence Record</h5>
                                </div>
                                <div class="modal-body p-lg">
                                  <form action="" method="post">
                                    <input type="hidden" name="absence_id" value="<?php echo $row['id'] ?>">
                                    <input type="hidden" name="employee_id" value="<?php echo $row['employee_id'] ?>">
                                    
                                    <div class="form-group">
                                      <label class="form-label">Employee</label>
                                      <input type="text" class="form-control" value="<?php echo $row['fullname'] ?>" readonly>
                                    </div>
                                    
                                    <div class="form-group">
                                      <label class="form-label">Date</label>
                                      <input type="text" class="form-control" value="<?php echo date('F d, Y', strtotime($row['date'])) ?>" readonly>
                                      <input type="hidden" name="absence_year" value="<?php echo date('Y', strtotime($row['date'])) ?>">
                                      <input type="hidden" name="absence_month" value="<?php echo date('m', strtotime($row['date'])) ?>">
                                      <input type="hidden" name="absence_day" value="<?php echo date('d', strtotime($row['date'])) ?>">
                                    </div>
                                    
                                    <div class="form-group">
                                      <label class="form-label">Type</label>
                                      <select class="form-control" name="type">
                                        <option value="Unexcused" <?php echo ($row['type'] == 'Unexcused') ? 'selected' : '' ?>>Unexcused</option>
                                        <option value="Sick Leave" <?php echo ($row['type'] == 'Sick Leave') ? 'selected' : '' ?>>Sick Leave</option>
                                        <option value="Vacation" <?php echo ($row['type'] == 'Vacation') ? 'selected' : '' ?>>Vacation</option>
                                        <option value="Personal Leave" <?php echo ($row['type'] == 'Personal Leave') ? 'selected' : '' ?>>Personal Leave</option>
                                        <option value="Other" <?php echo ($row['type'] == 'Other') ? 'selected' : '' ?>>Other</option>
                                      </select>
                                    </div>
                                    
                                    <div class="form-group">
                                      <label class="form-label">Reason</label>
                                      <textarea class="form-control" name="reason" rows="3"><?php echo $row['reason'] ?></textarea>
                                    </div>
                                    
                                    <div class="form-group">
                                      <label class="form-label">Status</label>
                                      <select class="form-control" name="status">
                                        <option value="Pending" <?php echo ($row['status'] == 'Pending') ? 'selected' : '' ?>>Pending</option>
                                        <option value="Approved" <?php echo ($row['status'] == 'Approved') ? 'selected' : '' ?>>Approved</option>
                                        <option value="Rejected" <?php echo ($row['status'] == 'Rejected') ? 'selected' : '' ?>>Rejected</option>
                                      </select>
                                    </div>
                                    
                                    <div class="form-group">
                                      <button type="submit" name="record_absence" class="btn btn-primary">Update Record</button>
                                    </div>
                                  </form>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Delete Modal -->
                          <div id="delete-<?php echo $row['id'] ?>" class="modal fade animate" data-backdrop="true">
                            <div class="modal-dialog" id="animate">
                              <div class="modal-content">
                                <div class="modal-header">
                                  <h5 class="modal-title">Delete</h5>
                                </div>
                                <div class="modal-body text-center p-lg">
                                  <p>Are you sure you want to delete this absence record?</p>
                                  <p><b><?php echo $row['fullname'] ?></b> on <b><?php echo date('F d, Y', strtotime($row['date'])) ?></b></p>
                                </div>
                                <div class="modal-footer">
                                  <button type="button" class="btn dark-white p-x-md" data-dismiss="modal">No</button>
                                  <a href="delete/absence.php?id=<?php echo $row['id'] ?>"><button type="button" class="btn danger p-x-md">Yes</button></a>
                                </div>
                              </div>
                            </div>
                          </div>
                          <?php } ?>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <?php require_once('includes/footer.php') ?>
    </div>
    
    <!-- Record Absence Modal -->
    <div id="modal-record-absence" class="modal fade animate" data-backdrop="true">
      <div class="modal-dialog" id="animate">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Record Absence</h5>
          </div>
          <div class="modal-body p-lg">
            <form action="" method="post">
              <div class="form-group">
                <label class="form-label">Employee</label>
                <select class="form-control" name="employee_id" required>
                  <option value="">Select Employee</option>
                  <?php 
                  mysqli_data_seek($employeeResult, 0);
                  while($emp = mysqli_fetch_assoc($employeeResult)) { 
                  ?>
                    <option value="<?php echo $emp['id'] ?>"><?php echo $emp['fullname'] ?> (<?php echo $emp['employee_id'] ?>)</option>
                  <?php } ?>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Date</label>
                <div class="row">
                  <div class="col-md-4">
                    <select class="form-control" name="absence_month" required>
                      <option value="">Month</option>
                      <?php for($i = 1; $i <= 12; $i++): ?>
                        <option value="<?php echo str_pad($i, 2, '0', STR_PAD_LEFT); ?>" <?php echo (date('m') == $i) ? 'selected' : ''; ?>><?php echo date('F', mktime(0, 0, 0, $i, 1)); ?></option>
                      <?php endfor; ?>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <select class="form-control" name="absence_day" required>
                      <option value="">Day</option>
                      <?php for($i = 1; $i <= 31; $i++): ?>
                        <option value="<?php echo str_pad($i, 2, '0', STR_PAD_LEFT); ?>" <?php echo (date('d') == $i) ? 'selected' : ''; ?>><?php echo $i; ?></option>
                      <?php endfor; ?>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <select class="form-control" name="absence_year" required>
                      <option value="">Year</option>
                      <?php for($i = date('Y')-1; $i <= date('Y'); $i++): ?>
                        <option value="<?php echo $i; ?>" <?php echo (date('Y') == $i) ? 'selected' : ''; ?>><?php echo $i; ?></option>
                      <?php endfor; ?>
                    </select>
                  </div>
                </div>
              </div>
              
              <div class="form-group">
                <label class="form-label">Type</label>
                <select class="form-control" name="type" required>
                  <option value="Unexcused">Unexcused</option>
                  <option value="Sick Leave">Sick Leave</option>
                  <option value="Vacation">Vacation</option>
                  <option value="Personal Leave">Personal Leave</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Reason</label>
                <textarea class="form-control" name="reason" rows="3"></textarea>
              </div>
              
              <div class="form-group">
                <label class="form-label">Status</label>
                <select class="form-control" name="status" required>
                  <option value="Pending">Pending</option>
                  <option value="Approved">Approved</option>
                  <option value="Rejected">Rejected</option>
                </select>
              </div>
              
              <div class="form-group">
                <button type="submit" name="record_absence" class="btn btn-primary">Record Absence</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Filter Modal -->
    <div id="modal-filter-absences" class="modal fade animate" data-backdrop="true">
      <div class="modal-dialog" id="animate">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Filter Absences</h5>
          </div>
          <div class="modal-body p-lg">
            <form action="" method="get">
              <div class="form-group">
                <label class="form-label">Date Range</label>
                <div class="row">
                  <div class="col-md-6">
                    <label>From</label>
                    <input type="date" class="form-control" name="from" value="<?php echo $from; ?>">
                  </div>
                  <div class="col-md-6">
                    <label>To</label>
                    <input type="date" class="form-control" name="to" value="<?php echo $to; ?>">
                  </div>
                </div>
              </div>
              
              <div class="form-group">
                <label class="form-label">Employee</label>
                <select class="form-control" name="employee">
                  <option value="">All Employees</option>
                  <?php 
                  mysqli_data_seek($employeeResult, 0);
                  while($emp = mysqli_fetch_assoc($employeeResult)) { 
                  ?>
                    <option value="<?php echo $emp['id'] ?>" <?php echo ($employee_filter == $emp['id']) ? 'selected' : ''; ?>><?php echo $emp['fullname'] ?> (<?php echo $emp['employee_id'] ?>)</option>
                  <?php } ?>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Type</label>
                <select class="form-control" name="type">
                  <option value="">All Types</option>
                  <option value="Unexcused" <?php echo ($type_filter == 'Unexcused') ? 'selected' : ''; ?>>Unexcused</option>
                  <option value="Sick Leave" <?php echo ($type_filter == 'Sick Leave') ? 'selected' : ''; ?>>Sick Leave</option>
                  <option value="Vacation" <?php echo ($type_filter == 'Vacation') ? 'selected' : ''; ?>>Vacation</option>
                  <option value="Personal Leave" <?php echo ($type_filter == 'Personal Leave') ? 'selected' : ''; ?>>Personal Leave</option>
                  <option value="Other" <?php echo ($type_filter == 'Other') ? 'selected' : ''; ?>>Other</option>
                </select>
              </div>
              
              <div class="form-group">
                <button type="submit" class="btn btn-primary">Apply Filter</button>
                <a href="absences.php" class="btn btn-secondary">Reset</a>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
    
    <script>
    function runAbsenceCheck() {
      if(confirm('This will check for missing attendance records and mark them as absences. Continue?')) {
        fetch('scripts/check_absences_manual.php')
          .then(response => response.text())
          .then(data => {
            alert('Absence check completed. Page will now reload.');
            window.location.reload();
          })
          .catch(error => {
            console.error('Error:', error);
            alert('An error occurred during the absence check.');
          });
      }
    }
    </script>
    
    <?php require_once('includes/datatables.php') ?>
  </body>
</html>



