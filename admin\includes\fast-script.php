<!-- Fast Loading Scripts and Meta Files -->

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="theme-color" content="#4188c9">
    <link rel="icon" href="../favicon.ico" type="image/x-icon"/>
    
    <!-- Critical CSS - Inline for instant rendering -->
    <style>
    :root{--primary-color:#2c3e50;--accent-color:#3498db;--success-color:#27ae60;--warning-color:#f39c12;--danger-color:#e74c3c;--light-bg:#f8f9fa;--dark-text:#2c3e50;--muted-text:#6c757d;--border-color:#dee2e6;--shadow-light:0 2px 4px rgba(0,0,0,0.1);--border-radius:8px;--transition:all 0.3s ease}
    body{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;background-color:#f5f6fa;color:var(--dark-text);line-height:1.6;margin:0;padding:0}
    .page-title{font-size:2rem;font-weight:700;color:var(--primary-color);margin-bottom:0.5rem}
    .card{border:none;border-radius:var(--border-radius);box-shadow:var(--shadow-light);background:white;margin-bottom:1rem}
    .card-header{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:white;padding:1rem;border-radius:var(--border-radius) var(--border-radius) 0 0}
    .card-body{padding:1.5rem}
    .btn{border-radius:var(--border-radius);font-weight:500;padding:0.5rem 1rem;border:none;cursor:pointer;text-decoration:none;display:inline-block}
    .btn-primary{background:var(--accent-color);color:white}
    .btn-primary:hover{background:#2980b9;color:white}
    .table{width:100%;border-collapse:collapse;background:white}
    .table th,.table td{padding:0.75rem;text-align:left;border-bottom:1px solid #dee2e6}
    .table th{background:#f8f9fa;font-weight:600;color:var(--dark-text)}
    .form-control{border:1px solid #dee2e6;border-radius:var(--border-radius);padding:0.5rem;width:100%;box-sizing:border-box}
    .pagination{display:flex;list-style:none;padding:0;margin:1rem 0}
    .page-item{margin:0 0.25rem}
    .page-link{padding:0.5rem 0.75rem;border:1px solid #dee2e6;color:var(--dark-text);text-decoration:none;border-radius:var(--border-radius)}
    .page-link:hover{background:var(--accent-color);color:white}
    .page-item.active .page-link{background:var(--accent-color);color:white;border-color:var(--accent-color)}
    .alert{padding:0.75rem 1rem;border-radius:var(--border-radius);margin-bottom:1rem;border-left:4px solid}
    .alert-success{background:#d4edda;color:#155724;border-left-color:var(--success-color)}
    .alert-primary{background:#cce7ff;color:#004085;border-left-color:var(--accent-color)}
    .text-muted{color:var(--muted-text)}
    .text-center{text-align:center}
    .mb-4{margin-bottom:1.5rem}
    .mt-4{margin-top:1.5rem}
    .p-4{padding:1.5rem}
    .container{max-width:1200px;margin:0 auto;padding:0 1rem}
    .row{display:flex;flex-wrap:wrap;margin:0 -0.5rem}
    .col,.col-12{flex:1;padding:0 0.5rem}
    .col-md-6{flex:0 0 50%;max-width:50%}
    .col-md-8{flex:0 0 66.666667%;max-width:66.666667%}
    .col-md-4{flex:0 0 33.333333%;max-width:33.333333%}
    .col-auto{flex:0 0 auto}
    .d-flex{display:flex}
    .align-items-center{align-items:center}
    .justify-content-between{justify-content:space-between}
    .bg-light{background-color:var(--light-bg)}
    .border-bottom{border-bottom:1px solid var(--border-color)}
    .py-3{padding-top:1rem;padding-bottom:1rem}
    .input-group{display:flex}
    .input-group .form-control{border-radius:var(--border-radius) 0 0 var(--border-radius)}
    .input-group-append .btn{border-radius:0 var(--border-radius) var(--border-radius) 0}
    .table-responsive{overflow-x:auto}
    .loading{opacity:0.6;pointer-events:none}
    @media (max-width:768px){
        .col-md-6,.col-md-8,.col-md-4{flex:0 0 100%;max-width:100%}
        .row{margin:0}
        .col,.col-12{padding:0}
        .container{padding:0 0.5rem}
    }
    </style>
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Load non-critical CSS asynchronously -->
    <link rel="preload" href="../assets/css/dashboard.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="../assets/css/dashboard.css"></noscript>
    
    <!-- Minimal JavaScript for essential functionality -->
    <script>
    // Fast loading utilities
    window.fastLoad = {
        // Defer heavy scripts until after page load
        defer: function(callback) {
            if (document.readyState === 'complete') {
                callback();
            } else {
                window.addEventListener('load', callback);
            }
        },
        
        // Simple AJAX for essential requests
        ajax: function(url, callback) {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    callback(xhr.responseText);
                }
            };
            xhr.send();
        },
        
        // Show loading state
        showLoading: function(element) {
            if (element) element.classList.add('loading');
        },
        
        // Hide loading state
        hideLoading: function(element) {
            if (element) element.classList.remove('loading');
        }
    };
    
    // Optimize page loading
    document.addEventListener('DOMContentLoaded', function() {
        // Remove loading states
        document.body.classList.remove('loading');
        
        // Lazy load images
        var images = document.querySelectorAll('img[data-src]');
        if ('IntersectionObserver' in window) {
            var imageObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        var img = entry.target;
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                });
            });
            images.forEach(function(img) { imageObserver.observe(img); });
        } else {
            // Fallback for older browsers
            images.forEach(function(img) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            });
        }
    });
    
    // Load heavy scripts after page is ready
    window.fastLoad.defer(function() {
        // Load FontAwesome icons
        var fa = document.createElement('link');
        fa.rel = 'stylesheet';
        fa.href = 'https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css';
        document.head.appendChild(fa);
        
        // Load Google Fonts
        var gf = document.createElement('link');
        gf.rel = 'stylesheet';
        gf.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap';
        document.head.appendChild(gf);
    });
    </script>
