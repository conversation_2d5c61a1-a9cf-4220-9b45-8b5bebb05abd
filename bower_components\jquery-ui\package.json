{"name": "jquery-ui", "title": "jQuery <PERSON>", "description": "A curated set of user interface interactions, effects, widgets, and themes built on top of the jQuery JavaScript Library.", "version": "1.11.4", "homepage": "http://jqueryui.com", "author": {"name": "jQuery Foundation and other contributors", "url": "https://github.com/jquery/jquery-ui/blob/1-11-stable/AUTHORS.txt"}, "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://scottgonzalez.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "joern.z<PERSON><PERSON><PERSON>@gmail.com", "url": "http://bassistance.de"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://krisborchers.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://gnarf.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mike.sherov.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjvantoll.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.felixnagel.com"}], "repository": {"type": "git", "url": "git://github.com/jquery/jquery-ui.git"}, "bugs": "http://bugs.jqueryui.com/", "licenses": [{"type": "MIT", "url": "https://github.com/jquery/jquery-ui/blob/1-11-stable/LICENSE.txt"}], "dependencies": {}, "devDependencies": {"grunt": "~0.3.17", "grunt-css": "0.2.0", "grunt-compare-size": "0.1.4", "grunt-html": "0.1.1", "grunt-junit": "0.1.5", "grunt-git-authors": "1.0.0", "rimraf": "2.0.1", "testswarm": "0.3.0"}, "keywords": []}