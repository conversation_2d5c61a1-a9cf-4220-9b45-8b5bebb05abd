{"name": "ion.rangeSlider", "version": "2.2.0", "homepage": "https://github.com/IonDen/ion.rangeSlider", "authors": [{"name": "IonDen", "email": "<EMAIL>", "homepage": "http://ionden.com"}], "description": "Powerful range slider with skin support", "repository": {"type": "git", "url": "git://github.com/IonDen/ion.rangeSlider.git"}, "main": ["js/ion.rangeSlider.js", "css/ion.rangeSlider.css", "css/ion.rangeSlider.skinFlat.css", "img/sprite-skin-flat.png"], "dependencies": {"jquery": ">=1.8"}, "moduleType": ["globals"], "keywords": ["jquery-plugin", "diapason", "ion", "range", "slider"], "license": "MIT", "ignore": ["PSD", "ion-rangeSlider.jquery.json"], "_release": "2.2.0", "_resolution": {"type": "version", "tag": "2.2.0", "commit": "4bb522d3d28aedaaf035e3daa0f2f46850b2242c"}, "_source": "https://github.com/IonDen/ion.rangeSlider.git", "_target": "^2.2.0", "_originalSource": "ionrangeslider", "_direct": true}