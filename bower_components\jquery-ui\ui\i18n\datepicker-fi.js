/* Finnish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['fi'] = {
	closeText: 'Sulje',
	prevText: '&#xAB;Edellinen',
	nextText: 'Seuraava&#xBB;',
	currentText: 'Tänään',
	monthNames: ['<PERSON><PERSON><PERSON><PERSON>','He<PERSON>ikuu','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>okuu','<PERSON><PERSON><PERSON><PERSON><PERSON>',
	'<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON>akuu','<PERSON><PERSON>ku<PERSON>','<PERSON><PERSON>ku<PERSON>'],
	monthNamesShort: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>',
	'<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>'],
	dayNamesShort: ['<PERSON>','<PERSON>','Ti','<PERSON>','To','<PERSON>e','La'],
	dayNames: ['Sunnuntai','Maanantai','Tiistai','Keskiviikko','<PERSON>stai','Perjantai','Lauantai'],
	dayNames<PERSON>in: ['<PERSON>','Ma','Ti','Ke','To','<PERSON>e','La'],
	weekHeader: 'Vk',
	dateFormat: 'd.m.yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['fi']);

return datepicker.regional['fi'];

}));
