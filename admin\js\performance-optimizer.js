/**
 * Performance Optimizer
 * Handles preloading, lazy loading, and performance improvements
 */

class PerformanceOptimizer {
    constructor() {
        this.init();
    }
    
    init() {
        // Optimize page loading
        this.optimizePageLoad();
        
        // Implement lazy loading
        this.setupLazyLoading();
        
        // Preload critical resources
        this.preloadCriticalResources();
        
        // Setup performance monitoring
        this.setupPerformanceMonitoring();
        
        // Optimize images
        this.optimizeImages();
        
        // Setup service worker for caching
        this.setupServiceWorker();
    }
    
    optimizePageLoad() {
        // Defer non-critical JavaScript
        document.addEventListener('DOMContentLoaded', () => {
            this.deferNonCriticalJS();
        });
        
        // Optimize CSS loading
        this.optimizeCSSLoading();
        
        // Minimize DOM manipulation
        this.optimizeDOMOperations();
    }
    
    deferNonCriticalJS() {
        // Defer DataTables initialization until needed
        if (typeof $.fn.DataTable !== 'undefined') {
            const tables = document.querySelectorAll('table:not(.no-datatables)');
            tables.forEach(table => {
                if (table.rows.length > 10) {
                    // Only initialize DataTables for large tables
                    this.initializeDataTableLazy(table);
                }
            });
        }
        
        // Defer chart loading
        this.deferChartLoading();
    }
    
    initializeDataTableLazy(table) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    $(table).DataTable({
                        "pageLength": 25,
                        "responsive": true,
                        "processing": true,
                        "language": {
                            "processing": "Loading...",
                            "search": "Search:",
                            "lengthMenu": "Show _MENU_ entries",
                            "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                            "paginate": {
                                "first": "First",
                                "last": "Last",
                                "next": "Next",
                                "previous": "Previous"
                            }
                        }
                    });
                    observer.unobserve(table);
                }
            });
        });
        observer.observe(table);
    }
    
    setupLazyLoading() {
        // Lazy load images
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
        
        // Lazy load content sections
        this.setupContentLazyLoading();
    }
    
    setupContentLazyLoading() {
        const lazyContent = document.querySelectorAll('.lazy-content');
        const contentObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const loadUrl = element.dataset.loadUrl;
                    
                    if (loadUrl) {
                        this.loadContentLazy(element, loadUrl);
                    }
                    
                    contentObserver.unobserve(element);
                }
            });
        });
        
        lazyContent.forEach(element => contentObserver.observe(element));
    }
    
    async loadContentLazy(element, url) {
        try {
            element.innerHTML = '<div class="text-center p-4"><i class="fe fe-loader spinning"></i> Loading...</div>';
            
            const response = await fetch(url);
            const content = await response.text();
            
            element.innerHTML = content;
            
            // Trigger any necessary JavaScript for the loaded content
            this.initializeLoadedContent(element);
            
        } catch (error) {
            element.innerHTML = '<div class="text-center p-4 text-muted">Failed to load content</div>';
            console.error('Lazy loading failed:', error);
        }
    }
    
    initializeLoadedContent(element) {
        // Initialize any components in the loaded content
        const tables = element.querySelectorAll('table');
        tables.forEach(table => {
            if (table.rows.length > 5) {
                this.initializeDataTableLazy(table);
            }
        });
        
        // Initialize any other components
        this.initializeComponents(element);
    }
    
    preloadCriticalResources() {
        // Preload critical API data
        this.preloadApiData();
        
        // Preload critical CSS
        this.preloadCSS();
        
        // Preload next page content
        this.preloadNextPage();
    }
    
    async preloadApiData() {
        // Preload employee count for dashboard
        if (window.location.pathname.includes('/admin/')) {
            try {
                await fetch('includes/api_cache.php?action=preload', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
            } catch (error) {
                console.log('API preload failed:', error);
            }
        }
    }
    
    preloadCSS() {
        const criticalCSS = [
            '../assets/css/professional-theme.css'
        ];
        
        criticalCSS.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = href;
            document.head.appendChild(link);
        });
    }
    
    preloadNextPage() {
        // Preload likely next pages based on current page
        const currentPage = window.location.pathname;
        let nextPages = [];
        
        if (currentPage.includes('home.php')) {
            nextPages = ['profile.php', 'statistics.php'];
        } else if (currentPage.includes('profile.php')) {
            nextPages = ['statistics.php'];
        }
        
        nextPages.forEach(page => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = page;
            document.head.appendChild(link);
        });
    }
    
    optimizeCSSLoading() {
        // Load non-critical CSS asynchronously
        const nonCriticalCSS = document.querySelectorAll('link[rel="stylesheet"][data-non-critical]');
        nonCriticalCSS.forEach(link => {
            link.media = 'print';
            link.onload = function() {
                this.media = 'all';
            };
        });
    }
    
    optimizeDOMOperations() {
        // Batch DOM updates
        this.setupDOMBatching();
        
        // Optimize scroll events
        this.optimizeScrollEvents();
    }
    
    setupDOMBatching() {
        let pendingUpdates = [];
        let updateScheduled = false;
        
        window.batchDOMUpdate = function(updateFunction) {
            pendingUpdates.push(updateFunction);
            
            if (!updateScheduled) {
                updateScheduled = true;
                requestAnimationFrame(() => {
                    pendingUpdates.forEach(update => update());
                    pendingUpdates = [];
                    updateScheduled = false;
                });
            }
        };
    }
    
    optimizeScrollEvents() {
        let ticking = false;
        
        function updateScrollPosition() {
            // Handle scroll-based updates here
            ticking = false;
        }
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        });
    }
    
    optimizeImages() {
        // Convert images to WebP if supported
        if (this.supportsWebP()) {
            this.convertImagesToWebP();
        }
        
        // Implement responsive images
        this.setupResponsiveImages();
    }
    
    supportsWebP() {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    }
    
    convertImagesToWebP() {
        const images = document.querySelectorAll('img[src$=".jpg"], img[src$=".png"]');
        images.forEach(img => {
            const webpSrc = img.src.replace(/\.(jpg|png)$/, '.webp');
            
            // Check if WebP version exists
            const testImg = new Image();
            testImg.onload = () => {
                img.src = webpSrc;
            };
            testImg.src = webpSrc;
        });
    }
    
    setupResponsiveImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.srcset && img.dataset.responsive) {
                const baseSrc = img.src.replace(/\.(jpg|png|webp)$/, '');
                const ext = img.src.match(/\.(jpg|png|webp)$/)[0];
                
                img.srcset = `
                    ${baseSrc}-small${ext} 480w,
                    ${baseSrc}-medium${ext} 768w,
                    ${baseSrc}-large${ext} 1200w
                `;
                img.sizes = '(max-width: 480px) 480px, (max-width: 768px) 768px, 1200px';
            }
        });
    }
    
    setupPerformanceMonitoring() {
        // Monitor page load performance
        window.addEventListener('load', () => {
            if ('performance' in window) {
                const perfData = performance.getEntriesByType('navigation')[0];
                
                console.log('Performance Metrics:', {
                    'DNS Lookup': perfData.domainLookupEnd - perfData.domainLookupStart,
                    'TCP Connection': perfData.connectEnd - perfData.connectStart,
                    'Request': perfData.responseStart - perfData.requestStart,
                    'Response': perfData.responseEnd - perfData.responseStart,
                    'DOM Processing': perfData.domContentLoadedEventEnd - perfData.responseEnd,
                    'Total Load Time': perfData.loadEventEnd - perfData.navigationStart
                });
                
                // Send performance data to server if needed
                this.reportPerformanceMetrics(perfData);
            }
        });
    }
    
    reportPerformanceMetrics(perfData) {
        // Optional: Send performance metrics to server for monitoring
        const metrics = {
            loadTime: perfData.loadEventEnd - perfData.navigationStart,
            domReady: perfData.domContentLoadedEventEnd - perfData.navigationStart,
            page: window.location.pathname
        };
        
        // Uncomment to send metrics to server
        // fetch('/admin/performance-metrics.php', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(metrics)
        // });
    }
    
    setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/admin/sw.js')
                .then(registration => {
                    console.log('Service Worker registered');
                })
                .catch(error => {
                    console.log('Service Worker registration failed');
                });
        }
    }
    
    deferChartLoading() {
        // Defer chart initialization until the chart container is visible
        const chartContainers = document.querySelectorAll('.chart-container');
        const chartObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.initializeChart(entry.target);
                    chartObserver.unobserve(entry.target);
                }
            });
        });
        
        chartContainers.forEach(container => chartObserver.observe(container));
    }
    
    initializeChart(container) {
        // Initialize chart when container becomes visible
        const chartType = container.dataset.chartType;
        const chartData = container.dataset.chartData;
        
        if (chartType && chartData) {
            // Initialize chart based on type
            console.log('Initializing chart:', chartType);
        }
    }
    
    initializeComponents(container = document) {
        // Initialize any custom components in the container
        const components = container.querySelectorAll('[data-component]');
        components.forEach(element => {
            const componentType = element.dataset.component;
            
            switch (componentType) {
                case 'tooltip':
                    this.initializeTooltip(element);
                    break;
                case 'modal':
                    this.initializeModal(element);
                    break;
                // Add more component types as needed
            }
        });
    }
    
    initializeTooltip(element) {
        // Initialize tooltip if Bootstrap is available
        if (typeof bootstrap !== 'undefined') {
            new bootstrap.Tooltip(element);
        }
    }
    
    initializeModal(element) {
        // Initialize modal if Bootstrap is available
        if (typeof bootstrap !== 'undefined') {
            new bootstrap.Modal(element);
        }
    }
}

// Initialize performance optimizer when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.performanceOptimizer = new PerformanceOptimizer();
});

// Export for global access
window.PerformanceOptimizer = PerformanceOptimizer;
