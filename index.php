<?php
$timezone = 'Asia/Manila';
date_default_timezone_set($timezone);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])) {
    header("location:admin/index.php?utm_campaign=expired");
    exit;
}

// Get user information
require_once('admin/includes/script.php');
require_once('admin/session/ModelController.php');  // This includes both Global.php and ModelController.php
$model = new Dashboard();
$password = $_SESSION['official_password'];
$username = $_SESSION['official_username'];
$uid = $_SESSION['official_id'];

$connection = $model->TemporaryConnection();
$query = $model->GetAdministrator($username, $password);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];
$type = $admin['type'];
?>
<!doctype html>
<html lang="en" dir="ltr">
	<head>
		<?php require_once('admin/includes/script.php') ?>
		<title>Profiling and Payroll Management System</title>
		<link rel="icon" href="favicon.ico" type="image/x-icon"/>
		<!-- Dashboard Core -->
		<link href="./assets/css/dashboard.css" rel="stylesheet" />
	
	</head>
	<body class="">
		<div class="page">
			<div class="page-single">
				<div class="container">
					<div class="text-center mb-4">
						<div class="card shadow-custom bg-gradient-primary text-white">
							<div class="card-body py-4">
								<h1 class="display-4 mb-2" id="time"></h1>
								<h3 class="mb-0" id="date"></h3>
								<p class="mb-0 opacity-75">Employee Time Tracking System</p>
							</div>
						</div>
					</div>
					
					<!-- Add user info display -->
					<div class="row mb-4">
						<div class="col-12">
							<div class="card">
								<div class="card-body text-center">
									<div class="avatar avatar-xl mb-3">
										<img src="admin/demo/<?php echo $photo; ?>" class="avatar avatar-xl" alt="">
									</div>
									<h4 class="mb-0"><?php echo $firstname . ' ' . $lastname; ?></h4>
									<p class="text-muted"><?php echo $type; ?></p>
								</div>
							</div>
						</div>
					</div>
										
					<div class="row">
						<div class="col-lg-6 mb-4">
							<div class="card shadow-custom fade-in-up">
								<div class="card-header bg-gradient-primary text-white">
									<h3 class="card-title mb-0">
										<i class="fe fe-sunrise mr-2"></i>Morning Attendance
									</h3>
								</div>
								<div class="card-body text-center py-5">
									<div class="mb-4">
										<i class="fe fe-sun" style="font-size: 3rem; color: #f39c12;"></i>
									</div>
									<div class="btn-group-vertical w-100">
										<a href="time-in-morning.php" class="btn btn-success btn-lg mb-2">
											<i class="fe fe-log-in mr-2"></i>Time In
										</a>
										<a href="time-out-morning.php" class="btn btn-warning btn-lg">
											<i class="fe fe-log-out mr-2"></i>Time Out
										</a>
									</div>
								</div>
							</div>
						</div>
						<div class="col-lg-6 mb-4">
							<div class="card shadow-custom fade-in-up" style="animation-delay: 0.2s;">
								<div class="card-header bg-gradient-primary text-white">
									<h3 class="card-title mb-0">
										<i class="fe fe-sunset mr-2"></i>Afternoon Attendance
									</h3>
								</div>
								<div class="card-body text-center py-5">
									<div class="mb-4">
										<i class="fe fe-moon" style="font-size: 3rem; color: #9b59b6;"></i>
									</div>
									<div class="btn-group-vertical w-100">
										<a href="time-in-afternoon.php" class="btn btn-success btn-lg mb-2">
											<i class="fe fe-log-in mr-2"></i>Time In
										</a>
										<a href="time-out-afternoon.php" class="btn btn-warning btn-lg">
											<i class="fe fe-log-out mr-2"></i>Time Out
										</a>
									</div>
								</div>
							</div>
						</div>
					</div>
					
					<!-- Action Buttons -->
					<div class="text-center mt-4">
						<div class="card shadow-custom">
							<div class="card-body py-3">
								<div class="btn-group">
									<?php if($type == "Administrator"): ?>
									<a href="admin" class="btn btn-primary">
										<i class="fe fe-settings mr-2"></i>Dashboard Panel
									</a>
									<?php endif; ?>
									<a href="admin/signout.php" class="btn btn-outline-danger">
										<i class="fe fe-log-out mr-2"></i>Sign Out
									</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	
	<!-- jQuery 3 -->
	<script src="bower_components/jquery/dist/jquery.min.js"></script>
	<!-- Bootstrap 3.3.7 -->
	<script src="bower_components/bootstrap/dist/js/bootstrap.min.js"></script>
	<!-- Moment JS -->
	<script src="bower_components/moment/moment.js"></script>
	<script type="text/javascript">
	$(function() {
	var interval = setInterval(function() {
	var momentNow = moment();
	$('#date').html(momentNow.format('dddd').substring(0,3).toUpperCase() + ' - ' + momentNow.format('MMMM DD, YYYY'));
	$('#time').html(momentNow.format('hh:mm:ss A'));
	}, 100);
	
	$('#attendance').submit(function(e){
	e.preventDefault();
	var attendance = $(this).serialize();
	$.ajax({
	type: 'POST',
	url: 'attendance.php',
	data: attendance,
	dataType: 'json',
	success: function(response){
	if(response.error){
	$('.alert').hide();
	$('.alert-danger').show();
	$('.message').html(response.message);
	}
	else{
	$('.alert').hide();
	$('.alert-success').show();
	$('.message').html(response.message);
	$('#employee').val('');
	}
	}
	});
	});
	});
	</script>
</body>
</html>