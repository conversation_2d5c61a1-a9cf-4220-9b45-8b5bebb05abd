/* Slovenian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
/* c = č, s = š z = ž C = Č S = Š Z = Ž */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['sl'] = {
	closeText: '<PERSON><PERSON><PERSON>',
	prevText: '&#x3C;Prejšnji',
	nextText: 'Naslednji&#x3E;',
	currentText: 'Trenutni',
	monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','April','Maj','Junij',
	'Julij','Avgust','September','Oktober','November','December'],
	monthNamesShort: ['Jan','Feb','<PERSON>','Apr','<PERSON>','<PERSON>',
	'Jul','Avg','Sep','Okt','Nov','Dec'],
	dayNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>etrtek','<PERSON>k','Sobota'],
	dayNamesShort: ['Ned','Pon','Tor','Sre','Čet','Pet','Sob'],
	dayNamesMin: ['Ne','Po','To','Sr','Če','Pe','So'],
	weekHeader: 'Teden',
	dateFormat: 'dd.mm.yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['sl']);

return datepicker.regional['sl'];

}));
