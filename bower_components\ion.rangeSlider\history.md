![ion.rangeSlider](_tmp/logo-ion-range-slider.png)

# Update History

### Version 2.2.0. June 21, 2017
* Slider has `TabIndex` support now. Issue #321
* `keyboard_step` option removed as confusing.
* `keyboard` controls are enabled by default now
* `keyboard` movement is bind to step now
* Traverse extra class names to container. Issue #318
* Prettified values added to `result object`. Issue #356
* It is possible to pass `scope` for callbacks now. Issue #402
* New option `block`. Light version of `disabled` but sliders value can be send with form. Issue #242

### Version 2.1.8. June 19, 2017
* Issues resolved: #420, #423, #441, #464, #479

### Version 2.1.7. March 26, 2017
* Issues: #438
* Reverted: #390

### Version 2.1.6. December 27, 2016
* Issues: #393, #406, #429, #431, #432

### Version 2.1.5. December 12, 2016
* Issues: #331, #332, #333, #337, #338, #343, #358, #374, #380, #389, #390, #394, #411, #412, #413

### Version 2.1.4. April 27, 2016
* Issues: #330, #369

### Version 2.1.3. April 10, 2016
* Issues: #342, #350

### Version 2.1.2. October 13, 2015
* Fixed bugs. Issues: #310, #312, #313, #316

### Version 2.1.1. September 21, 2015
* Fixed bugs. Issues: #305
* New features. Requests: #282

### Version 2.1.0. September 20, 2015
* Fixed bugs. Issues: #288
* New features. Requests: #281, #284, #286, #289
* Some code refactoring
* Code documentation updated

### Version 2.0.13. July 25, 2015
* Fixed bugs. Issues: #208, #270, #273
* New features. Requests: #233

### Version 2.0.12. July 10, 2015
* Fixed more bugs. Issues: #247, #263, #265, #269

### Version 2.0.11. July 02, 2015
* Fixed more bugs. Issues: #247, #253

### Version 2.0.10. June 06, 2015
* Fixed rounding bug. Issue #247

### Version 2.0.9. May 30, 2015
* Fixed critical bug. Issue #246

### Version 2.0.8. May 30, 2015
* Fixed some bugs. Issues: #219, #228, #230, #245

### Version 2.0.7. May 26, 2015
* Fixed memory issue: #220
* Fixed CPU issue: #186
* Merged PRs: #209, #226, #235

### Version 2.0.6. February 17, 2015
* Issues done: #197
* Fixed bug with broken From and To

### Version 2.0.5. February 13, 2015
* Issues done: #193, #195, #201

### Version 2.0.4. February 12, 2015
* Issues done: #174, #184
* NPM support added
* Readme files optimisations

### Version 2.0.3. December 29, 2014
* Issues done: #160, #165, #166, #168, #170

### Version 2.0.2: December 02, 2014
* Issues done: #143, #148, #153, #155, #159
* API update.

### Version 2.0.1: November 15, 2014
* Some bugs fixed
* Some new methods

### Version 2.0.0: November 08, 2014
* New Core
* New API
* Lot's of bug fixes
* Many improvements
* SPM support added

### Version 1.9.3: August 06, 2014
* Bower support added

### Version 1.9.2: August 04, 2014
* New param gridMargin
* Issues done: #89, #94, #96, #97, #98, #103

### Version 1.9.1: April 15, 2014
* Issues done: #81, #82, #85

### Version 1.9.0: March 16, 2014
* Issues done: #65, #68, #70, #77, #78
* New plugin description
* New demos design
* Some new slider params

### Version 1.8.5: January 12, 2014
* Issues done: #12, #30, #33, #43, #47, #52, #58
* Bug fixes
* New param "disable"
* Link to input and slider in all callbacks
* Click on slider to move it

### Version 1.8.2: October 31, 2013
* Issues done: #13, #31, #35, #37, #40
* Some code optimisations

### Version 1.8.1: October 10, 2013
* Issues done: #25
* New Flat UI Skin
* Some skin optimisations

### Version 1.8.0: October 08, 2013
* Issues done: #20, #21, #23, #24, #26
* Removed hideText option
* New method and options
* Improved code style
* Minor optimisations

### Version 1.7.2: September 11, 2013
* Issues done: #15, #16
* Fixed bug on Android-devices
* Added support for negative and fractional values

### Version 1.7.0: August 23, 2013
* Issues done: #7, #8, #9, #10
* Some enhancements

### Version 1.6.3: July 29, 2013
* Issues done: #2
* Moved to Semantic Versioning

### December, 2012
* Plugin release

***

Support the plugin:

[![](https://pledgie.com/campaigns/25694.png?skin_name=chrome)](https://pledgie.com/campaigns/25694)