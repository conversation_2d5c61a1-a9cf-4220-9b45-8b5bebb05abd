{"name": "inputmask", "version": "3.3.8", "main": ["./dist/inputmask/inputmask.js", "./dist/inputmask/inputmask.extensions.js", "./dist/inputmask/inputmask.date.extensions.js", "./dist/inputmask/inputmask.numeric.extensions.js", "./dist/inputmask/inputmask.phone.extensions.js", "./dist/inputmask/jquery.inputmask.js", "./dist/inputmask/global/document.js", "./dist/inputmask/global/window.js", "./dist/inputmask/phone-codes/phone.js", "./dist/inputmask/phone-codes/phone-be.js", "./dist/inputmask/phone-codes/phone-nl.js", "./dist/inputmask/phone-codes/phone-ru.js", "./dist/inputmask/phone-codes/phone-uk.js", "./dist/inputmask/dependencyLibs/inputmask.dependencyLib.jqlite.js", "./dist/inputmask/dependencyLibs/inputmask.dependencyLib.jquery.js", "./dist/inputmask/dependencyLibs/inputmask.dependencyLib.js", "./dist/inputmask/bindings/inputmask.binding.js"], "keywords": ["j<PERSON>y", "plugins", "input", "form", "inputmask", "mask"], "description": "Inputmask is a javascript library which creates an input mask.  Inputmask can run against vanilla javascript, jQuery and jqlite.", "license": "http://opensource.org/licenses/mit-license.php", "ignore": ["**/*", "!dist/*", "!dist/inputmask/*", "!dist/min/*", "!dist/min/inputmask/*"], "dependencies": {"jquery": ">=1.7"}, "authors": [{"name": "<PERSON>"}], "homepage": "http://robinherbots.github.io/Inputmask"}