<div class="row">
    <article class="col-md-12">
        <hr>
        <div class="social">
            <a href="https://github.com/itsjavi/bootstrap-colorpicker/" target="_blank"
               class="btn btn-default btn-sm"><span class="octicon octicon-mark-github"></span> Source code</a>
            <a href="https://github.com/itsjavi/bootstrap-colorpicker/archive/master.zip" target="_blank"
               class="btn btn-success btn-sm"><i class="glyphicon glyphicon-download-alt"></i> Download latest</a>

            <div class="share-btn share-btn-twitter">
                <a href="https://twitter.com/share" class="twitter-share-button" data-size="large" data-related="itsjaviaguilar"
                   data-hashtags="bootstrap">Tweet</a>
                <script>!function (d, s, id) {
                    var js, fjs = d.getElementsByTagName(s)[0], p = /^http:/.test(d.location) ? 'http' : 'https';
                    if (!d.getElementById(id)) {
                        js = d.createElement(s);
                        js.id = id;
                        js.src = p + '://platform.twitter.com/widgets.js';
                        fjs.parentNode.insertBefore(js, fjs);
                    }
                }(document, 'script', 'twitter-wjs');</script>
            </div>

            <div class="share-btn share-btn-google-plus">
                <!-- Place this tag where you want the +1 button to render. -->
                <div class="g-plusone" data-size="large"></div>

                <!-- Place this tag after the last +1 button tag. -->
                <script type="text/javascript">
                    (function () {
                        var po = document.createElement('script');
                        po.type = 'text/javascript';
                        po.async = true;
                        po.src = 'https://apis.google.com/js/plusone.js';
                        var s = document.getElementsByTagName('script')[0];
                        s.parentNode.insertBefore(po, s);
                    })();
                </script>
            </div>
        </div>
        <hr>
    </article>
</div>
