/**
 * Auto Sync Manager
 * Handles automatic ERP synchronization every 5 minutes
 */

class AutoSyncManager {
    constructor() {
        this.syncInterval = 5 * 60 * 1000; // 5 minutes in milliseconds
        this.syncTimer = null;
        this.isRunning = false;
        this.lastSyncTime = null;
        this.syncStatus = 'idle';
        
        this.init();
    }
    
    init() {
        // Check if auto-sync is enabled
        if (this.isAutoSyncEnabled()) {
            this.startAutoSync();
        }
        
        // Add sync status indicator to page
        this.addSyncIndicator();
        
        // Check last sync time on page load
        this.checkLastSyncTime();
        
        // Listen for page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isAutoSyncEnabled()) {
                this.checkSyncNeeded();
            }
        });
    }
    
    isAutoSyncEnabled() {
        // Check if auto-sync is enabled (you can add a setting for this)
        return localStorage.getItem('autoSyncEnabled') !== 'false';
    }
    
    startAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
        }
        
        // Run initial sync after 30 seconds
        setTimeout(() => {
            this.performSync();
        }, 30000);
        
        // Set up recurring sync
        this.syncTimer = setInterval(() => {
            this.performSync();
        }, this.syncInterval);
        
        this.isRunning = true;
        this.updateSyncIndicator('running');
        console.log('Auto-sync started - will sync every 5 minutes');
    }
    
    stopAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
        }
        
        this.isRunning = false;
        this.updateSyncIndicator('stopped');
        console.log('Auto-sync stopped');
    }
    
    async performSync() {
        if (this.syncStatus === 'syncing') {
            console.log('Sync already in progress, skipping...');
            return;
        }
        
        this.syncStatus = 'syncing';
        this.updateSyncIndicator('syncing');
        
        try {
            console.log('Starting automatic ERP sync...');
            
            const response = await fetch('sync/auto_sync.php?manual_run=true', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.lastSyncTime = new Date();
                this.syncStatus = 'success';
                this.updateSyncIndicator('success');
                
                // Store last sync time
                localStorage.setItem('lastSyncTime', this.lastSyncTime.toISOString());
                
                console.log('Auto-sync completed successfully');
                
                // Show subtle notification
                this.showSyncNotification('ERP data synchronized successfully', 'success');
                
                // Refresh page data if we're on certain pages
                this.refreshPageData();
                
            } else {
                throw new Error(result.message || 'Sync failed');
            }
            
        } catch (error) {
            console.error('Auto-sync failed:', error);
            this.syncStatus = 'error';
            this.updateSyncIndicator('error');
            
            // Show error notification
            this.showSyncNotification('ERP sync failed: ' + error.message, 'error');
        }
    }
    
    checkLastSyncTime() {
        const lastSync = localStorage.getItem('lastSyncTime');
        if (lastSync) {
            this.lastSyncTime = new Date(lastSync);
            this.updateSyncIndicator('success');
        }
    }
    
    checkSyncNeeded() {
        if (!this.lastSyncTime) {
            this.performSync();
            return;
        }
        
        const timeSinceLastSync = Date.now() - this.lastSyncTime.getTime();
        if (timeSinceLastSync > this.syncInterval) {
            this.performSync();
        }
    }
    
    addSyncIndicator() {
        // Add sync status indicator to the page
        const indicator = document.createElement('div');
        indicator.id = 'sync-indicator';
        indicator.innerHTML = `
            <div class="sync-status">
                <i class="fe fe-refresh-cw sync-icon"></i>
                <span class="sync-text">ERP Sync</span>
                <span class="sync-time"></span>
            </div>
        `;
        
        // Add CSS styles
        const style = document.createElement('style');
        style.textContent = `
            #sync-indicator {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1050;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                padding: 8px 12px;
                font-size: 12px;
                color: #6c757d;
                border-left: 3px solid #28a745;
                transition: all 0.3s ease;
                opacity: 0.8;
            }
            
            #sync-indicator:hover {
                opacity: 1;
                transform: translateY(-1px);
            }
            
            .sync-status {
                display: flex;
                align-items: center;
                gap: 6px;
            }
            
            .sync-icon {
                font-size: 14px;
            }
            
            .sync-icon.spinning {
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            
            #sync-indicator.syncing {
                border-left-color: #ffc107;
                color: #856404;
            }
            
            #sync-indicator.error {
                border-left-color: #dc3545;
                color: #721c24;
            }
            
            #sync-indicator.success {
                border-left-color: #28a745;
                color: #155724;
            }
            
            .sync-notification {
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 1060;
                max-width: 300px;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            
            .sync-notification.show {
                transform: translateX(0);
            }
            
            .sync-notification.success {
                background: #d4edda;
                color: #155724;
                border-left: 4px solid #28a745;
            }
            
            .sync-notification.error {
                background: #f8d7da;
                color: #721c24;
                border-left: 4px solid #dc3545;
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(indicator);
    }
    
    updateSyncIndicator(status) {
        const indicator = document.getElementById('sync-indicator');
        const icon = indicator.querySelector('.sync-icon');
        const text = indicator.querySelector('.sync-text');
        const time = indicator.querySelector('.sync-time');
        
        // Remove all status classes
        indicator.classList.remove('syncing', 'error', 'success', 'stopped');
        icon.classList.remove('spinning');
        
        switch (status) {
            case 'syncing':
                indicator.classList.add('syncing');
                icon.classList.add('spinning');
                text.textContent = 'Syncing...';
                time.textContent = '';
                break;
                
            case 'success':
                indicator.classList.add('success');
                text.textContent = 'ERP Sync';
                if (this.lastSyncTime) {
                    time.textContent = this.formatSyncTime(this.lastSyncTime);
                }
                break;
                
            case 'error':
                indicator.classList.add('error');
                text.textContent = 'Sync Error';
                time.textContent = '';
                break;
                
            case 'stopped':
                indicator.classList.add('stopped');
                text.textContent = 'Sync Disabled';
                time.textContent = '';
                break;
                
            default:
                text.textContent = 'ERP Sync';
                time.textContent = '';
        }
    }
    
    formatSyncTime(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        
        if (minutes < 1) return 'Just now';
        if (minutes < 60) return `${minutes}m ago`;
        
        const hours = Math.floor(minutes / 60);
        if (hours < 24) return `${hours}h ago`;
        
        return date.toLocaleDateString();
    }
    
    showSyncNotification(message, type) {
        // Remove existing notification
        const existing = document.querySelector('.sync-notification');
        if (existing) {
            existing.remove();
        }
        
        const notification = document.createElement('div');
        notification.className = `sync-notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // Hide notification after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }
    
    refreshPageData() {
        // Refresh data on specific pages
        const currentPage = window.location.pathname;
        
        if (currentPage.includes('profile.php')) {
            // Refresh employee table if on profile page
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else if (currentPage.includes('statistics.php') || currentPage.includes('home.php')) {
            // Refresh statistics if on dashboard pages
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
    }
    
    // Public methods for manual control
    enableAutoSync() {
        localStorage.setItem('autoSyncEnabled', 'true');
        if (!this.isRunning) {
            this.startAutoSync();
        }
    }
    
    disableAutoSync() {
        localStorage.setItem('autoSyncEnabled', 'false');
        this.stopAutoSync();
    }
    
    manualSync() {
        this.performSync();
    }
}

// Initialize auto-sync when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize on admin pages
    if (window.location.pathname.includes('/admin/')) {
        window.autoSyncManager = new AutoSyncManager();
    }
});

// Export for global access
window.AutoSyncManager = AutoSyncManager;
