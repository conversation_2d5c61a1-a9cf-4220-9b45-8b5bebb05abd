<?php 
require_once('includes/script.php');  
require_once('session/Login.php'); 

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
   header("location:index.php?utm_campaign=expired");
}

$model = new Dashboard();
$password = $_SESSION['official_password'];
$username = $_SESSION['official_username'];
$uid = $_SESSION['official_id'];

$connection = $model->TemporaryConnection();

$query = $model->GetAdministrator($username, $password);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];
$type = $admin['type'];
$email = isset($admin['email']) ? $admin['email'] : '';
$phone = isset($admin['phone']) ? $admin['phone'] : '';

// Handle profile update
$message = '';
if(isset($_POST['update_profile'])) {
    $new_firstname = $_POST['firstname'];
    $new_lastname = $_POST['lastname'];
    $new_email = $_POST['email'];
    $new_phone = $_POST['phone'];
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Verify current password
    if(!empty($current_password)) {
        $verify_query = "SELECT * FROM administrator WHERE username='$username' AND password='$current_password'";
        $verify_result = mysqli_query($connection, $verify_query);
        
        if(mysqli_num_rows($verify_result) > 0) {
            // Update basic info
            $update_query = "UPDATE administrator SET 
                firstname='$new_firstname', 
                lastname='$new_lastname', 
                email='$new_email', 
                phone='$new_phone'";
            
            // Update password if new password is provided
            if(!empty($new_password) && $new_password == $confirm_password) {
                $update_query .= ", password='$new_password'";
                $_SESSION['official_password'] = $new_password;
            } elseif(!empty($new_password) && $new_password != $confirm_password) {
                $message = '<div class="alert alert-danger">New passwords do not match!</div>';
            }
            
            $update_query .= " WHERE id=$id";
            
            if(mysqli_query($connection, $update_query)) {
                $message = '<div class="alert alert-success">Profile updated successfully!</div>';
                
                // Update session variables
                $_SESSION['official_firstname'] = $new_firstname;
                $_SESSION['official_lastname'] = $new_lastname;
                
                // Refresh admin data
                $query = $model->GetAdministrator($username, $_SESSION['official_password']);
                $admin = mysqli_fetch_assoc($query);
                $firstname = $admin['firstname'];
                $lastname = $admin['lastname'];
                $email = isset($admin['email']) ? $admin['email'] : '';
                $phone = isset($admin['phone']) ? $admin['phone'] : '';
            } else {
                $message = '<div class="alert alert-danger">Error updating profile: ' . mysqli_error($connection) . '</div>';
            }
        } else {
            $message = '<div class="alert alert-danger">Current password is incorrect!</div>';
        }
    } else {
        $message = '<div class="alert alert-danger">Please enter your current password to make changes!</div>';
    }
}

// Handle photo upload
if(isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] == 0) {
    $allowed = ['jpg', 'jpeg', 'png', 'gif'];
    $filename = $_FILES['profile_photo']['name'];
    $filetype = pathinfo($filename, PATHINFO_EXTENSION);
    
    if(in_array(strtolower($filetype), $allowed)) {
        // Create directory if it doesn't exist
        $upload_dir = '../uploads/admin/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        
        $new_filename = 'admin_' . $id . '_' . time() . '.' . $filetype;
        $upload_path = $upload_dir . $new_filename;
        
        if(move_uploaded_file($_FILES['profile_photo']['tmp_name'], $upload_path)) {
            $update_photo = "UPDATE administrator SET photo='$new_filename' WHERE id=$id";
            if(mysqli_query($connection, $update_photo)) {
                $photo = $new_filename;
                $message = '<div class="alert alert-success">Profile photo updated successfully!</div>';
            } else {
                $message = '<div class="alert alert-danger">Error updating profile photo in database!</div>';
            }
        } else {
            $message = '<div class="alert alert-danger">Error uploading profile photo!</div>';
        }
    } else {
        $message = '<div class="alert alert-danger">Invalid file type. Please upload JPG, JPEG, PNG or GIF files only.</div>';
    }
}
?>

<!doctype html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta http-equiv="Content-Language" content="en" />
  <meta name="msapplication-TileColor" content="#2d89ef">
  <meta name="theme-color" content="#4188c9">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/>
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="HandheldFriendly" content="True">
  <meta name="MobileOptimized" content="320">
  <link rel="icon" href="./favicon.ico" type="image/x-icon"/>
  <link rel="shortcut icon" type="image/x-icon" href="./favicon.ico" />
  <title>Admin Profile - Profiling and Payroll Management System</title>
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,300i,400,400i,500,500i,600,600i,700,700i&amp;subset=latin-ext">
  <script src="./assets/js/require.min.js"></script>
  <script>
    requirejs.config({
      baseUrl: '.'
    });
  </script>
  <!-- Dashboard Core -->
  <link href="./assets/css/dashboard.css" rel="stylesheet" />
  <script src="./assets/js/dashboard.js"></script>
</head>
<body class="">
  <div class="page">
    <div class="page-main">
    <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
          <div class="container">
            <div class="row align-items-center">
              <div class="d-flex">
                <?php require_once('includes/header.php') ?>
              </div>
              <div class="col-lg order-lg-first">
                <?php require_once('includes/subheader.php') ?> 
              </div>
            </div>
          </div>
        </div>
      <div class="my-3 my-md-5">
        <div class="container">
          <div class="page-header">
            <h1 class="page-title">Admin Profile</h1>
          </div>
          
          <?php echo $message; ?>
          
          <div class="row">
            <div class="col-lg-4">
              <div class="card">
                <div class="card-body text-center">
                  <div class="mb-3">
                    <img src="<?php echo !empty($photo) ? '../uploads/admin/'.$photo : './demo/faces/female/25.jpg'; ?>" class="avatar avatar-xxl" alt="Profile Image">
                  </div>
                  <h3 class="mb-1"><?php echo $firstname . ' ' . $lastname; ?></h3>
                  <p class="mb-4 text-muted"><?php echo $type; ?></p>
                  
                  <form action="" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                      <div class="custom-file">
                        <input type="file" class="custom-file-input" name="profile_photo" id="profile_photo">
                        <label class="custom-file-label">Choose new photo</label>
                      </div>
                    </div>
                    <button type="submit" class="btn btn-primary btn-block">Update Photo</button>
                  </form>
                </div>
                <div class="card-footer">
                  <div class="row">
                    <div class="col">
                      <div class="text-muted">Member since</div>
                      <strong><?php echo date('M d, Y', strtotime($create)); ?></strong>
                    </div>
                    <div class="col">
                      <div class="text-muted">Role</div>
                      <strong><?php echo $type; ?></strong>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="col-lg-8">
              <form class="card" action="" method="post">
                <div class="card-body">
                  <h3 class="card-title">Edit Profile</h3>
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">First Name</label>
                        <input type="text" class="form-control" name="firstname" value="<?php echo $firstname; ?>" required>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">Last Name</label>
                        <input type="text" class="form-control" name="lastname" value="<?php echo $lastname; ?>" required>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-control" name="email" value="<?php echo $email; ?>">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">Phone</label>
                        <input type="text" class="form-control" name="phone" value="<?php echo $phone; ?>">
                      </div>
                    </div>
                    <div class="col-md-12">
                      <div class="form-group">
                        <label class="form-label">Username</label>
                        <input type="text" class="form-control" value="<?php echo $username; ?>" disabled>
                        <small class="text-muted">Username cannot be changed</small>
                      </div>
                    </div>
                    <div class="col-md-12">
                      <hr>
                      <h4>Change Password</h4>
                    </div>
                    <div class="col-md-12">
                      <div class="form-group">
                        <label class="form-label">Current Password</label>
                        <input type="password" class="form-control" name="current_password">
                        <small class="text-muted">Required to make any changes</small>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">New Password</label>
                        <input type="password" class="form-control" name="new_password">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" name="confirm_password">
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-footer text-right">
                  <button type="submit" name="update_profile" class="btn btn-primary">Update Profile</button>
                </div>
              </form>
              
              <div class="card">
                <div class="card-header">
                  <h3 class="card-title">Account Activity</h3>
                </div>
                <div class="table-responsive">
                  <table class="table card-table table-vcenter text-nowrap">
                    <thead>
                      <tr>
                        <th>Event</th>
                        <th>Date</th>
                        <th>IP Address</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>Last login</td>
                        <td><?php echo date('M d, Y H:i:s'); ?></td>
                        <td><?php echo $_SERVER['REMOTE_ADDR']; ?></td>
                      </tr>
                      <tr>
                        <td>Account created</td>
                        <td><?php echo date('M d, Y', strtotime($create)); ?></td>
                        <td>-</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <?php require_once('includes/footer.php') ?>
  </div>
  <?php require_once('includes/datatables.php') ?>
  <?php require_once('includes/bower.php') ?>
  <script>
  document.addEventListener('DOMContentLoaded', function() {
    // Update custom file input label with selected filename
    document.getElementById('profile_photo').addEventListener('change', function(e) {
      var fileName = e.target.files[0].name;
      var label = e.target.nextElementSibling;
      label.innerHTML = fileName;
    });
  });
  </script>
</body>
</html>
