/* Portuguese initialisation for the jQuery UI date picker plugin. */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['pt'] = {
	closeText: 'Fechar',
	prevText: 'Anterior',
	nextText: 'Seguin<PERSON>',
	currentText: 'Ho<PERSON>',
	monthNames: ['Janeiro','Fevereiro','Mar<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>',
	'<PERSON><PERSON>','Agos<PERSON>','Setembro','Outubro','Novembro','Dezemb<PERSON>'],
	monthNamesShort: ['Jan','Fev','Mar','Abr','Mai','Jun',
	'Jul','A<PERSON>','Set','Out','Nov','Dez'],
	dayNames: ['<PERSON>','<PERSON>-feira','<PERSON><PERSON><PERSON>-feira','<PERSON><PERSON><PERSON>-feira','<PERSON><PERSON><PERSON>-f<PERSON>','<PERSON><PERSON>-f<PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
	dayNamesShort: ['Dom','Seg','Ter','Qua','Qui','Sex','Sáb'],
	dayNamesMin: ['Dom','Seg','Ter','Qua','Qui','Sex','Sáb'],
	weekHeader: 'Sem',
	dateFormat: 'dd/mm/yy',
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['pt']);

return datepicker.regional['pt'];

}));
