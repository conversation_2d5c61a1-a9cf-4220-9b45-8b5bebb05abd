{"name": "components/jqueryui", "type": "component", "description": "jQuery UI is a curated set of user interface interactions, effects, widgets, and themes built on top of the jQuery JavaScript Library. Whether you're building highly interactive web applications or you just need to add a date picker to a form control, jQuery UI is the perfect choice.", "license": "MIT", "require": {"components/jquery": ">=1.6"}, "authors": [{"name": "jQuery UI Team", "homepage": "http://jqueryui.com/about"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://scottgonzalez.com"}, {"name": "<PERSON><PERSON>", "email": "joern.z<PERSON><PERSON><PERSON>@gmail.com", "homepage": "http://bassistance.de"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://krisborchers.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gnarf.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://mike.sherov.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://tjvantoll.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.felixnagel.com"}], "extra": {"component": {"name": "jquery-ui", "scripts": ["jquery-ui.js"], "files": ["ui/**", "themes/**", "jquery-ui.min.js"], "shim": {"deps": ["j<PERSON>y"], "exports": "j<PERSON><PERSON><PERSON>"}}}}