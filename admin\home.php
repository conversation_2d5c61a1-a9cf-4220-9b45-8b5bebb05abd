<?php 
require_once('includes/script.php');  
require_once('session/Login.php'); 

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

if (!isset($_SESSION['official_username']) || !isset($_SESSION['official_password']) || !isset($_SESSION['official_id'])) {
    header("location:index.php?utm_campaign=expired");
}

$connection = $model->TemporaryConnection();

$query = $model->GetAdministrator($_SESSION['official_username'], $_SESSION['official_password']);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];
$type = $admin['type'];

// Get employee count from ERP API
function fetchApiEmployees($page = 1, $per_page = 50) {
    $apiUrl = "http://***************:8080/api/HRMAPI/get_employee?page=$page&pageSize=$per_page";

    $data = array();
    $total = 0;

    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);

        if(curl_errno($ch)) {
            error_log("cURL Error: " . curl_error($ch));
        } else {
            $decoded = json_decode($response, true);

            if(json_last_error() === JSON_ERROR_NONE && isset($decoded['data'])) {
                $data = $decoded['data'];
                $total = isset($decoded['total']) ? (int)$decoded['total'] : count($data);
            }
        }
        curl_close($ch);
    }

    return array('data' => $data, 'total' => $total);
}

$api_response = fetchApiEmployees(1, 1);
$rowEmp = array('countEmp' => $api_response['total']);

$to = date('Y-m-d');
$from = date('Y-m-d', strtotime('-6 day', strtotime($to)));

if (isset($_GET['range'])) {
    $range = $_GET['range'];
    $ex = explode('-', $range);
    $from = date('Y-m-d', strtotime($ex[0]));
    $to = date('Y-m-d', strtotime($ex[1]));
}

if ($type == "Timekeeper") {
    header("location:attendance.php?filter=$to");
}
?>

<!doctype html>
<html lang="en" dir="ltr">
<head>
    <title>Profiling and Payroll Management System</title>
</head>
<body class="">
    <div class="page" id="app">
        <div class="page-main">
            <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
              <div class="container">
                <div class="row align-items-center">
                  <div class="d-flex">
                    <?php require_once('includes/header.php') ?>
                  </div>
                  <div class="col-lg order-lg-first">
                    <?php require_once('includes/subheader.php') ?> 
                  </div>
                </div>
              </div>
            </div>
            <div class="my-3 my-md-5">
                <div class="container">
                    <div class="page-header mb-4">
                        <div class="row align-items-center">
                            <div class="col">
                                <h1 class="page-title text-gradient">Payroll Dashboard</h1>
                                <p class="page-subtitle">Comprehensive payroll management and employee tracking system</p>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group">
                                    <button class="btn btn-primary">
                                        <i class="fe fe-download mr-2"></i>Export Report
                                    </button>
                                    <button class="btn btn-outline-primary">
                                        <i class="fe fe-refresh-cw mr-2"></i>Refresh
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Statistics Overview -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stat-card fade-in-up">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-gradient-primary text-white rounded-circle p-3 mr-3">
                                        <i class="fe fe-users" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <div class="stat-number"><?php echo number_format($rowEmp['countEmp']); ?></div>
                                        <div class="stat-label">Total Employees</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card fade-in-up" style="animation-delay: 0.1s;">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-success text-white rounded-circle p-3 mr-3">
                                        <i class="fe fe-check-circle" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <div class="stat-number">
                                            <?php
                                            $today = date('Y-m-d');
                                            $presentQuery = "SELECT COUNT(*) as present FROM attendance WHERE date = '$today' AND status_morning = 1";
                                            $presentResult = mysqli_query($connection, $presentQuery);
                                            $presentCount = mysqli_fetch_assoc($presentResult)['present'];
                                            echo number_format($presentCount);
                                            ?>
                                        </div>
                                        <div class="stat-label">Present Today</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card fade-in-up" style="animation-delay: 0.2s;">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-warning text-white rounded-circle p-3 mr-3">
                                        <i class="fe fe-clock" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <div class="stat-number">
                                            <?php
                                            $pendingQuery = "SELECT COUNT(*) as pending FROM attendance WHERE date = '$today' AND (time_out_morning IS NULL OR time_out_afternoon IS NULL)";
                                            $pendingResult = mysqli_query($connection, $pendingQuery);
                                            $pendingCount = mysqli_fetch_assoc($pendingResult)['pending'];
                                            echo number_format($pendingCount);
                                            ?>
                                        </div>
                                        <div class="stat-label">Pending Checkout</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card fade-in-up" style="animation-delay: 0.3s;">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-danger text-white rounded-circle p-3 mr-3">
                                        <i class="fe fe-x-circle" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <div class="stat-number">
                                            <?php
                                            $absentCount = $rowEmp['countEmp'] - $presentCount;
                                            echo number_format($absentCount);
                                            ?>
                                        </div>
                                        <div class="stat-label">Absent Today</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row row-cards">
                        <div style="padding-left: 12px; padding-bottom: 25px;" class="">
                            <button type="button" class="btn btn-secondary" data-toggle="modal" data-target="#modal-filter-date">
                                <i class="fe fe-filter mr-2"></i> Filter Payroll
                            </button>
                        </div>    
                        <div style="padding-left: 12px; padding-bottom: 25px;" class="">
                            <button type="button" class="btn btn-secondary" onclick="printPage()">
                                <i class="fe fe-list mr-2"></i> Print Payroll
                            </button>
                        </div>
                        <div style="padding-left: 12px; padding-bottom: 25px;" class="">
                            <button type="button" class="btn btn-primary" onclick="exportToCSV()">
                                <i class="fe fe-download mr-2"></i> Export to CSV
                            </button>
                        </div>  

                        <?php require_once('modals/modal_filter_date.php') ?>

                        <div class="col-12" id="printDataHolder">
                            <div class="card">
                                <div class="card-header py-3">
                                    <h3 class="card-title">Payroll From <b><?php echo date('M d, Y', strtotime($from)) ?> (<?php echo date('D', strtotime($from)) ?>) </b>To <b><?php echo date('M d, Y', strtotime($to)) ?> (<?php echo date('D', strtotime($to)) ?>)</b></h3>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover" id="dataTable" width="100%" cellspacing="0">
                                            <thead>
                                                <tr>
                                                    <th></th> <!-- For expand/collapse button -->
                                                    <th>No.</th>
                                                    <th>EID</th>
                                                    <th>Employee Name</th>
                                                    <th>Position</th>
                                                    <th>Department</th>
                                                    <th>Site Location</th>
                                                    <th>Rate Per Hour</th>
                                                    <th>Total Hours</th>
                                                    <th>Payslip</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php 
                                                $sql = "SELECT e.id, e.employee_id, e.fullname, e.site_location, p.description AS position, p.rate, 
                                                        SUM(a.num_hr_morning + a.num_hr_afternoon) AS total_hours
                                                        FROM employees e
                                                        LEFT JOIN attendance a ON e.id = a.employee_id
                                                        LEFT JOIN position p ON e.position_id = p.id
                                                        WHERE a.date BETWEEN '$from' AND '$to'
                                                        GROUP BY e.id
                                                        ORDER BY e.fullname ASC";

                                                $sqlPayroll = mysqli_query($connection, $sql);
                                                $numbers = 0;

                                                while ($row = mysqli_fetch_assoc($sqlPayroll)) {
                                                    $numbers++;
                                                    $employee_id = $row['id'];
                                                    $total_hours = $row['total_hours'] ? round($row['total_hours'], 2) : 0;
                                                    ?>
                                                    <tr>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-outline-primary toggle-details" data-employee-id="<?php echo $employee_id; ?>">
                                                                <i class="fe fe-plus"></i>
                                                            </button>
                                                        </td>
                                                        <td><?php echo $numbers ?></td>
                                                        <td><a href="view.php?id=<?php echo $employee_id ?>"><?php echo $row['employee_id'] ?></a></td>
                                                        <td><?php echo htmlspecialchars($row['fullname']) ?></td>
                                                        <td><?php echo htmlspecialchars($row['position']) ?></td>
                                                        <td>N/A</td>
                                                        <td><?php echo htmlspecialchars($row['site_location'] ?: 'N/A') ?></td>
                                                        <td><?php echo number_format($row['rate'], 2) ?></td>
                                                        <td><?php echo $total_hours ?> Hours</td>
                                                        <td>
                                                            <div class="dropdown">
                                                                <a href="javascript:void(0)" data-toggle="dropdown" class="icon"><i class="fe fe-menu"></i></a>
                                                                <div class="dropdown-menu dropdown-menu-right">
                                                                    <a href="payslip.php?id=<?php echo $employee_id ?>" class="dropdown-item"><i class="dropdown-icon fe fe-eye"></i> View Payslip</a>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php } ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>           
                </div>
            </div>
            <?php require_once('includes/footer.php') ?>
        </div>   
    </div>   
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.22/js/dataTables.bootstrap4.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#dataTable').DataTable({
                "pageLength": 10,
                "ordering": true,
                "searching": true,
                "columnDefs": [
                    {
                        "targets": 0,
                        "orderable": false,
                        "searchable": false
                    }
                ]
            });

            // Function to format the details row
            function formatDetails(employeeId) {
                var dates = <?php 
                    $date = $from;
                    $dates = [];
                    while (strtotime($date) <= strtotime($to)) {
                        $dates[] = date('m/d', strtotime($date));
                        $date = date('Y-m-d', strtotime($date . ' +1 day'));
                    }
                    echo json_encode($dates);
                ?>;
                
                var header = dates.map(function(date) {
                    return '<th>' + date + '</th>';
                }).join('');
                
                return '<div class="p-3">' +
                       '<table class="table table-bordered mb-0">' +
                       '<thead><tr>' + header + '</tr></thead>' +
                       '<tbody><tr><td colspan="' + dates.length + '" class="text-center">Loading...</td></tr></tbody>' +
                       '</table></div>';
            }

            // Update the click handler for toggle buttons
            $('#dataTable').on('click', '.toggle-details', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                var tr = $(this).closest('tr');
                var row = table.row(tr);
                var employeeId = $(this).data('employee-id');
                var icon = $(this).find('i');
                
                if (row.child.isShown()) {
                    row.child.hide();
                    icon.removeClass('fe-minus').addClass('fe-plus');
                } else {
                    // Close all other rows
                    table.rows().every(function() {
                        if (this.child.isShown()) {
                            this.child.hide();
                            $(this.node()).find('.toggle-details i').removeClass('fe-minus').addClass('fe-plus');
                        }
                    });
                    
                    // Open this row
                    row.child(formatDetails(employeeId)).show();
                    icon.removeClass('fe-plus').addClass('fe-minus');
                    
                    // Load attendance data
                    $.ajax({
                        url: 'get_attendance.php',
                        type: 'POST',
                        data: {
                            employee_id: employeeId,
                            from: '<?php echo $from; ?>',
                            to: '<?php echo $to; ?>'
                        },
                        success: function(response) {
                            try {
                                var data = JSON.parse(response);
                                var tbody = row.child().find('tbody');
                                var dates = data.dates;
                                
                                // Add status row
                                var statusRow = '<tr>';
                                dates.forEach(function(date) {
                                    var status = data.attendance[date] || { morning: null, afternoon: null };
                                    var morning = status.morning ? 'A' : 'P';
                                    var afternoon = status.afternoon ? 'A' : 'P';
                                    statusRow += '<td>' + morning + '/' + afternoon + '</td>';
                                });
                                statusRow += '</tr>';
                                
                                // Add hours row
                                var hoursRow = '<tr>';
                                dates.forEach(function(date) {
                                    var hours = data.attendance[date] ? 
                                        (parseFloat(data.attendance[date].morning_hours) + parseFloat(data.attendance[date].afternoon_hours)).toFixed(2) : 
                                        '0.00';
                                    hoursRow += '<td>' + hours + ' hrs</td>';
                                });
                                hoursRow += '</tr>';
                                
                                tbody.html(statusRow + hoursRow);
                            } catch (e) {
                                console.error('Error parsing attendance data:', e);
                                row.child().find('tbody').html('<tr><td colspan="<?php echo count($dates); ?>" class="text-center text-danger">Error loading data</td></tr>');
                            }
                        },
                        error: function() {
                            row.child().find('tbody').html('<tr><td colspan="<?php echo count($dates); ?>" class="text-center text-danger">Error loading data</td></tr>');
                        }
                    });
                }
            });
        });

        function printPage() {
            var divElements = document.getElementById('printDataHolder').innerHTML;
            var oldPage = document.body.innerHTML;
            document.body.innerHTML = "<link rel='stylesheet' href='css/common.css' type='text/css' /><body class='bodytext'><div class='padding'><b style='font-size: 16px;'><p class=''>Payroll generated on <?php echo date('m/d/Y') ?> <?php echo date('G:i A') ?> by <?php echo $firstname ?> <?php echo $lastname ?></p></b></div>" + divElements + "</body>";
            window.print();
            document.body.innerHTML = oldPage;
        }

        function exportToCSV() {
            let csvContent = "data:text/csv;charset=utf-8,";
            // Main table headers
            let headers = ["No.","EID","Employee Name","Position","Department","Site Location","Rate Per Hour","Total Hours"];
            <?php
            // Add date headers for detailed data
            $dateHeaders = [];
            $date = $from;
            while (strtotime($date) <= strtotime($to)) {
                $dateHeaders[] = date('m/d', strtotime($date));
                $date = date('Y-m-d', strtotime($date . ' +1 day'));
            }
            ?>
            const dateHeaders = <?php echo json_encode($dateHeaders); ?>;
            headers = headers.concat(dateHeaders.map(d => `Attendance ${d}`), dateHeaders.map(d => `Hours ${d}`));
            csvContent += headers.join(",") + "\r\n";

            <?php
            // Re-run the query to generate CSV data
            $sqlPayroll = mysqli_query($connection, $sql);
            $numbers = 0;
            while ($row = mysqli_fetch_assoc($sqlPayroll)) {
                $numbers++;
                $employee_id = $row['id'];
                $total_hours = $row['total_hours'] ? round($row['total_hours'], 2) : 0;
                
                // Fetch attendance data again
                $attSql = "SELECT date, status_morning, status_afternoon, num_hr_morning, num_hr_afternoon 
                           FROM attendance 
                           WHERE employee_id = '$employee_id' AND date BETWEEN '$from' AND '$to'
                           ORDER BY date";
                $attQuery = mysqli_query($connection, $attSql);
                $attendance = [];
                while ($attRow = mysqli_fetch_assoc($attQuery)) {
                    $attendance[$attRow['date']] = $attRow;
                }
                
                $attendanceData = [];
                $hoursData = [];
                foreach ($dateHeaders as $d) {
                    $formattedDate = date('Y-m-d', strtotime($d));
                    if (isset($attendance[$formattedDate])) {
                        $morning = $attendance[$formattedDate]['status_morning'] ? 'A' : 'P';
                        $afternoon = $attendance[$formattedDate]['status_afternoon'] ? 'A' : 'P';
                        $attendanceData[] = "$morning/$afternoon";
                        $total = round($attendance[$formattedDate]['num_hr_morning'] + $attendance[$formattedDate]['num_hr_afternoon'], 2);
                        $hoursData[] = $total;
                    } else {
                        $attendanceData[] = '-/-';
                        $hoursData[] = '0';
                    }
                }
                
                // Escape values for CSV
                $rowData = [
                    $numbers,
                    '"' . str_replace('"', '""', $row['employee_id']) . '"',
                    '"' . str_replace('"', '""', $row['fullname']) . '"',
                    '"' . str_replace('"', '""', $row['position']) . '"',
                    '"N/A"',
                    '"' . str_replace('"', '""', ($row['site_location'] ?: 'N/A')) . '"',
                    number_format($row['rate'], 2),
                    $total_hours
                ];
                $rowData = array_merge($rowData, $attendanceData, $hoursData);
                ?>
                csvContent += <?php echo json_encode(implode(',', $rowData)); ?> + "\r\n";
                <?php
            }
            ?>

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "payroll_<?php echo $from ?>_to_<?php echo $to ?>.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>